package com.near_reality.api.model

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlin.time.Duration

@Serializable
sealed class Sanction(val level: SanctionLevel){

    abstract val id: Int?
    abstract val time: Instant
    abstract val type: SanctionType
    abstract val reporter: String?
    abstract val reason: String?
    abstract val duration: Duration?

    val expirationTime get() = duration?.let { time.plus(it) }
    val expirationDuration get() = expirationTime?.let { it - Clock.System.now() }
    fun isExpired() = expirationTime?.let { it < Clock.System.now() } ?: false

    fun format() = "${type.formattedName} ${level.format()} ${if (reporter != null) "by $reporter" else ""} ${if (reason != null) "for $reason" else ""} ${if (duration != null) "for ${duration!!.inWholeMinutes} minutes" else ""}"
}

@Serializable
data class IPSanction(
    val ip: String,
    override val id: Int? = null,
    override val time: Instant = Clock.System.now(),
    @SerialName("sanctionType")
    override val type: SanctionType,
    override val reporter: String?,
    override val reason: String?,
    override val duration: Duration?,
) : Sanction(SanctionLevel.IP)

@Serializable
data class AccountSanction(
    val offender: String,
    override val id: Int? = null,
    override val time: Instant = Clock.System.now(),
    @SerialName("sanctionType")
    override val type: SanctionType,
    override val reporter: String?,
    override val reason: String?,
    override val duration: Duration?,
) : Sanction(SanctionLevel.ACCOUNT)

@Serializable
data class UUIDSanction(
    val uuid: ByteArray,
    override val id: Int? = null,
    override val time: Instant = Clock.System.now(),
    @SerialName("sanctionType")
    override val type: SanctionType,
    override val reporter: String?,
    override val reason: String?,
    override val duration: Duration?,
) : Sanction(SanctionLevel.UUID) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as UUIDSanction

        if (!uuid.contentEquals(other.uuid)) return false
        if (id != other.id) return false
        if (time != other.time) return false
        if (type != other.type) return false
        if (reporter != other.reporter) return false
        if (reason != other.reason) return false
        if (duration != other.duration) return false

        return true
    }

    override fun hashCode(): Int {
        var result = uuid.contentHashCode()
        result = 31 * result + (id ?: 0)
        result = 31 * result + time.hashCode()
        result = 31 * result + type.hashCode()
        result = 31 * result + (reporter?.hashCode() ?: 0)
        result = 31 * result + (reason?.hashCode() ?: 0)
        result = 31 * result + (duration?.hashCode() ?: 0)
        return result
    }
}

@Serializable
enum class SanctionType(val actionName: String, val privilege: ApiPrivilege){
    MUTE("muted", ApiPrivilege.SUPPORT),
    YELL_MUTE("yell muted", ApiPrivilege.SUPPORT),
    BAN("banned", ApiPrivilege.MODERATOR);
    val formattedName get() = actionName.lowercase().replace("_", " ")
}

@Serializable
enum class SanctionLevel(val privilege: ApiPrivilege) {

    /**
     * Represents a ban based on the name of a player.
     */
    ACCOUNT(ApiPrivilege.SUPPORT),

    /**
     * Represents a ban based on the IP address of a player.
     */
    IP(ApiPrivilege.MODERATOR),

    /**
     * Represents a ban based on the random.dat file generated by the client.
     */
    UUID(ApiPrivilege.ADMINISTRATOR);

    fun format() = name.lowercase().replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }
}
