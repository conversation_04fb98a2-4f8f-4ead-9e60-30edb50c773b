package com.near_reality.api.model

import kotlinx.datetime.LocalDateTime
import kotlinx.serialization.Serializable

@Serializable
data class User(
    val id: Long,
    val name: String,
    val passwordHash: String? = null,
    val passwordAtRisk: Bo<PERSON>an,
    val email: String,
    val gameMode: ApiGameMode,
    val memberRank: ApiMemberRank,
    val privilege: ApiPrivilege,
    val twoFactorEnabled: Boolean,
    val twoFactorSecret: String? = null,
    var totalSpent: Int,
    var storeCredits: Int,
    val totalVotes: Int,
    val joinDate: LocalDateTime,
    val sanctions: List<Sanction>
) {
    fun stripCredentials() = copy(passwordHash = null, twoFactorSecret = null)
}
@Serializable
enum class Bond(val id: Int, val credits: Int, val amount: Int) {
    DONATOR_BOND_10(32070, 100, 10),
    DONATOR_BOND_25(32071, 250, 25),
    DONATOR_BOND_50(32072, 500, 50),
    DONATOR_BOND_100(32073, 1000, 100);

    val fancyName get() = name.lowercase().replaceFirstChar { it.uppercase() }.replace("_", " ")

    fun isValid(): Boolean {
        return this.amount > 0 && this.credits > 0
    }

    companion object {
        // Retrieves the Bond by its ID
        operator fun get(id: Int): Bond? = entries.find { it.id == id }
        fun item(id: Int): Bond? = get(id)
    }
}




@Serializable
enum class ApiGameMode {
    REGULAR,
    STANDARD_IRON_MAN,
    ULTIMATE_IRON_MAN,
    HARDCORE_IRON_MAN,
    GROUP_IRON_MAN,
    GROUP_HARDCORE_IRON_MAN;

    val isIronMan: Boolean
        get() = this != REGULAR

    companion object {
        fun forId(id: Number) = when(id.toInt()) {
            1 -> REGULAR
            2 -> STANDARD_IRON_MAN
            3 -> ULTIMATE_IRON_MAN
            4 -> HARDCORE_IRON_MAN
            5 -> GROUP_IRON_MAN
            else -> error("Did not find ApiGameMode for id $id")
        }
    }
}

@Serializable
enum class ApiMemberRank(val formattedName: String, val requiredDonatedAmount: Int) {
    NONE("None", 0),
    REGULAR("Regular", 10),
    SUPER("Super", 50),
    EXTREME("Extreme", 100),
    EPIC("Epic", 250),
    LEGENDARY("Legendary", 500),
    MYTHIC("Mythic", 750),
    DIVINE("Divine", 1000),

    /**
     * Amascut was a special rank added by Will that is only obtainable
     * for 2 members `speckle` and `gim`.
     */
    EXILED("Amascut", 2000);

    companion object {
        val obtainableRanks = entries.filter { it != EXILED }
        fun findForUserWithAmountSpent(username: String? = null, totalSpent: Int) =
//            if (username == "saturated" || username == "gim")
//                EXILED
//            else
            obtainableRanks
                .filter { it.requiredDonatedAmount <= totalSpent }
                .maxBy { it.requiredDonatedAmount }
    }
}

@Serializable
enum class ApiPrivilege(val requires2FA: Boolean = false) {
    PLAYER(false),
    YOUTUBER(false),
    MEMBER(false),
    FORUM_MODERATOR(false),
    SUPPORT(false),
    MODERATOR(false),
    SENIOR_MODERATOR(false),
    ADMINISTRATOR(false),
    DEVELOPER(false),
    HIDDEN_ADMINISTRATOR(false),
    TRUE_DEVELOPER(false)
}

