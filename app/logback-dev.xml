<!-- Configuration for developers, prints all logs to console -->
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <target>System.out</target>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="io.netty" level="WARN"/>
    <logger name="io.mockk" level="WARN"/>

    <root level="debug">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
