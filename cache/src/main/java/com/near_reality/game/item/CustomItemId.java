package com.near_reality.game.item;

import com.near_reality.cache_tool.packing.custom.CustomItemPacker;

/**
 * !!! THIS FILE IS AUTO-GENERATED, DO NOT EDIT !!!
 *
 * These IDs were custom-generated by {@link CustomItemPacker}.
 *
 * <AUTHOR>
 */
public class CustomItemId
{
	public static final int CANNONBALL_PACK = 31301;
	public static final int GRANITE_CANNONBALL_PACK = 31314;
	public static final int KORASI = 32001;
	public static final int ANCIENT_EYE = 32002;
	public static final int ANCIENT_BOOK_32004 = 32004;
	public static final int ARMADYL_SOUL_CRYSTAL = 32006;
	public static final int ARMADYL_BOW = 32008;
	public static final int BANDOS_SOUL_CRYSTAL = 32010;
	public static final int BANDOS_BOW = 32012;
	public static final int SARADOMIN_SOUL_CRYSTAL = 32014;
	public static final int SARADOMIN_BOW = 32016;
	public static final int ZAMORAK_SOUL_CRYSTAL = 32018;
	public static final int ZAMORAK_BOW = 32020;
	public static final int DRAGON_KITE = 32022;
	public static final int ANCIENT_MEDALLION_32024 = 32024;
	public static final int IMBUED_ANCIENT_CAPE = 32026;
	public static final int IMBUED_ARMADYL_CAPE = 32028;
	public static final int IMBUED_BANDOS_CAPE = 32030;
	public static final int IMBUED_SEREN_CAPE = 32032;
	public static final int POLYPORE_SPORES = 32038;
	public static final int POLYPORE_STAFF_DEG = 32040;
	public static final int POLYPORE_STAFF = 32042;
	public static final int BRONZE_KEY = 32044;
	public static final int SILVER_KEY = 32046;
	public static final int GOLD_KEY = 32048;
	public static final int PLATINUM_KEY = 32050;
	public static final int DIAMOND_KEY = 32052;
	public static final int NR_TABLET = 32056;
	public static final int DEATH_CAPE = 32058;
	public static final int LIME_WHIP = 32060;
	public static final int LIME_WHIP_SPECIAL = 32062;
	public static final int LAVA_WHIP = 32064;
	public static final int PINK_PARTYHAT = 32066;
	public static final int ORANGE_PARTYHAT = 32068;
	public static final int DONATOR_BOND_10 = 32070;
	public static final int DONATOR_BOND_25 = 32071;
	public static final int DONATOR_BOND_50 = 32072;
	public static final int DONATOR_BOND_100 = 32073;
	public static final int GAUNTLET_SLAYER_HELM = 32074;
	public static final int CORRUPTED_GAUNTLET_SLAYER_HELM = 32076;
	public static final int NEAR_REALITY_PARTY_HAT = 32078;
	public static final int OSNR_MYSTERY_BOX = 32080;
	public static final int PET_XAMPHUR = 32083;
	public static final int GANODERMIC_RUNT = 32100;
	public static final int BLUE_ANKOU_SOCKS = 32102;
	public static final int BLUE_ANKOU_GLOVES = 32104;
	public static final int BLUE_ANKOUS_LEGGINGS = 32106;
	public static final int BLUE_ANKOU_MASK = 32108;
	public static final int BLUE_ANKOU_TOP = 32110;
	public static final int GREEN_ANKOU_SOCKS = 32112;
	public static final int GREEN_ANKOU_GLOVES = 32114;
	public static final int GREEN_ANKOUS_LEGGINGS = 32116;
	public static final int GREEN_ANKOU_MASK = 32118;
	public static final int GREEN_ANKOU_TOP = 32120;
	public static final int GOLD_ANKOU_SOCKS = 32122;
	public static final int GOLD_ANKOU_GLOVES = 32124;
	public static final int GOLD_ANKOUS_LEGGINGS = 32126;
	public static final int GOLD_ANKOU_MASK = 32128;
	public static final int GOLD_ANKOU_TOP = 32130;
	public static final int WHITE_ANKOU_SOCKS = 32132;
	public static final int WHITE_ANKOU_GLOVES = 32134;
	public static final int WHITE_ANKOUS_LEGGINGS = 32136;
	public static final int WHITE_ANKOU_MASK = 32138;
	public static final int WHITE_ANKOU_TOP = 32140;
	public static final int BANDOS_CHESTPLATE_OR = 32142;
	public static final int BANDOS_TASSETS_OR = 32144;
	public static final int BANDOS_ORNAMENT_KIT = 32146;
	public static final int NR_VOTE_SHARD = 32148;
	public static final int LARRANS_KEY_BOOSTER = 32149;
	public static final int GANODERMIC_BOOSTER = 32150;
	public static final int SLAYER_BOOSTER = 32151;
	public static final int PET_BOOSTER = 32152;
	public static final int GAUNTLET_BOOSTER = 32153;
	public static final int TOB_BOOSTER = 32156;
	public static final int SLAYER_TASK_RESET_SCROLL = 32158;
	public static final int SLAYER_TASK_PICKER_SCROLL = 32157;
	public static final int COSMETIC_MYSTERY_BOX = 32163;
	public static final int NEX_BOOSTER = 32167;
	public static final int VESTAS_HELM = 32169;
	public static final int HOLY_GREAT_WARHAMMER = 32172;
	public static final int HOLY_GREAT_LANCE = 32175;
	public static final int DEGRADED_ESSENCE = 32178;
	public static final int BLOOD_TENTACLES = 32180;
	public static final int ANGELIC_ARTIFACT = 32182;
	public static final int PVP_MYSTERY_BOX = 32203;
	public static final int SLAYER_PLATESKIRT = 32229;
	public static final int REGAL_MYSTERY_BOX = 32231;
	public static final int CARROT_SPEAR = 32354;
	public static final int CARROT_CROWN = 32355;
	public static final int BROKEN_EGG_SHELLS = 32356;
	public static final int EASTER_MYSTERY_BOX = 32357;
	public static final int EASTER_CARDS = 32358;
	public static final int ANCIENT_MAX_CAPE = 32359;
	public static final int ARMADYL_MAX_CAPE = 32360;
	public static final int BANDOS_MAX_CAPE = 32361;
	public static final int SEREN_MAX_CAPE = 32362;
	public static final int REMNANT_POINT_VOUCHER_1 = 32363;
	public static final int REMNANT_POINT_VOUCHER_5 = 32364;
	public static final int REMNANT_POINT_VOUCHER_10 = 32365;
	public static final int WILDY_SUPPLY_SACK = 32366;
	public static final int WILDY_SUPPLY_CRATE = 32367;
	public static final int PVP_TOURNEY_MYSTERY_BOX = 32368;
	public static final int ORB_OF_XERIC = 32369;
	public static final int ORB_OF_BLOOD = 32370;
	public static final int ORB_OF_AMASCUT = 32371;
	public static final int TEMPERED_CRYSTAL = 32373;
	public static final int TEMPERED_BOOTS = 32374;
	public static final int TEMPERED_BOOTS_PLACEHOLDER = 32375;
	public static final int GUTHIX_SOUL_CRYSTAL = 32376;
	public static final int AMULET_OF_THE_FIRST_AGE = 32377;
	public static final int BLUE_TWISTED_BOW = 32378;
	public static final int BLUE_TWISTED_BOW_PAINT = 32381;
	public static final int DIVINE_KIT = 32382;
	public static final int PURPLE_TWISTED_BOW = 32383;
	public static final int PURPLE_TWISTED_BOW_PAINT = 32386;
	public static final int RED_TWISTED_BOW = 32387;
	public static final int RED_TWISTED_BOW_PAINT = 32390;
	public static final int WHITE_TWISTED_BOW = 32391;
	public static final int WHITE_TWISTED_BOW_PAINT = 32394;
	public static final int BLACK_ANKOU_SOCKS = 32395;
	public static final int BLACK_ANKOU_GLOVES = 32397;
	public static final int BLACK_ANKOUS_LEGGINGS = 32399;
	public static final int BLACK_ANKOU_MASK = 32401;
	public static final int BLACK_ANKOU_TOP = 32403;
	public static final int PURPLE_ANKOU_SOCKS = 32405;
	public static final int PURPLE_ANKOU_GLOVES = 32407;
	public static final int PURPLE_ANKOUS_LEGGINGS = 32409;
	public static final int PURPLE_ANKOU_MASK = 32411;
	public static final int PURPLE_ANKOU_TOP = 32413;
	public static final int ELYSIAN_SPIRIT_SHIELD_OR = 32421;
    public static final int PVM_MYSTERY_BOX = 32423;
    public static final int WORLD_BOOST_TOKEN = 32424;
    public static final int SIR_ELDRICS_BOOST_SCROLL = 32425;
	public static final int GILDED_BOXING_GLOVES = 32451;
	public static final int POTENT_ARDER_MUSCA_POISON = 32452;
	public static final int MAGUS_RING_A = 32453;

	public static final int BLACK_GREEN_ANKOU_SOCKS = 32455;
	public static final int BLACK_GREEN_ANKOU_GLOVES = 32458; // 32455 + 3
	public static final int BLACK_GREEN_ANKOU_LEGGINGS = 32461; // 32458 + 3
	public static final int BLACK_GREEN_ANKOU_MASK = 32464; // 32461 + 3
	public static final int BLACK_GREEN_ANKOU_TOP = 32467; // 32464 + 3
	public static final int BLACK_PURPLE_ANKOU_SOCKS = 32470;
	public static final int BLACK_PURPLE_ANKOU_GLOVES = 32473; // 32470 + 3
	public static final int BLACK_PURPLE_ANKOU_LEGGINGS = 32476; // 32473 + 3
	public static final int BLACK_PURPLE_ANKOU_MASK = 32479; // 32476 + 3
	public static final int BLACK_PURPLE_ANKOU_TOP = 32482; // 32479 + 3
	public static final int BLACK_WHITE_ANKOU_SOCKS = 32485; // 32482 + 3
	public static final int BLACK_WHITE_ANKOU_GLOVES = 32488; // 32485 + 3
	public static final int BLACK_WHITE_ANKOU_LEGGINGS = 32491; // 32488 + 3
	public static final int BLACK_WHITE_ANKOU_MASK = 32494; // 32491 + 3
	public static final int BLACK_WHITE_ANKOU_TOP = 32497; // 32494 + 3
	public static final int BLACK_YELLOW_ANKOU_SOCKS = 32500; // 32497 + 3
	public static final int BLACK_YELLOW_ANKOU_GLOVES = 32503; // 32500 + 3
	public static final int BLACK_YELLOW_ANKOU_LEGGINGS = 32506; // 32503 + 3
	public static final int BLACK_YELLOW_ANKOU_MASK = 32509; // 32506 + 3
	public static final int BLACK_YELLOW_ANKOU_TOP = 32512; // 32509 + 3
	public static final int GILDED_ANKOU_SOCKS = 32515; // 32512 + 3
	public static final int GILDED_ANKOU_GLOVES = 32518; // 32515 + 3
	public static final int GILDED_ANKOU_LEGGINGS = 32521; // 32518 + 3
	public static final int GILDED_ANKOU_MASK = 32524; // 32521 + 3
	public static final int GILDED_ANKOU_TOP = 32527; // 32524 + 3
	public static final int GREEN_BLACK_ANKOU_SOCKS = 32530; // 32527 + 3
	public static final int GREEN_BLACK_ANKOU_GLOVES = 32533; // 32530 + 3
	public static final int GREEN_BLACK_ANKOU_LEGGINGS = 32536; // 32533 + 3
	public static final int GREEN_BLACK_ANKOU_MASK = 32539; // 32536 + 3
	public static final int GREEN_BLACK_ANKOU_TOP = 32542; // 32539 + 3
	public static final int PURPLE_BLACK_ANKOU_SOCKS = 32545; // 32542 + 3
	public static final int PURPLE_BLACK_ANKOU_GLOVES = 32548; // 32545 + 3
	public static final int PURPLE_BLACK_ANKOU_LEGGINGS = 32551; // 32548 + 3
	public static final int PURPLE_BLACK_ANKOU_MASK = 32554; // 32551 + 3
	public static final int PURPLE_BLACK_ANKOU_TOP = 32557; // 32554 + 3
	public static final int RED_BLACK_ANKOU_SOCKS = 32560; // 32557 + 3
	public static final int RED_BLACK_ANKOU_GLOVES = 32563; // 32560 + 3
	public static final int RED_BLACK_ANKOU_LEGGINGS = 32566; // 32563 + 3
	public static final int RED_BLACK_ANKOU_MASK = 32569; // 32566 + 3
	public static final int RED_BLACK_ANKOU_TOP = 32572; // 32569 + 3
	public static final int WHITE_BLUE_ANKOU_SOCKS = 32575; // 32572 + 3
	public static final int WHITE_BLUE_ANKOU_GLOVES = 32578; // 32575 + 3
	public static final int WHITE_BLUE_ANKOU_LEGGINGS = 32581; // 32578 + 3
	public static final int WHITE_BLUE_ANKOU_MASK = 32584; // 32581 + 3
	public static final int WHITE_BLUE_ANKOU_TOP = 32587; // 32584 + 3
	public static final int WHITE_PURPLE_ANKOU_SOCKS = 32590; // 32587 + 3
	public static final int WHITE_PURPLE_ANKOU_GLOVES = 32593; // 32590 + 3
	public static final int WHITE_PURPLE_ANKOU_LEGGINGS = 32596; // 32593 + 3
	public static final int WHITE_PURPLE_ANKOU_MASK = 32599; // 32596 + 3
	public static final int WHITE_PURPLE_ANKOU_TOP = 32602; // 32599 + 3

	public static final int FOUNDERS_CAPE = 33246;
	public static final int SMOLDERING_DEMON = 33250;

	public static final int BLACK_GREEN_ANKOU_SET = 2749;
	public static final int BLACK_PURPLE_ANKOU_SET = 2750;
	public static final int BLACK_WHITE_ANKOU_SET = 2751;
	public static final int BLACK_YELLOW_ANKOU_SET = 2752;
	public static final int GILDED_ANKOU_SET = 2753;
	public static final int GREEN_BLACK_ANKOU_SET = 2754;
	public static final int PURPLE_BLACK_ANKOU_SET = 2755;
	public static final int RED_BLACK_ANKOU_SET = 2756;
	public static final int WHITE_BLUE_ANKOU_SET = 2757;
	public static final int WHITE_PURPLE_ANKOU_SET = 2758;

}
