package com.zenyte.game.content.event.christmas2019;

import com.zenyte.game.world.entity.Location;

/**
 * <AUTHOR>
 * @since 14/12/2019
 */
public class ChristmasConstants {
    public static final String[] SNOW_IMP_NAMES = {"<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"};
    public static final String[] FROZEN_GUEST_ORDERS = {"twbacp", "twbpac", "tabcwp", "tpcbaw", "tawpbc", "tpacwb", "tpawcb", "twabpc", "tbpcwa", "tcbpwa"};
    public static final Location homeChristmasCupboardLocation = new Location(3094, 3485);
    public static final int CANDY_CANE = 30105;
    public static final int ICE_AMULET = 30113;
    public static final int CHRISTMAS_SCYTHE = 30115;
    public static final int PARTYGOER_EVENT_ADVERTISER = 15018;
    public static final int QUEEN_OF_SNOW_ID = 15035;
    public static final int SNOW_IMP_HELPER = 15014;
    public static final int PERSONAL_SNOW_IMP = 15013; // invisible 15009
    public static final int PARTYGOER_EVENT_INTRODUCER = 15024;
    public static final int SANTA_NPC_ID = 15037; // invisible 15036
    public static final int SCOURGE_NPC_ID = 15008; // 15006 invisible
    public static final int PAJAMAS_SCOURGE_NPC_ID = 15000;
    public static final int JACK_FROST_NPC_ID = 15040;
    public static final int LOUIS_THE_CAMEL_NPC_ID = 15043;
    public static final int TINY_THOM_NPC_ID = 15039;
    public static final int AMIK_VARZE_NPC_ID = 15034;
    public static final int COOK_NPC_ID = 15042;
    public static final int BENTNOZE_NPC_ID = 15047;
    public static final int WARTFACE_NPC_ID = 15045;
    public static final int PARTYGOER_NPC_ID = 15021;
    public static final int PARTYGOER_2_NPC_ID = 15027;
    public static final int PARTYGOER_3_NPC_ID = 15030;
    public static final int BETTY_NPC_ID = 15032;
    public static final int BEDSHEETS_ID = 30100;
    public static final int CHAINS_ID = 30101;
    public static final int GHOST_HOOD_ID = 30102;
    public static final int GHOST_TOP_ID = 30103;
    public static final int GHOST_BOTTOMS_ID = 30104;
    public static final int ICY_WATER_BUCKET = 30106;
    public static final int TURKEY_DRUMSTICK = 30107;
    public static final int ROAST_POTATOES = 30108;
    public static final int YULE_LOG = 30109;
    public static final int MULLED_WINE = 30110;
    public static final int SNOW_OBJECT = 46030;
    public static final int PRESENT_OBJECT = 46086;
    public static final int CHRISTMAS_CUPBOARD_ID = 46092;
    public static final int LAND_OF_SNOW_GATES = 46100;
    public static final int SCOURGE_GATES = 46101;
    public static final int CAGED_SANTA = 46181;
    public static final int CAGED_SANTA_2 = 46111;
    public static final int SCOURGE_STAIRS = 46102;
    public static final int ICY_WATER_FOUNTAIN = 46076;
    public static final int REWARD_PRESENT_VARBIT = 15010;
    public static final int WARTFACE_VAR = 15011;
    public static final int BENTNOZE_VAR = 15012;
    public static final int AMIK_VARZE_VAR = 15013;
    public static final int COOK_VAR = 15014;
    public static final int PARTYGOER_VAR = 15015;
    public static final int TINY_THOM_VAR = 15016;
    public static final int FREED_SANTA_VAR = 15006;
    public static final int SE_CUPBOARD_VAR = 15020;
    public static final int NE_CUPBOARD_VAR = 15022;
    public static final int NW_CUPBOARD_VAR = 15023;
    public static final int SW_CUPBOARD_VAR = 15021;
    public static final int SNOW_IMP_VAR1 = 15000;
    public static final int SNOW_IMP_VAR2 = 15001;
    public static final int SNOW_IMP_VAR3 = 15002;
    public static final int SNOW_IMP_VAR4 = 15003;
    public static final int EBENEZER_SCOURGE_VAR = 15004;
    public static final int FROZEN_NPCS_VAR = 15018;
    public static final String UNFROZEN_GUESTS_HASH_KEY = "A Christmas Warble unfrozen guests hash";

}
