package mgi.tools.dumpers;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import mgi.types.config.enums.EnumDefinitions;
import mgi.utilities.ByteBuffer;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.file.Files;
import java.util.Objects;

/**
 * Utility to dump/pack enums (whole directory or single file)
 */
public class EnumPackTool {

    // ----------------- CONFIGURE -----------------
    private static final String EXTENSION = "";
        // or "" if you don’t want any extension
        // Example: set to "" → imports become "123"
        // Example: set to ".enum.pack" → imports become "123.enum.pack"

    private static final String ENUM_PACK_DIR = "cache/assets/enum"; // where .enum.pack files live
    private static final String JSON_DIR = "dumps/enum";             // Directory for JSON files

    // Modes: choose exactly ONE
    private enum Mode {
        DUMP_ALL,        // pack -> json for all
        IMPORT_ALL,      // json -> pack for all
        IMPORT_SINGLE    // json -> pack for one file
    }
    private static final Mode MODE = Mode.IMPORT_SINGLE;

    // Single-file import (only used if MODE == IMPORT_SINGLE)
    private static final String FILE_NAME = "10038";
    // ----------------------------------------------

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    public static void main(String[] args) throws Exception {
        switch (MODE) {
            case DUMP_ALL -> dumpAll();
            case IMPORT_ALL -> importAll();
            case IMPORT_SINGLE -> importSingleFile();
        }
    }

//      Doesn't work because cache isnt loaded
//    private static void dumpSingle(int enumId, boolean asItems) throws Exception {
//
//        File outDir = new File(JSON_DIR);
//        if (!outDir.exists()) outDir.mkdirs();
//
//        EnumDefinitions map = EnumDefinitions.get(enumId);
//        if (map == null || map.getValues() == null) {
//            System.out.println("Enum " + enumId + " not found or empty.");
//            return;
//        }
//
//        // Build a map of values just like your command prints
//        Map<Object, Object> values = new LinkedHashMap<>();
//        map.getValues().forEach((k, v) -> {
//            if (asItems) {
//                String itemName = ItemDefinitions.get((int) v).getName();
//                values.put(k, itemName + " (" + v + ")");
//            } else {
//                values.put(k, v);
//            }
//        });
//
//        // Write JSON
//        File outFile = new File(outDir, enumId + ".json");
//        try (FileWriter writer = new FileWriter(outFile)) {
//            gson.toJson(values, writer);
//        }
//
//        System.out.println("✅ Dumped enum " + enumId + " → " + outFile.getAbsolutePath());
//    }

    // ----------------- DIRECTORY FUNCTIONS -----------------
    private static void dumpAll() throws Exception {
        File inDir = new File(ENUM_PACK_DIR);
        File outDir = new File(JSON_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : Objects.requireNonNull(inDir.listFiles(File::isFile))) {
            System.out.println("Dumping " + f.getName());
            byte[] raw = Files.readAllBytes(f.toPath());
            ByteBuffer buf = new ByteBuffer(raw);

            EnumDefinitions defs = new EnumDefinitions();
            defs.decode(buf);

            // Strip any extension (if present)
            String name = f.getName();
            int dotIndex = name.lastIndexOf('.');
            if (dotIndex > 0) {
                name = name.substring(0, dotIndex); // remove extension
            }

            File outFile = new File(outDir, name + ".json");

            try (FileWriter writer = new FileWriter(outFile)) {
                gson.toJson(defs, writer);
            }
        }
        System.out.println("✅ Dump complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    private static void importAll() throws Exception {
        File inDir = new File(JSON_DIR);
        File outDir = new File(ENUM_PACK_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : Objects.requireNonNull(inDir.listFiles((dir, name) -> name.endsWith(".json")))) {
            System.out.println("Importing " + f.getName());
            EnumDefinitions defs;
            try (FileReader reader = new FileReader(f)) {
                defs = gson.fromJson(reader, EnumDefinitions.class);
            }

            ByteBuffer buf = defs.encode();
            byte[] data = buf.toArray(0, buf.getPosition());

            // strip ".json"
            String name = f.getName();
            int dotIndex = name.lastIndexOf('.');
            if (dotIndex > 0) {
                name = name.substring(0, dotIndex);
            }

            // add extension only if EXTENSION is set
            String outName = name + (EXTENSION.isEmpty() ? "" : EXTENSION);

            File outFile = new File(outDir, outName);
            Files.write(outFile.toPath(), data);
        }
        System.out.println("✅ Import complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    // ----------------- SINGLE FILE FUNCTION -----------------
    private static void importSingleFile() throws Exception {
        // Input is always JSON
        File inFile = new File(JSON_DIR, FILE_NAME + ".json");

        // Output respects EXTENSION constant
        String outName = FILE_NAME + (EXTENSION.isEmpty() ? "" : EXTENSION);
        File outFile = new File(ENUM_PACK_DIR, outName);

        System.out.println("Importing single file: " + inFile);

        EnumDefinitions defs;
        try (FileReader reader = new FileReader(inFile)) {
            defs = gson.fromJson(reader, EnumDefinitions.class);
        }

        ByteBuffer buf = defs.encode();
        byte[] data = buf.toArray(0, buf.getPosition());

        Files.write(outFile.toPath(), data);

        System.out.println("✅ Single file written to: " + outFile);
    }
}