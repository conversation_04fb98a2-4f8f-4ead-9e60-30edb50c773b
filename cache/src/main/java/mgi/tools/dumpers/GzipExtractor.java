package mgi.tools.dumpers;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.*;
import java.util.Objects;

/*
* <AUTHOR> - Reworked by <PERSON> (Discord:imslickk)
 */

public class GzipExtractor {

    private static final boolean DECOMPRESS = true; // Set to false to compress - never tested

    public static void main(String[] args) {
        Path compressedDir = Paths.get("cache/assets/cs2/ng_store");
        Path decompressedDir = Paths.get("cache/assets/cs2/ng_store_decompiled");

        Path inputDir = DECOMPRESS ? compressedDir : decompressedDir;
        Path outputDir = DECOMPRESS ? decompressedDir : compressedDir;

        try {
            Files.createDirectories(outputDir);
        } catch (IOException e) {
            System.err.println("Failed to create output directory: " + outputDir);
            e.printStackTrace();
            return;
        }

        for (final File file : Objects.requireNonNull(inputDir.toFile().listFiles())) {
            try {
                if (file.isDirectory()) continue;

                // Strip the extension
                String name = file.getName()
                        .replace(DECOMPRESS ? ".cs2.pack" : ".cs2", "");

                int scriptId = Integer.parseInt(name); // Enforce numeric file names

                Path inputPath = file.toPath();
                Path outputPath = outputDir.resolve(
                        DECOMPRESS ? scriptId + ".cs2" : scriptId + ".cs2.pack"
                );

                byte[] data = DECOMPRESS
                        ? decompressGzipToBytes(inputPath)
                        : compressBytesToGzipFormat(Files.readAllBytes(inputPath));

                Files.write(outputPath, data);
                System.out.println((DECOMPRESS ? "Decompressed: " : "Compressed: ") + outputPath);

            } catch (Exception e) {
                System.err.println("Failed to " + (DECOMPRESS ? "decompress" : "compress") + " file: " + file);
                e.printStackTrace();
            }
        }
    }

    public static byte[] decompressGzipToBytes(Path source) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        FileInputStream fs = new FileInputStream(source.toFile());
        byte[] fsb = fs.readAllBytes();
        output.write(fsb, 5, fsb.length - 5 - 2); // Strip header (5) and footer (2)
        return output.toByteArray();
    }
    //Never tested the compress method
    public static byte[] compressBytesToGzipFormat(byte[] rawData) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        // Fake GZIP header (5 bytes)
        outputStream.write(new byte[]{0x1F, (byte) 0x8B, 0x08, 0x00, 0x00});
        // Raw data
        outputStream.write(rawData);
        // Fake GZIP footer (2 bytes)
        outputStream.write(new byte[]{0x00, 0x00});
        return outputStream.toByteArray();
    }
}
