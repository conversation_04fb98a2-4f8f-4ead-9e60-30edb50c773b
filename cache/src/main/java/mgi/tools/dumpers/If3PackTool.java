package mgi.tools.dumpers;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import mgi.types.component.ComponentDefinitions;
import mgi.utilities.ByteBuffer;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.file.Files;

/**
 * Utility to dump/pack if3 interface components (whole directory or single file)
 */
public class If3PackTool {

    // ----------------- CONFIGURE -----------------
    private static final String IF3_PACK_DIR = "cache/assets/osnr/store_interface/if3";   // where .if3.pack files live
    private static final String JSON_DIR = "dumps/if3";           // Directory for JSON files

    private static final boolean DUMP_MODE = true;                // true=dump pack->json, false=json->pack

    // Single-file import (optional)
    private static final boolean IMPORT_SINGLE_FILE = false;      // true=import this single file
    private static final String SINGLE_JSON_FILE = "dumps/if3/0.json";  // Path to JSON
    private static final String SINGLE_PACK_OUTPUT = "cache/assets/osnr/store_interface/if3/0"; // Path to output .if3.pack
    // ----------------------------------------------

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    // Helper class for raw hex data when ComponentDefinitions fails
    private static class RawHexData {
        private final String hexData;
        private final String note;

        public RawHexData(String hexData) {
            this.hexData = hexData;
            this.note = "This if3 file could not be decoded as ComponentDefinitions. Raw hex data is provided.";
        }

        public String getHexData() { return hexData; }
        public String getNote() { return note; }
    }

    public static void main(String[] args) throws Exception {
        if (IMPORT_SINGLE_FILE) {
            importSingleFile();
        } else {
            if (DUMP_MODE) {
                dumpAll();
            } else {
                importAll();
            }
        }
    }

    // ----------------- DIRECTORY FUNCTIONS -----------------
    private static void dumpAll() throws Exception {
        File inDir = new File(IF3_PACK_DIR);
        File outDir = new File(JSON_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : inDir.listFiles((dir, name) -> name.endsWith(".if3.pack"))) {
            System.out.println("Dumping " + f.getName());
            byte[] raw = Files.readAllBytes(f.toPath());

            String baseName = f.getName().replace(".if3.pack", ".json");
            File outFile = new File(outDir, baseName);

            try {
                ByteBuffer buf = new ByteBuffer(raw);
                ComponentDefinitions defs = new ComponentDefinitions();
                defs.decode(buf);

                try (FileWriter writer = new FileWriter(outFile)) {
                    gson.toJson(defs, writer);
                }
                System.out.println("✅ Successfully decoded as ComponentDefinitions: " + f.getName());
            } catch (Exception e) {
                System.out.println("⚠️ Failed to decode as ComponentDefinitions, saving as raw hex: " + f.getName());
                System.out.println("Error: " + e.getMessage());

                // Fallback: save as hex string in JSON format
                RawHexData hexData = new RawHexData(bytesToHex(raw));
                try (FileWriter writer = new FileWriter(outFile)) {
                    gson.toJson(hexData, writer);
                }
            }
        }
        System.out.println("✅ Dump complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    // Helper method to convert bytes to hex string
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    // Helper method to convert hex string back to bytes
    private static byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    private static void importAll() throws Exception {
        File inDir = new File(JSON_DIR);
        File outDir = new File(IF3_PACK_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : inDir.listFiles((dir, name) -> name.endsWith(".json"))) {
            System.out.println("Importing " + f.getName());

            try (FileReader reader = new FileReader(f)) {
                // Try to read as ComponentDefinitions first
                try {
                    ComponentDefinitions defs = gson.fromJson(reader, ComponentDefinitions.class);
                    ByteBuffer buf = defs.encode();
                    byte[] data = buf.toArray(0, buf.getPosition());

                    String baseName = f.getName().replace(".json", ".if3.pack");
                    File outFile = new File(outDir, baseName);
                    Files.write(outFile.toPath(), data);
                    System.out.println("✅ Successfully imported as ComponentDefinitions: " + f.getName());
                } catch (Exception e) {
                    // Fallback: try to read as RawHexData
                    reader.close();
                    try (FileReader reader2 = new FileReader(f)) {
                        RawHexData hexData = gson.fromJson(reader2, RawHexData.class);
                        byte[] data = hexToBytes(hexData.getHexData());

                        String baseName = f.getName().replace(".json", ".if3.pack");
                        File outFile = new File(outDir, baseName);
                        Files.write(outFile.toPath(), data);
                        System.out.println("✅ Successfully imported as raw hex data: " + f.getName());
                    }
                }
            }
        }
        System.out.println("✅ Import complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    // ----------------- SINGLE FILE FUNCTION -----------------
    private static void importSingleFile() throws Exception {
        File inFile = new File(SINGLE_JSON_FILE);
        File outFile = new File(SINGLE_PACK_OUTPUT);

        System.out.println("Importing single file: " + inFile);

        try (FileReader reader = new FileReader(inFile)) {
            // Try to read as ComponentDefinitions first
            try {
                ComponentDefinitions defs = gson.fromJson(reader, ComponentDefinitions.class);
                ByteBuffer buf = defs.encode();
                byte[] data = buf.toArray(0, buf.getPosition());
                Files.write(outFile.toPath(), data);
                System.out.println("✅ Single file written as ComponentDefinitions to: " + outFile);
            } catch (Exception e) {
                // Fallback: try to read as RawHexData
                reader.close();
                try (FileReader reader2 = new FileReader(inFile)) {
                    RawHexData hexData = gson.fromJson(reader2, RawHexData.class);
                    byte[] data = hexToBytes(hexData.getHexData());
                    Files.write(outFile.toPath(), data);
                    System.out.println("✅ Single file written as raw hex data to: " + outFile);
                }
            }
        }
    }
}
