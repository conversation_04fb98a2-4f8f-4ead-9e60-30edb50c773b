package mgi.tools.dumpers;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import mgi.types.config.ParamDefinitions;
import mgi.utilities.ByteBuffer;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.file.Files;

/**
 * Utility to dump/pack params (whole directory or single file)
 */
public class ParamPackTool {

    // ----------------- CONFIGURE -----------------
    private static final String PARAM_PACK_DIR = "cache/assets/osnr/store_interface/param";   // where .param.pack files live
    private static final String JSON_DIR = "dumps/param";           // Directory for JSON files

    private static final boolean DUMP_MODE = true;                // true=dump pack->json, false=json->pack

    // Single-file import (optional)
    private static final boolean IMPORT_SINGLE_FILE = false;      // true=import this single file
    private static final String SINGLE_JSON_FILE = "dumps/param/689.json";  // Path to JSON
    private static final String SINGLE_PACK_OUTPUT = "cache/assets/osnr/store_interface/param/689"; // Path to output .param.pack
    // ----------------------------------------------

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    public static void main(String[] args) throws Exception {
        if (IMPORT_SINGLE_FILE) {
            importSingleFile();
        } else {
            if (DUMP_MODE) {
                dumpAll();
            } else {
                importAll();
            }
        }
    }

    // ----------------- DIRECTORY FUNCTIONS -----------------
    private static void dumpAll() throws Exception {
        File inDir = new File(PARAM_PACK_DIR);
        File outDir = new File(JSON_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : inDir.listFiles((dir, name) -> name.endsWith(".param.pack"))) {
            System.out.println("Dumping " + f.getName());
            byte[] raw = Files.readAllBytes(f.toPath());
            ByteBuffer buf = new ByteBuffer(raw);

            // Create a new ParamDefinitions instance
            String baseName = f.getName().replace(".param.pack", "");
            // Extract numeric ID from filename (handles both "689" and "param_689" formats)
            String idStr = baseName.contains("_") ? baseName.substring(baseName.lastIndexOf("_") + 1) : baseName;
            ParamDefinitions defs = new ParamDefinitions();
            defs.decode(buf);

            String jsonName = idStr + ".json";
            File outFile = new File(outDir, jsonName);

            try (FileWriter writer = new FileWriter(outFile)) {
                gson.toJson(defs, writer);
            }
        }
        System.out.println("✅ Dump complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    private static void importAll() throws Exception {
        File inDir = new File(JSON_DIR);
        File outDir = new File(PARAM_PACK_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : inDir.listFiles((dir, name) -> name.endsWith(".json"))) {
            System.out.println("Importing " + f.getName());
            ParamDefinitions defs;
            try (FileReader reader = new FileReader(f)) {
                defs = gson.fromJson(reader, ParamDefinitions.class);
            }

            ByteBuffer buf = defs.encode();
            byte[] data = buf.toArray(0, buf.getPosition());

            // Create output filename - use param_ID format to match expected format
            String idStr = f.getName().replace(".json", "");
            String baseName = "param_" + idStr + ".param.pack";
            File outFile = new File(outDir, baseName);
            Files.write(outFile.toPath(), data);
        }
        System.out.println("✅ Import complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    // ----------------- SINGLE FILE FUNCTION -----------------
    private static void importSingleFile() throws Exception {
        File inFile = new File(SINGLE_JSON_FILE);
        File outFile = new File(SINGLE_PACK_OUTPUT);

        System.out.println("Importing single file: " + inFile);

        ParamDefinitions defs;
        try (FileReader reader = new FileReader(inFile)) {
            defs = gson.fromJson(reader, ParamDefinitions.class);
        }

        ByteBuffer buf = defs.encode();
        byte[] data = buf.toArray(0, buf.getPosition());

        Files.write(outFile.toPath(), data);

        System.out.println("✅ Single file written to: " + outFile);
    }
}
