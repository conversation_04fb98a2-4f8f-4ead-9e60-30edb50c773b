package mgi.types.draw.model;

public class ViewportMouse {

    public static boolean ViewportMouse_isInViewport;
    static int ViewportMouse_x;
    static int ViewportMouse_y;
    static boolean ViewportMouse_false0;
    static int field2635;
    static int field2630;
    public static int ViewportMouse_entityCount;
    public static long[] ViewportMouse_entityTags;

    static {
        ViewportMouse_isInViewport = false; // L: 4
        ViewportMouse_x = 0; // L: 5
        ViewportMouse_y = 0; // L: 6
        ViewportMouse_false0 = false; // L: 7
        ViewportMouse_entityCount = 0; // L: 17
        ViewportMouse_entityTags = new long[1000]; // L: 18
    }

}
