package com.near_reality.cache.interfaces.teleports.categories

import com.near_reality.cache.interfaces.teleports.builder.TeleportsBuilder

internal fun TeleportsBuilder.cities() = "Cities Teleports"(10002) {
    "Al Kharid"(-1323, 3293, 3186, 0, "")
    "Arceeus"(-13513, 1682, 3746, 0, "")
    "Ardougne"(-8011, 2661, 3305, 0, "")
    "Bandit Camp"(-4595, 3172, 2981, 0, "")
    "Brimhaven"(-11745, 2762, 3166, 0, "")
    "Burthorpe"(-3105, 2899, 3545, 0, "")
    "Camelot"(-8010, 2757, 3478, 0, "")
    "Canifis"(-2952, 3494, 3489, 0, "")
    "Catherby"(-377, 2803, 3434, 0, "")
    "Darkmeyer"(-24699, 3592, 3337, 0, "")
    "Draynor Village"(-1704, 3080, 3250, 0, "")
    "<PERSON><PERSON><PERSON>-<PERSON><PERSON>"(-8880, 2717, 5312, 0, "")
    "Falador"(-8009, 2965, 3378, 0, "")
    "Fossil Island"(-21566, 3724, 3811, 0, "")
    "Hosidius"(-13353, 1770, 3598, 0, "")
    "<PERSON>mja"(-1963, 2937, 3148, 0, "")
    "Kourend"(-22947, 1643, 3674, 0, "")
    "Land's End"(-13462, 1505, 3424, 0, "")
    "Lo<PERSON><PERSON>gj"(-13354, 1544, 3762, 0, "")
    "<PERSON>mbridge"(-8008, 3222, 3219, 0, "")
    "Lletya"(-6103, 2332, 3172, 0, "")
    "Miscellania"(-6333, 2533, 3862, 0, "")
    "Mort'Ton"(-3396, 3488, 3287, 0, "")
    "Mos Le Harmless"(-8950, 3683, 2972, 0, "")
    "Nardah"(-1831, 3434, 2917, 0, "")
    "Neitiznot"(-10828, 2311, 3785, 0, "")
    "Piscarilius"(-307, 1818, 3690, 0, "")
    "Piscatoris"(-10174, 2342, 3665, 0, "")
    "Pollnivneach"(-1971, 3359, 2968, 0, "")
    "Port Sarim"(-317, 3028, 3224, 0, "")
    "Prifddinas"(-23962, 3264, 6066, 0, "")
    "Rellekka"(-11744, 2662, 3647, 0, "")
    "Rimmington"(-8782, 2957, 3216, 0, "")
    "Shayzien"(-13379, 1485, 3649, 0, "")
    "Shilo Village"(-616, 2865, 2952, 0, "")
    "Sophanem"(-4691, 3304, 2789, 0, "")
    "Taverley"(-11742, 2896, 3455, 0, "")
    "Trollheim"(-8136, 2890, 3677, 0, "")
    "Tzhaar City"(-6524, 2480, 5174, 0, "")
    "Varrock"(-8007, 3213, 3424, 0, "")
    "Weiss"(-22595, 2855, 3939, 0, "")
    "Yanille"(-11746, 2544, 3092, 0, "")
}