package com.near_reality.cache.interfaces.teleports.categories

import com.near_reality.cache.interfaces.teleports.builder.TeleportsBuilder

internal fun TeleportsBuilder.training() = "Training Teleports"(10001) {
    "Ammonite Crabs"(-21562, 3703, 3879, 0, "")
    "Bandit Camp"(-4611, 3174, 3002, 0, "")
    "Cave Horrors"(-8921, 3746, 9372, 0, "")
    "Chickens"(-314, 3238, 3294, 0, "")
    "Cows"(-1739, 3257, 3267, 0, "")
    "Dagannoths (Lighthouse)"(-13492, 2516, 4625, 0, "")
    "Dagannoths (Waterbirth)"(-13492, 2523, 3740, 0, "")
    "Elf Camp"(-23983, 2203, 3253, 0, "")
    "Experiments"(-7893, 3576, 9927, 0, "")
    "Goblins"(-288, 3260, 3228, 0, "")
    "Ice Trolls"(-10842, 2346, 3832, 0, "")
    "Maniacal Monkeys"(-19525, 2381, 9168, 1, "")
    "Monkey Guards"(-13450, 2786, 2786, 0, "")
    "Ogres"(-13477, 2518, 3366, 0, "")
    "Rock Crabs"(-3695, 2710, 3704, 0, "")
    "Sand Crabs"(-1783, 1779, 3476, 0, "")
    "Slayer Tower"(-11864, 3428, 3532, 0, "")
    "Suqahs"(-9080, 2115, 3921, 0, "")
    "Swamp Crabs"(-1939, 3743, 3327, 0, "")
    "Trolls"(-13483, 2861, 3591, 0, "")
    "Yaks"(-10822, 2332, 3803, 0, "")
}