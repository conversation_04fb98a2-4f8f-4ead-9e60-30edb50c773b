package com.near_reality.cache_tool.packing.custom.sigils

import com.zenyte.game.item.ItemId
import mgi.types.config.StructDefinitions
import mgi.types.config.enums.EnumDefinitions
import mgi.types.config.items.ItemDefinitions

object SigilCustomPacker {

    const val SPRITE_PARAM = 1384;
    const val NAME_PARAM = 1372;
    const val ID_PARAM = 1371;
    const val DESC_PARAM = 1373;
    const val TYPE_PARAM = 1374;
    const val TIER_PARAM = 1375;

    @JvmStatic
    fun pack() {
        /* Attuned Sigils */
        EnumDefinitions.get(4039).apply {
            values.clear()
            var idx = 1
            this.values[idx++] = ItemId.SIGIL_OF_RESISTANCE
            this.values[idx++] = ItemId.SIGIL_OF_DEFT_STRIKES
            this.values[idx++] = ItemId.SIGIL_OF_CONSISTENCY
            this.values[idx++] = ItemId.SIGIL_OF_FORTIFICATION
            this.values[idx++] = ItemId.SIGIL_OF_PRECISION
            this.values[idx++] = ItemId.SIGIL_OF_THE_FERAL_FIGHTER
            this.values[idx++] = ItemId.SIGIL_OF_THE_RUTHLESS_RANGER
            this.values[idx++] = ItemId.SIGIL_OF_THE_MENACING_MAGE
            this.values[idx++] = ItemId.SIGIL_OF_TITANIUM
            this.values[idx++] = ItemId.SIGIL_OF_AGGRESSION
            this.values[idx++] = ItemId.SIGIL_OF_FINALITY
            this.pack()
        }

        /* Unattuned Sigils */
        EnumDefinitions.get(4040).apply {
            values.clear()
            var idx = 1
            this.values[idx++] = ItemId.SIGIL_OF_RESISTANCE_28490
            this.values[idx++] = ItemId.SIGIL_OF_DEFT_STRIKES_26012
            this.values[idx++] = ItemId.SIGIL_OF_CONSISTENCY_25994
            this.values[idx++] = ItemId.SIGIL_OF_FORTIFICATION_26006
            this.values[idx++] = ItemId.SIGIL_OF_PRECISION_28514
            this.values[idx++] = ItemId.SIGIL_OF_THE_FERAL_FIGHTER_26075
            this.values[idx++] = ItemId.SIGIL_OF_THE_RUTHLESS_RANGER_26072
            this.values[idx++] = ItemId.SIGIL_OF_THE_MENACING_MAGE_26078
            this.values[idx++] = ItemId.SIGIL_OF_TITANIUM_28523
            this.values[idx++] = ItemId.SIGIL_OF_AGGRESSION_26132
            this.values[idx++] = ItemId.SIGIL_OF_FINALITY_26126
            this.pack()
        }

        /* Sigil Structs */
        EnumDefinitions.get(4038).apply {
            values.clear()
            var idx = 1
            this.values[idx++] = 3667//Resistance (the smith)
            this.values[idx++] = 3663
            this.values[idx++] = 2973
            this.values[idx++] = 2999
            this.values[idx++] = 3668//Precision (the alchemist)
            this.values[idx++] = 3684
            this.values[idx++] = 3683
            this.values[idx++] = 3685
            this.values[idx++] = 3669//Titanium (the fletcher)
            this.values[idx++] = 3703
            this.values[idx++] = 3701
            this.pack()
        }

        //Resistance
        StructDefinitions.get(3667).apply {
            this.parameters[SPRITE_PARAM] = 5057
            this.parameters[NAME_PARAM] = "Sigil of Resistance"
            this.parameters[DESC_PARAM] = "All damage taken is reduced by 10%."
            this.parameters[TYPE_PARAM] = 1
            this.parameters[ID_PARAM] = 1
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_RESISTANCE_28490).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_RESISTANCE).apply {
            this.parameters[ID_PARAM] = 1
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }

        //Deft Strikes
        StructDefinitions.get(3663).apply {
            this.parameters[ID_PARAM] = 2
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_DEFT_STRIKES_26012).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_DEFT_STRIKES).apply {
            this.parameters[ID_PARAM] = 2
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }

        //Consistency
        StructDefinitions.get(2973).apply {
            this.parameters[ID_PARAM] = 3
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_CONSISTENCY_25994).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_CONSISTENCY).apply {
            this.parameters[ID_PARAM] = 3
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }

        //Fortification
        StructDefinitions.get(2999).apply {
            this.parameters[DESC_PARAM] = "All damage taken is reduced by 25%."
            this.parameters[ID_PARAM] = 4
            this.parameters[TIER_PARAM] = 2
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_FORTIFICATION_26006).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.originalColours = shortArrayOf(31682)
            this.replacementColours = shortArrayOf(962)
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_FORTIFICATION).apply {
            this.parameters[ID_PARAM] = 4
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.originalColours = shortArrayOf(31682)
            this.replacementColours = shortArrayOf(962)
            this.pack()
        }

        //Precision
        StructDefinitions.get(3668).apply {
            this.parameters[SPRITE_PARAM] = 5051
            this.parameters[NAME_PARAM] = "Sigil of Precision"
            this.parameters[DESC_PARAM] = "Gain 50% more accuracy in all styles."
            this.parameters[TYPE_PARAM] = 1
            this.parameters[TIER_PARAM] = 2
            this.parameters[ID_PARAM] = 5
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_PRECISION_28514).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_PRECISION).apply {
            this.parameters[ID_PARAM] = 5
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }

        //Feral Fighter
        StructDefinitions.get(3684).apply {
            this.parameters[DESC_PARAM] = "Successful melee hits have a 10% chance to hit twice."
            this.parameters[ID_PARAM] = 6
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_THE_FERAL_FIGHTER_26075).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_THE_FERAL_FIGHTER).apply {
            this.parameters[ID_PARAM] = 6
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }

        //Ruthless Ranger
        StructDefinitions.get(3683).apply {
            this.parameters[DESC_PARAM] = "Successful ranged hits have a 10% chance to hit twice."
            this.parameters[ID_PARAM] = 7
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_THE_RUTHLESS_RANGER_26072).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_THE_RUTHLESS_RANGER).apply {
            this.parameters[ID_PARAM] = 7
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }

        //Menacing Mage
        StructDefinitions.get(3685).apply {
            this.parameters[DESC_PARAM] = "Successful mage hits have a 10% chance to hit twice."
            this.parameters[ID_PARAM] = 8
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_THE_MENACING_MAGE_26078).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_THE_MENACING_MAGE).apply {
            this.parameters[ID_PARAM] = 8
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }

        //Titanium
        StructDefinitions.get(3669).apply {
            this.parameters[SPRITE_PARAM] = 5057
            this.parameters[NAME_PARAM] = "Sigil of Titanium"
            this.parameters[DESC_PARAM] = "All damage taken is reduced by 50%."
            this.parameters[TYPE_PARAM] = 1
            this.parameters[TIER_PARAM] = 3
            this.parameters[ID_PARAM] = 9
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_TITANIUM_28523).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_TITANIUM).apply {
            this.parameters[ID_PARAM] = 9
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }

        //Aggression
        StructDefinitions.get(3703).apply {
            this.parameters[DESC_PARAM] = "All attacks deal 20% more damage, but you take 10% more damage."
            this.parameters[ID_PARAM] = 10
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_AGGRESSION_26132).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }

        ItemDefinitions.get(ItemId.SIGIL_OF_AGGRESSION).apply {
            this.parameters[ID_PARAM] = 10
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }

        //Finality
        StructDefinitions.get(3701).apply {
            this.parameters[DESC_PARAM] = "Instantly kill monsters that are below 15% health."
            this.parameters[ID_PARAM] = 11
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_FINALITY_26126).apply {
            this.inventoryOptions = arrayOf("Attune", "Inspect", "Recharge", null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }
        ItemDefinitions.get(ItemId.SIGIL_OF_FINALITY).apply {
            this.parameters[ID_PARAM] = 11
            this.inventoryOptions = arrayOf("Unattune", "Inspect", null, null, "Destroy")
            this.isGrandExchange = false
            this.pack()
        }
    }
}