package com.near_reality.game.content.dt2.drops

import com.near_reality.game.world.entity.player.attributes.hasReceivedSmokeQuartz
import com.near_reality.scripts.npc.drops.NPCDropTableScript
import com.near_reality.scripts.npc.drops.table.DropTableType.*
import com.near_reality.scripts.npc.drops.table.dsl.StandaloneDropTableBuilder
import com.near_reality.scripts.npc.drops.table.noted
import com.zenyte.game.item.Item
import com.zenyte.game.item.ItemId.*
import com.zenyte.game.util.Utils
import com.zenyte.game.world.entity.npc.NpcId.THE_LEVIATHAN

class LeviathanNormalDropTable : NPCDropTableScript() {
    init {
        npcs(THE_LEVIATHAN)

        onDeath {


            if (Utils.random(95) == 0) {
                rollTable(killer, Standalone, LeviathanUniques.staticTable).forEach {
                    npc.dropItemAtKiller(killer, Item(it.id, 1))
                }
            } else if (Utils.random(52) == 0) {
                npc.dropItemAtKiller(killer, Item(AWAKENERS_ORB))
            } else if (Utils.random(199) == 0) {
                npc.dropItemAtKiller(killer, Item(SMOKE_QUARTZ))
                killer.hasReceivedSmokeQuartz = true
            } else if (Utils.random(4) == 0) {
                npc.dropItemAtKiller(killer, Item(PRAYER_POTION3, 1))
                npc.dropItemAtKiller(killer, Item(RANGING_POTION2, 1))
                npc.dropItemAtKiller(killer, Item(SEA_TURTLE, (3..4).random()))
            } else {
                rollStaticTableAndDropBelowPlayer(killer, Main)
            }


            if (Utils.random(2000) == 0) {
                npc.dropItemAtKiller(killer, Item(LILVIATHAN))
            }
            if (Utils.random(39) == 0) {
                npc.dropItemAtKiller(killer, Item(CLUE_SCROLL))
            }
        }






        appendDrop(DisplayedDrop(AWAKENERS_ORB, 1, 1, 53.00))
        appendDrop(DisplayedDrop(SCARRED_TABLET, 1, 1, 25.00))
        appendDrop(DisplayedDrop(SMOKE_QUARTZ, 1, 1, 200.00))
        appendDrop(DisplayedDrop(PRAYER_POTION3, 1, 1, 5.00))
        appendDrop(DisplayedDrop(RANGING_POTION2, 1, 1, 5.00))
        appendDrop(DisplayedDrop(SEA_TURTLE, 3, 4, 5.00))
        appendDrop(DisplayedDrop(LILVIATHAN, 1, 1, 2000.00))


        appendDrop(DisplayedDrop(PURE_ESSENCE, 180, 270, 100.00))
        appendDrop(DisplayedDrop(IRON_ORE, 57, 85, 100.00))
        appendDrop(DisplayedDrop(SILVER_ORE, 57, 85, 100.00))
        appendDrop(DisplayedDrop(COAL, 195, 292, 800.00))
        appendDrop(DisplayedDrop(GOLD_ORE, 67, 101, 800.00))
        appendDrop(DisplayedDrop(ADAMANTITE_ORE, 57, 85, 100.00))
        appendDrop(DisplayedDrop(RUNITE_ORE, 27, 40, 200.00))
        appendDrop(DisplayedDrop(SAPPHIRE, 25, 38, 100.00))
        appendDrop(DisplayedDrop(EMERALD, 25, 38, 100.00))
        appendDrop(DisplayedDrop(RUBY, 25, 38, 100.00))
        appendDrop(DisplayedDrop(UNCUT_RUBY, 37, 56, 500.00))
        appendDrop(DisplayedDrop(UNCUT_DIAMOND, 37, 56, 500.00))
        appendDrop(DisplayedDrop(DRAGON_JAVELIN_HEADS, 54, 81, 800.00))
        appendDrop(DisplayedDrop(DRAGON_BOLTS_UNF, 150, 225, 200.00))
        appendDrop(DisplayedDrop(ONYX_BOLT_TIPS, 90, 135, 100.00))
        appendDrop(DisplayedDrop(RAW_MANTA_RAY, 180, 270, 100.00))
        appendDrop(DisplayedDrop(ANGLERFISH, 4, 6, 800.00))

        appendDrop(DisplayedDrop(BRONZE_ARROW, 63, 94, 100.00))
        appendDrop(DisplayedDrop(MITHRIL_ARROW, 63, 94, 100.00))
        appendDrop(DisplayedDrop(ADAMANT_ARROW, 63, 94, 100.00))
        appendDrop(DisplayedDrop(RUNE_ARROW, 54, 81, 800.00))
        appendDrop(DisplayedDrop(BODY_RUNE, 180, 270, 100.00))
        appendDrop(DisplayedDrop(EARTH_RUNE, 180, 270, 100.00))
        appendDrop(DisplayedDrop(SMOKE_RUNE, 300, 450, 800.00))
        appendDrop(DisplayedDrop(SOUL_RUNE, 600, 900, 200.00))

        appendDrop(DisplayedDrop(VIRTUS_MASK, 1, 1, 1600.00))
        appendDrop(DisplayedDrop(VIRTUS_ROBE_TOP, 1, 1, 1600.00))
        appendDrop(DisplayedDrop(VIRTUS_ROBE_LEGS, 1, 1, 1600.00))

        buildTable(100) {
            Main {
                PURE_ESSENCE quantity 180.noted rarity 1
                IRON_ORE quantity 57.noted rarity 1
                SILVER_ORE quantity 57.noted rarity 1
                COAL quantity 195.noted rarity 8
                GOLD_ORE quantity 67.noted rarity 8
                ADAMANTITE_ORE quantity 57.noted rarity 1
                RUNITE_ORE quantity 27.noted rarity 2
                SAPPHIRE quantity 25.noted rarity 1
                EMERALD quantity 25.noted rarity 1
                RUBY quantity 25.noted rarity 1
                UNCUT_RUBY quantity 37.noted rarity 5
                UNCUT_DIAMOND quantity 37.noted rarity 5
                DRAGON_JAVELIN_HEADS quantity 54 rarity 8
                DRAGON_BOLTS_UNF quantity 150 rarity 2
                ONYX_BOLT_TIPS quantity 90 rarity 1
                RAW_MANTA_RAY quantity 180.noted rarity 1
                ANGLERFISH quantity 4.noted rarity 8

                BRONZE_ARROW quantity 63 rarity 1
                MITHRIL_ARROW quantity 63 rarity 1
                ADAMANT_ARROW quantity 63 rarity 1
                RUNE_ARROW quantity 54 rarity 8
                BODY_RUNE quantity 180 rarity 1
                EARTH_RUNE quantity 180 rarity 1
                SMOKE_RUNE quantity 300 rarity 8
                SOUL_RUNE quantity 600 rarity 2
            }
            Tertiary {
                LILVIATHAN quantity 1 oneIn 2500
                CLUE_SCROLL quantity 1 oneIn 40
            }
        }
    }

    object LeviathanUniques : StandaloneDropTableBuilder({
        limit = 24
        static {
            VENATOR_VESTIGE quantity 1 rarity 3
            CHROMIUM_INGOT quantity 1 rarity 9
            LEVIATHANS_LURE quantity 1 rarity 3
            VIRTUS_MASK quantity 1 rarity 1
            VIRTUS_ROBE_TOP quantity 1 rarity 1
            VIRTUS_ROBE_LEGS quantity 1 rarity 1
        }
    })
}
