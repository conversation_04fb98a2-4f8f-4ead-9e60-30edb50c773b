package com.near_reality.game.content.dt2.npc.theduke

import com.zenyte.game.world.entity.Location
import com.zenyte.game.world.`object`.WorldObject

/**
 * <PERSON> wrote original logic - <PERSON><PERSON> rewrote in NR terms
 * <AUTHOR> / <PERSON>
 * @date 8.14.2024
 */
data class Extremity(
    var location: Location,
    var asleepId: Int,
    var awakenedId: Int,
    val rotation: Int,
    val gazeAnim: Int,
    val spotLightNPC: Int,
    val animationId: Int,
    val restAnimationId: Int = 10185
) {
    fun awakened(): WorldObject = WorldObject(
        id = awakenedId,
        rotation = rotation,
        tile = location
    )

    fun sleeping(): WorldObject = WorldObject(
        id = asleepId,
        rotation = rotation,
        tile = location
    )

}