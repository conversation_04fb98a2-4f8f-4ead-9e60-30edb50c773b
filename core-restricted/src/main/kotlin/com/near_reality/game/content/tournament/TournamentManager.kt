package com.near_reality.game.content.tournament

import com.near_reality.game.content.tournament.preset.TournamentPreset
import com.near_reality.game.world.WorldEvent
import com.near_reality.game.world.WorldEventListener
import org.slf4j.LoggerFactory
import kotlin.time.Duration

object TournamentManager : WorldEventListener<WorldEvent.Tick> {

    var enabled = false // Enable/Disable Tournaments here
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val tournamentControllers = mutableListOf<TournamentController>(TournamentController.Global)

    fun schedule(preset: TournamentPreset, durationUntilStart: Duration) {
        val manualController = TournamentController.Manual(preset, durationUntilStart)
        tournamentControllers.add(manualController)
        logger.info("Manually scheduled a tournament: $preset starting in $durationUntilStart.")
    }

    fun clearManualTournaments() { // Clean up finished manual tournaments
        val removed = tournamentControllers.removeIf {
            it is TournamentController.Manual && it.getTournamentIfActive() == null
        }
        if (removed) {
            logger.info("Cleared all finished manually scheduled tournaments.")
        } else {
            logger.info("No finished manual tournaments to clear.")
        }
    }

    fun clearAllTournaments(): Int { //Stops all tournaments that are scheduled or currently occurring.
        val cleared = tournamentControllers.size
        tournamentControllers.clear()
        return cleared
    }

    fun listActiveTournaments(): List<Tournament> =
        tournamentControllers.mapNotNull { it.getTournamentIfActive() }

    fun getController(tournament: Tournament): TournamentController? =
        tournamentControllers.find { it.isControlling(tournament) }

    override fun on(event: WorldEvent.Tick) {

        tournamentControllers.removeIf { !it.process() }
    }
}
