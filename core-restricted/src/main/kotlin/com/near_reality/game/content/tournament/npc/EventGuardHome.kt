package com.near_reality.game.content.tournament.npc

import com.near_reality.game.world.entity.player.communityEventPoints
import com.zenyte.game.content.event.communityevents.EventManager
import com.zenyte.game.util.Colour
import com.zenyte.game.world.entity.npc.NPC
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.actions.NPCPlugin
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.dialogue.dialogue
import com.zenyte.game.world.entity.player.dialogue.options

/**
 * Represents the Event Guard NPC at home.
 *
 * <AUTHOR> | 07/06/2019 | 00:01
 * <AUTHOR>
 */
@Suppress("unused")
class EventGuardHome : NPCPlugin() {

    override fun handle() {
        bind("Talk-to", ::startDialogue)
        bind("Trade", ::openEventShop)
        bind("View Current Event", ::showEventStatus)
    }

    private fun startDialogue(player: Player, npc: NPC) {
        player.dialogue(npc) {
            npc("Greetings, I'm the Event Guard. What can I do for you?")
            options {
                dialogueOption("Can you tell me more about the community event system?") {
                    npc("Of course. The community event system lets our staff host special events for everyone to enjoy.")
                    npc("These can be boss masses, world boosts, tournaments, and more.")
                    npc("When an event begins, you'll see a global broadcast with a command you can type to join right away.")
                    npc("Each event is unique, so keep an eye out.")
                }
                dialogueOption("Can I view the current event?") {
                    npc("Lets see..")
                        .executeAction { showEventStatus(player, npc) }
                }
                dialogueOption("View Event Shop", true) {
                    openEventShop(player, npc)
                }
                dialogueOption("Nevermind")
            }
        }
    }

    private fun openEventShop(player: Player, npc: NPC) {
        player.openShop("Community Event Shop")
        player.sendMessage("You currently have ${Colour.RS_RED.wrap(player.communityEventPoints.toString())} Community Event Points.")
    }

    private fun showEventStatus(player: Player, npc: NPC) {
        EventManager.sendActiveEventStatus(player, npc)
    }

    override fun getNPCs(): IntArray =
        intArrayOf(NpcId.EVENT_GUARD_16012)
}
