0	[clientscript,tt_rewards_init]
1	[proc,settings_warning_carrallangar_teleport]
2	[proc,hex_to_rgb]
3	[proc,target_world]
4	[clientscript,tt_reward_selected_onclick]
5	[clientscript,tt_reward_selected]
6	[clientscript,wom_tab_init]
7	[proc,tt_reward_highlight_selected]
8	[proc,tt_reward_set_desc]
9	[clientscript,tt_reward_highlight]
10	[clientscript,wom_item_move]
11	[clientscript,wom_tab_highlight]
16	[clientscript,wom_tab_highlight_onclick]
17	[clientscript,wom_item_update]
18	[clientscript,wom_item_hide]
19	[proc,magic_runecount]
20	[proc,magic_tostring]
21	[proc,wom_build_item_list]
22	[clientscript,tt_reward_highlight_off]
23	[clientscript,pvp_store_init]
25	[clientscript,closebutton]
26	[proc,closebutton_action]
27	[clientscript,closebutton_over]
28	[clientscript,closebutton_leave]
29	[clientscript,closebutton_click]
30	[clientscript,scrollbar_vertical]
31	[proc,scrollbar_vertical]
32	[clientscript,scrollbar_vertical_up]
33	[clientscript,scrollbar_vertical_down]
34	[clientscript,scrollbar_vertical_jump]
35	[clientscript,scrollbar_vertical_drag]
36	[clientscript,scrollbar_vertical_wheel]
37	[proc,scrollbar_vertical_doscroll]
38	[clientscript,addtooltip]
39	[clientscript,camera_zoom]
40	[clientscript,deltooltip]
41	[proc,deltooltip_action]
42	[proc,camera_do_zoom]
43	[clientscript,zoom_mouse_toggle]
44	[clientscript,graphic_swapper]
45	[clientscript,text_colour_swapper]
46	[proc,tostring_spacer]
48	[clientscript,cc_colour_temporarily]
49	[clientscript,docheat]
50	[proc,settings_warning_carrallangar_tablet]
51	[clientscript,pattern_next_setmodel]
52	[clientscript,pattern_cards_setmodel]
53	[clientscript,tol_pressure_leverinit]
54	[clientscript,poh_options_drawbutton]
55	[clientscript,chatbox_keyinput_init]
56	[proc,area_task_complete]
57	[clientscript,chatbox_keyinput_listener]
58	[clientscript,chatbox_multi_init]
59	[proc,chatbox_multi_addoption]
60	[clientscript,dream_bank_init]
61	[clientscript,dream_chest_init]
62	[clientscript,hp_hud_update_bar]
63	[clientscript,scrollbar_vertical_graphics]
65	[clientscript,set_spinner]
66	[clientscript,spinner]
67	[clientscript,model_swapper]
68	[clientscript,text_swapper]
69	[clientscript,comp_sethide]
70	[clientscript,tzhaar_fightpit_foesremaining]
71	[proc,toplevel_sidebutton_op]
72	[proc,scrollbar_resize]
73	[clientscript,chatdefault_onkey]
74	[proc,add_to_inputstring]
75	[proc,chatout_scrollup]
76	[proc,chatout_scrolldown]
77	[proc,chatout_add]
78	[proc,chatout_set]
79	[proc,chatout_get]
80	[clientscript,chat_onsubchange]
82	[clientscript,orbs_update_prayer]
83	[clientscript,splitpm_changed]
84	[proc,rebuildchatbox]
85	[clientscript,cc_colour_swapper]
86	[clientscript,chat_op]
87	[clientscript,macro_pinball_score]
88	[clientscript,private_op]
89	[proc,rebuildpmbox]
90	[proc,filtertest]
91	[proc,pm_filtertest]
92	[clientscript,v2_stone_button]
93	[clientscript,v2_stone_button_filled]
94	[clientscript,v2_stone_button_in]
95	[proc,chat_autotyper_updateop]
96	[proc,chat_sendpublic]
97	[clientscript,v2_stone_button_out_filled]
98	[proc,create_graphic]
99	[clientscript,duel_check_button]
100	[clientscript,duel_stake_vartrans]
101	[clientscript,meslayer_close]
102	[proc,meslayer_mode1]
103	[clientscript,meslayer_mode2]
104	[clientscript,meslayer_mode3]
105	[clientscript,meslayer_mode4]
106	[clientscript,meslayer_mode5]
107	[proc,meslayer_mode6]
108	[clientscript,meslayer_mode7]
109	[clientscript,meslayer_mode8]
110	[clientscript,meslayer_mode9]
111	[proc,meslayer_mode10]
112	[clientscript,meslayer_onkey]
114	[clientscript,oculus_init]
115	[clientscript,pattern_cards_select_init]
116	[clientscript,pattern_cards_select_op]
117	[clientscript,pattern_cards_select_update]
118	[clientscript,questscroll_init]
119	[clientscript,component_pulse_start]
120	[proc,pattern_cards_select_update]
121	[clientscript,pattern_cards_confirm_init]
122	[clientscript,component_pulse_timer]
123	[clientscript,friend_init]
124	[clientscript,grouping_viewchannel]
125	[proc,friend_update]
126	[clientscript,friend_op]
127	[clientscript,ignore_init]
128	[clientscript,grouping_offset]
129	[proc,ignore_update]
130	[clientscript,ignore_op]
131	[clientscript,closebutton_key]
132	[clientscript,barrows_overlay_init]
133	[clientscript,snapshot_onkey]
134	[proc,v2_stone_button_filled]
135	[clientscript,if_settextfont]
136	[clientscript,abyssalsire_overseer]
137	[clientscript,abyssalsire_overseer_update]
138	[clientscript,meslayer_ondialogabort]
139	[clientscript,raids_partydetails_autorefresh]
140	[clientscript,barrows_overlay_size]
141	[proc,ge_collect_all_init]
142	[clientscript,trade_confirm_init]
143	[clientscript,cam_forceangle]
144	[clientscript,bank_depositbox_init]
145	[clientscript,bank_depositbox_update]
146	[proc,bank_depositbox_update]
149	[clientscript,interface_inv_init]
150	[clientscript,interface_inv_init_big]
151	[clientscript,interface_inv_update_big]
152	[clientscript,chat_set_filter]
153	[proc,interface_inv_update_big]
154	[proc,interface_inv_draw_slot_big]
155	[clientscript,interface_inv_dragcomplete_swap_big]
156	[clientscript,interface_inv_dragcomplete_shuffle_big]
157	[proc,scrollbar_ondrag_doscroll]
158	[clientscript,interface_invother_init]
159	[clientscript,interface_invother_update_big]
160	[proc,interface_invother_update_big]
161	[proc,interface_invother_draw_slot_big]
162	[clientscript,interface_inv_drag_slot]
163	[clientscript,cws_toggle]
164	[clientscript,cws_graphic_swap]
165	[clientscript,cws_graphic_swap_2]
166	[proc,cws_cc_getvar]
167	[clientscript,cws_mouseover]
168	[proc,barrows_overlay_size]
169	[clientscript,kr_mouseleave_colour]
170	[clientscript,kr_tumbler_height]
171	[clientscript,kr_display_riddle]
172	[proc,bank_depositbox_drawslot]
175	[clientscript,chat_button_onop]
176	[proc,skill_guide_subsection_thieving]
177	[clientscript,raids_storage_items_op]
178	[proc,redraw_chat_buttons]
179	[proc,chat_alert_get]
180	[proc,chat_alert_enable]
181	[clientscript,ntk_layout]
182	[clientscript,chat_alert_flash]
183	[proc,chat_alert_set]
184	[proc,chat_set_filter]
185	[proc,chat_get_filter]
186	[clientscript,combat_interface_sp_redraw]
187	[proc,combat_interface_sp_redraw]
188	[clientscript,combat_interface_sp_updatebar]
189	[proc,combat_interface_sp_updatebar]
190	[clientscript,patchy_page_1]
191	[clientscript,patchy_page_2]
192	[proc,chatalerts]
193	[proc,panetest]
194	[clientscript,joinchat_onop]
195	[clientscript,pattern_cards_confirm_update]
197	[clientscript,agilityarena_rewards_init]
198	[proc,v2_stone_button_in_filled]
200	[clientscript,displayname_init]
201	[clientscript,pet_insurance_insured]
204	[clientscript,displayname_draw]
205	[clientscript,duel_initworn]
206	[clientscript,duel_confirm_text]
207	[proc,duel_addtostring]
208	[proc,v2_stone_button_out_filled]
209	[clientscript,trade_partner_set]
210	[proc,trade_partner_check]
211	[clientscript,poh_options_buildmode]
213	[proc,clanwars_hud_timeremaining_resynch]
216	[clientscript,rebuildchatbox]
217	[clientscript,menu]
218	[proc,menu_createentry]
220	[proc,ntk_layout]
221	[clientscript,cc_colour_temporarily_end]
224	[proc,inputstring_handlecheat]
225	[proc,inputstring_teledirection]
226	[clientscript,lost_property_init]
227	[clientscript,steelborder]
228	[proc,steelborder]
229	[clientscript,cc_graphic_swapper]
230	[clientscript,makeover_clothes_setup]
231	[proc,scrollbar_vertical_rebuild]
232	[clientscript,league_rewards_init]
234	[proc,fake_runes_enabled]
235	[clientscript,autocast_init]
236	[clientscript,autocast_spellhighlight]
237	[proc,autocast_spellhighlight]
238	[clientscript,autocast_tooltip]
239	[proc,autocast_tooltip]
240	[clientscript,autocast_tooltip_runecount]
241	[proc,autocast_tooltip_runecount]
242	[proc,chatdefault_opt_item]
243	[proc,autocast_spellpos]
244	[clientscript,cc_settrans]
245	[clientscript,deadman_tournament_fogcolour]
246	[proc,rgb_to_hex]
247	[proc,inzone]
248	[clientscript,nzone_rewards_init]
249	[proc,steelbox]
250	[clientscript,music_init]
251	[clientscript,nzone_rewards_updatepotion]
252	[clientscript,music_init_component]
253	[clientscript,nzone_listbosses]
254	[clientscript,nzone_listquests]
255	[clientscript,nzone_game_overlay]
256	[clientscript,nzone_game_hide]
257	[clientscript,nzone_game_points_transmit]
258	[clientscript,nzone_game_points_tickup]
259	[proc,nzone_game_points_set]
260	[clientscript,nzone_game_absorption_transmit]
261	[clientscript,nzone_game_absorption_tickdown]
262	[proc,nzone_game_absorption_set]
263	[clientscript,nzone_game_subtitle]
264	[clientscript,nzone_lobby_overlay]
265	[clientscript,nzone_lobby_hide]
266	[clientscript,nzone_lobby_partystatus]
267	[proc,nzone_lobby_partystatus]
268	[clientscript,nzone_lobby_partynames]
269	[proc,nzone_lobby_partynames]
270	[clientscript,nzone_lobby_coffer]
271	[proc,nzone_lobby_coffer]
272	[clientscript,nzone_lobby_coffer_examine]
273	[clientscript,settrans]
274	[clientscript,bankmain_init]
275	[proc,nzone_rewards_price]
276	[clientscript,bankmain_build]
277	[proc,bankmain_build]
278	[proc,bankmain_drawitem]
279	[proc,bankmain_filteritem]
280	[proc,bankmain_filterstring]
281	[clientscript,bankmain_search_toggle]
282	[proc,bankmain_search_setbutton]
283	[clientscript,bankmain_search_refresh]
284	[clientscript,bankmain_dragscroll]
285	[clientscript,bankmain_reorder]
286	[clientscript,bankmain_swapinsert]
287	[proc,bankmain_swapinsert]
288	[clientscript,bankmain_swapinsert_op]
289	[clientscript,bankmain_itemnote]
290	[proc,bankmain_itemnote]
291	[clientscript,bankmain_itemnote_op]
292	[clientscript,bankmain_depositall]
293	[clientscript,bankmain_depositall_end]
294	[clientscript,bankside_init]
295	[clientscript,bankside_build]
296	[proc,bankside_build]
297	[proc,bankside_drawitem]
298	[clientscript,bankside_reorder]
299	[proc,meslayer_close]
300	[proc,meslayer_mode11]
301	[clientscript,nzone_rewards_lists]
302	[proc,nzone_rewards_lists]
303	[proc,nzone_rewards_lists_drawitem]
304	[proc,nzone_rewards_updatepotion]
305	[clientscript,nzone_rewards_upgrades]
306	[proc,nzone_rewards_upgrades]
307	[clientscript,nzone_rewards_tabs]
308	[proc,nzone_rewards_tabs]
309	[proc,nzone_rewards_tab]
310	[clientscript,nzone_rewards_setpoints]
311	[proc,nzone_rewards_setpoints]
312	[clientscript,welcome_screen_init]
313	[clientscript,music_vartransmit]
314	[proc,music_setcolour]
315	[clientscript,music_optionbuttons_setmode]
316	[clientscript,music_optionbuttons_setloop]
317	[clientscript,music_optionbuttons_resynch]
318	[proc,music_optionbuttons]
319	[clientscript,slayer_rewards_confirm_back]
320	[clientscript,slayer_rewards_confirm_backlater]
321	[clientscript,slayer_rewards_buy_init]
322	[clientscript,slayer_rewards_buy_op]
323	[clientscript,combat_interface_opbutton]
324	[proc,combat_interface_setbuttons]
325	[clientscript,combat_interface_retaliate]
326	[clientscript,slayer_rewards_buy_release]
327	[clientscript,combat_interface_sp]
328	[clientscript,slayer_rewards_tasks_init]
329	[clientscript,combat_interface_autocast]
330	[clientscript,thormac_setup]
331	[clientscript,thormac_highlight]
332	[proc,thormac_highlight]
333	[clientscript,bankpin_keypad_init]
334	[clientscript,godwars_hideshow_init]
335	[clientscript,godwars_hideshow]
336	[proc,godwars_hideshow]
337	[clientscript,godwars_killcount_armadyl]
338	[clientscript,godwars_killcount_bandos]
339	[clientscript,godwars_killcount_saradomin]
340	[clientscript,godwars_killcount_zamorak]
341	[proc,godwars_killcount]
342	[clientscript,godwars_darkness]
343	[clientscript,godwars_fader_init]
344	[clientscript,godwars_fader]
345	[proc,pattern_cards_confirm_update]
346	[clientscript,pattern_cards_confirm_op]
347	[clientscript,pattern_cards_confirm_reset]
348	[clientscript,poh_menu_init]
350	[proc,sw_world]
353	[clientscript,stonepanel_light]
354	[proc,stonepanel_light]
355	[clientscript,stonepanel_blue]
356	[proc,stonepanel_blue]
357	[clientscript,stonepanel_red]
358	[proc,stonepanel_red]
375	[proc,settings_bond_options]
376	[proc,get_selected_quantity]
377	[proc,set_selected_quantity]
378	[clientscript,menu_indexed]
379	[proc,menu_createentry_indexed]
383	[proc,pvpw_check]
384	[proc,wilderness_level]
385	[clientscript,pvp_icons_layout]
386	[proc,pvp_icons_layout]
387	[proc,pvp_icons_comlevelrange]
388	[clientscript,pvp_icons_wildernesslevel]
389	[clientscript,combat_level_transmit]
390	[clientscript,combat_wilderness_transmit]
391	[clientscript,poh_options_doors]
392	[clientscript,steelbox]
393	[clientscript,stats_init]
394	[clientscript,stats_setlevels]
395	[proc,stats_setlevels]
396	[clientscript,stats_skilltotal]
397	[clientscript,fairyrings_op]
398	[clientscript,fairyrings_rotate]
399	[clientscript,fairyrings_confirm]
400	[clientscript,fairyrings_confirm_expire]
401	[clientscript,fairyrings_sort_mouseeffect]
402	[clientscript,fairyrings_sort_update]
403	[clientscript,poh_tablets_init]
404	[clientscript,raids_storage_private_init]
405	[clientscript,slayer_rewards_init]
406	[clientscript,slayer_rewards_tabs]
407	[proc,slayer_rewards_tabs]
408	[proc,slayer_rewards_tab]
409	[clientscript,slayer_rewards_setpoints]
410	[proc,slayer_rewards_setpoints]
411	[clientscript,slayer_rewards_unlock_init]
414	[clientscript,slayer_rewards_unlock_confirm]
415	[clientscript,smithing_init]
416	[clientscript,smithing_setup]
417	[clientscript,chat_set_filter_conditional]
418	[clientscript,longscroll_setup]
419	[proc,skill_guide_subsection_slayer]
420	[clientscript,combat_interface_setup]
421	[clientscript,slayer_rewards_tasks_currenttask]
422	[proc,slayer_rewards_tasks_currenttask]
423	[clientscript,slayer_rewards_tasks_canceltask]
424	[clientscript,slayer_rewards_tasks_blocked_init]
425	[clientscript,slayer_rewards_tasks_blocked_draw]
426	[proc,slayer_rewards_tasks_blocked_draw]
427	[clientscript,slayer_rewards_tasks_unblocktask]
430	[proc,smithing_setup]
431	[proc,smithing_item]
432	[clientscript,grouping_init]
433	[clientscript,grouping_dropdown]
434	[clientscript,grouping_rebuild]
435	[proc,grouping_rebuild]
436	[clientscript,grouping_mouseover]
437	[clientscript,grouping_joinchannel]
438	[proc,tournament_world]
440	[clientscript,settings_client_type]
443	[clientscript,castlewarstrade_init]
444	[clientscript,barbassault_turret_data]
445	[proc,setvolumemusic]
446	[clientscript,orbs_update_health]
447	[clientscript,orbs_update_runenergy]
449	[proc,orbs_update]
450	[clientscript,rareitems_diango]
451	[proc,high_risk_world]
453	[clientscript,rune_pouch_init]
454	[proc,orbs_update_runmode]
455	[clientscript,orbs_toggle_prayer]
456	[proc,orbs_update_prayer]
457	[clientscript,orbs_toggle_runmode]
458	[clientscript,prayer_init]
459	[clientscript,tol_pressure_leverresynch]
460	[proc,tol_pressure_leverresynch]
461	[clientscript,prayer_updatebutton]
462	[clientscript,prayer_op]
463	[proc,prayer_updatebutton]
464	[proc,prayer_isavailable]
465	[proc,prayer_quicksort]
466	[clientscript,quickprayer_init]
467	[clientscript,quickprayer_icon_update]
468	[proc,quickprayer_icon_update]
469	[clientscript,quickprayer_button_op]
470	[clientscript,quickprayer_button_update]
471	[proc,quickprayer_button_update]
472	[clientscript,quickprayer_close]
473	[clientscript,quickprayer_close_timeout]
475	[proc,pest_rewards_divider]
476	[clientscript,br_hud_writetimer]
477	[clientscript,ntk_textbox]
478	[proc,skill_guide_data_thieving]
479	[clientscript,ntk_timer_resynch]
480	[proc,ntk_timer_resynch]
481	[clientscript,ntk_timer_autonomous]
482	[proc,ntk_timer_update]
483	[clientscript,barbassault_tutorial_button_init]
484	[clientscript,barbassault_tutorial_button_setup]
485	[proc,barbassault_tutorial_button_setup]
486	[proc,v2_stone_button]
487	[clientscript,cc_settrans_temporarily]
488	[clientscript,cc_settrans_temporarily_end]
489	[clientscript,opsound]
490	[clientscript,bankside_switchview]
491	[proc,bankside_switchview]
492	[clientscript,misc_collection_init]
493	[clientscript,misc_collection_draw]
494	[proc,tostring_minutes]
495	[clientscript,wilderness_lootingbag_setup]
496	[clientscript,wilderness_lootingbag_draw]
497	[proc,wilderness_lootingbag_draw]
498	[clientscript,killdeathratio_init]
499	[proc,makeover_drawmodels]
500	[clientscript,makeover_indicator]
501	[proc,makeover_indicator]
502	[clientscript,makeover_hair_setup]
503	[proc,league_rewards_draw]
504	[clientscript,bankmain_switchtab]
505	[proc,bankmain_finishbuilding]
506	[proc,bankmain_tabicon]
508	[proc,bankmain_tabicon_highlight]
511	[clientscript,bankmain_dragtab_text]
512	[clientscript,bankmain_dragtab_graphic]
513	[proc,bank_gettabrange]
514	[proc,bankmain_searching]
516	[proc,bank_tabforslot]
526	[clientscript,tooltip_mouserepeat]
527	[proc,settings_colour_input_update]
528	[proc,settings_buff_corruption]
534	[clientscript,steam_set_unlocked]
535	[clientscript,soul_wars_rewards_init]
538	[proc,soul_wars_rewards_statbuttondraw]
539	[proc,options_setfps]
540	[clientscript,ge_offers_bigbutton_init]
541	[proc,ge_offers_bigbutton]
542	[clientscript,ge_offers_bigbutton_op]
543	[clientscript,ge_offers_bigbutton_reset]
544	[proc,league_reward_draw_item]
545	[clientscript,wear_updateslot]
546	[proc,wear_updateslot]
547	[clientscript,runweight_visible]
548	[proc,meslayer_mode12]
549	[proc,meslayer_mode12_setprompt]
550	[proc,meslayer_mode13]
551	[proc,meslayer_mode13_setprompt]
552	[clientscript,chat_autotyper]
554	[clientscript,clanwars_confirm_setup]
555	[clientscript,clanwars_confirm_challenge_wait]
556	[clientscript,clanwars_confirm_challenge_op]
557	[clientscript,clanwars_confirm_challenge_redraw]
558	[proc,clanwars_confirm_challenge_redraw]
559	[clientscript,clanwars_confirm_join_op]
560	[proc,clanwars_confirm_pressed]
561	[proc,clanwars_confirm_notpressed]
562	[clientscript,clanwars_view_setup]
563	[clientscript,clanwars_view_op]
564	[proc,clanwars_view_setup]
565	[proc,clanwars_view_setbutton]
566	[proc,clanwars_view_updatebutton]
567	[clientscript,clanwars_view_spin]
568	[proc,clanwars_ffa_arena]
569	[clientscript,clanwars_ffa_init]
570	[clientscript,clanwars_ffa_hint]
571	[proc,clanwars_ffa_hint]
574	[proc,gnomeball_score]
576	[clientscript,clanwars_setup_scrollbox_init]
577	[proc,clanwars_setup_scrollbox_drawframe]
578	[clientscript,clanwars_setup_smallbox_init]
582	[clientscript,clanwars_setup_togglebox_init]
583	[clientscript,clanwars_setup_togglebox_op]
584	[clientscript,clanwars_setup_togglebox_update]
585	[proc,clanwars_setup_togglebox_update]
586	[clientscript,clanwars_setup_mouseleave]
587	[clientscript,clanwars_setup_corner_init]
588	[clientscript,clanwars_setup_corner_op]
589	[clientscript,clanwars_setup_corner_update]
590	[proc,clanwars_setup_corner_update]
592	[clientscript,clanwars_gameover]
593	[clientscript,clanwars_hud_init]
594	[clientscript,clanwars_hud_update]
595	[proc,clanwars_hud_info_update]
596	[proc,clanwars_hud_countdown_update]
597	[clientscript,clanwars_hud_countdown_tick]
598	[proc,clanwars_hud_countdown_redraw]
599	[clientscript,cc_text_swapper]
600	[clientscript,if_settextalign]
601	[proc,poll_storequestion]
602	[proc,poll_retrievequestion]
603	[clientscript,poll_initialise]
604	[clientscript,poll_setbutton]
605	[clientscript,poll_resetbutton]
606	[proc,poll_setbutton]
607	[clientscript,poll_buttonhover]
608	[clientscript,poll_buttonop]
611	[proc,poll_addlink]
612	[proc,poll_setuplink]
613	[clientscript,poll_clicklink]
614	[clientscript,poll_resetlink]
615	[clientscript,poll_placeholder_init]
616	[clientscript,poll_placeholder_pulse]
617	[proc,poll_placeholder_pulse]
618	[clientscript,poll_conclude]
619	[clientscript,poll_voting_addquestion_full]
620	[clientscript,poll_voting_addquestion_refresh]
621	[proc,poll_voting_addquestion]
624	[clientscript,poll_results_addquestion_full]
625	[clientscript,poll_results_addquestion_refresh]
626	[proc,poll_results_addquestion]
627	[clientscript,poll_history_setup]
628	[clientscript,poll_historyop]
629	[clientscript,poll_historyreset]
630	[clientscript,ignore_update]
631	[clientscript,friend_update]
633	[proc,int_to_bool]
634	[proc,displayname_draw]
635	[clientscript,displayname_lookup]
637	[proc,pet_insurance_drawlist]
638	[clientscript,pet_insurance_op]
639	[clientscript,poh_menagerie_initlist]
640	[clientscript,poh_menagerie_drawlist]
641	[proc,poh_menagerie_drawlist]
642	[proc,poh_menagerie_drawpet]
643	[clientscript,poh_menagerie_initroaming]
644	[clientscript,poh_menagerie_setroaming]
645	[proc,poh_menagerie_setroaming]
646	[clientscript,poh_menagerie_toggleroaming]
647	[clientscript,poh_menagerie_petlist]
648	[clientscript,poh_add_room_init]
649	[proc,poh_add_room_initbutton]
650	[clientscript,poh_add_room_costupdate]
651	[proc,poh_add_room_costupdate]
652	[clientscript,poh_add_room_op]
653	[proc,bankpin_keypad_set]
654	[clientscript,skill_guide_init]
655	[clientscript,skill_guide_build]
656	[proc,skill_guide_build]
657	[proc,skill_guide_addentry]
658	[proc,skill_guide_subsection_construction]
659	[proc,skill_guide_data_construction]
660	[proc,skill_guide_subsection]
661	[proc,skill_guide_data]
662	[proc,skill_guide_salamanders]
663	[clientscript,chat_onchattransmit]
665	[clientscript,rune_pouch_drawpouch]
666	[proc,rune_pouch_drawpouch]
667	[proc,rune_pouch_drawpouch_slot]
668	[clientscript,rune_pouch_pouchreorder]
669	[clientscript,rune_pouch_drawinventory]
670	[proc,rune_pouch_drawinventory]
671	[proc,rune_pouch_drawinventory_slot]
672	[clientscript,rune_pouch_inventoryreorder]
673	[proc,add]
677	[proc,meslayer_open]
678	[proc,meslayer_dynamicbutton]
679	[proc,bankpin_button_setup]
680	[proc,meslayer_initlastnames]
681	[proc,meslayer_enter]
682	[clientscript,meslayer_lastname_click]
683	[clientscript,bankpin_button_addtextlater]
684	[clientscript,bankpin_button_mouseover]
685	[clientscript,bankpin_button_op]
686	[clientscript,bankpin_otherbutton]
687	[clientscript,ironman_init]
688	[clientscript,trail_slidepuzzle_init]
689	[clientscript,trail_slidepuzzle_synch]
690	[proc,trail_slidepuzzle_synch]
691	[clientscript,trail_slidepuzzle_op]
692	[proc,trail_slidepuzzle_position]
693	[clientscript,corp_beast_damage]
694	[clientscript,makeovermage_gender]
695	[clientscript,makeovermage_colour_init]
696	[clientscript,makeovermage_colour_update]
697	[clientscript,makeovermage_colour_op]
698	[proc,makeovermage_colour_update]
699	[clientscript,emote_init]
700	[clientscript,emote_update]
701	[proc,emote_update]
702	[proc,emote_checkunlocked]
703	[clientscript,stoneborder]
704	[proc,wom_item_display]
705	[clientscript,bank_depositbox_drag]
706	[clientscript,ge_collect_all_draw]
707	[proc,ge_collect_all_draw]
709	[clientscript,build_achievement_list]
710	[clientscript,deadman_spectator_map_close]
711	[proc,stoneborder]
712	[clientscript,stonebox]
713	[proc,stonebox]
714	[clientscript,thinbox]
715	[proc,thinbox]
717	[clientscript,anma_reward_init]
718	[clientscript,object_swapper]
719	[clientscript,cc_object_swapper]
720	[clientscript,cc_fade]
721	[clientscript,cc_fade_timer]
722	[clientscript,bondif_convert_init]
723	[clientscript,bondif_convert_initialvalues]
724	[clientscript,bondif_convert_invtransmit]
725	[clientscript,bondif_convert_vartransmit]
726	[clientscript,bondif_convert_x]
727	[clientscript,bondif_convert_minus]
728	[clientscript,bondif_convert_plus]
729	[proc,bondif_convert_updatevalues]
730	[proc,bondif_convert_disableconfirm]
731	[proc,bondif_convert_enableconfirm]
734	[clientscript,bondif_redeem_init]
735	[clientscript,inferno_hud_init]
736	[clientscript,inferno_hud_pos]
737	[proc,inferno_hud_pos]
738	[clientscript,inferno_hud_update]
739	[proc,inferno_hud_update]
740	[proc,scrollbar_vertical_setdragger]
741	[clientscript,pest_activity_init]
742	[clientscript,seed_vault_size_check]
743	[clientscript,clanwars_hud_timeremaining_tick]
744	[proc,clanwars_hud_timeremaining_gettext]
745	[proc,quicksort_questlist]
747	[clientscript,worldswitcher_init]
748	[clientscript,output_wifiavailable]
749	[clientscript,tutorial_progressbar_init]
750	[clientscript,meslayer_mode14]
751	[clientscript,meslayer_mode14_update]
752	[proc,meslayer_mode14_update]
754	[clientscript,meslayer_mode14_select]
755	[clientscript,trade_main_init]
756	[clientscript,cc_flasher]
757	[clientscript,if_flasher]
758	[clientscript,duel_options_click]
759	[proc,duel_options_radio_buttons]
760	[proc,worldswitcher_sortbutton_draw]
761	[clientscript,worldswitcher_sortbutton_op]
762	[clientscript,trade_main_drawitems]
763	[proc,trade_main_drawitems]
764	[clientscript,duel_confirm_initworn]
765	[clientscript,trade_slot_changed]
766	[clientscript,trade_slot_flasher]
767	[clientscript,trade_confirm_redraw]
768	[proc,trade_confirm_redraw]
769	[proc,trade_confirm_itemtext]
770	[proc,stonepanel]
772	[clientscript,v2_stone_button_in_filled]
773	[proc,ge_offers_setup_init]
774	[proc,ge_offers_setup_buttontext]
775	[proc,ge_offers_setup_buttongraphic]
776	[clientscript,ge_offers_setup_draw]
777	[clientscript,ge_offers_setup_changequantity]
778	[clientscript,ge_offers_setup_changeprice]
779	[proc,ge_offers_setup_draw]
780	[proc,ge_offers_setup_draw_disableconfirm]
781	[clientscript,ge_offer_side_init]
782	[clientscript,ge_offer_side_draw]
783	[proc,ge_offer_side_draw]
784	[clientscript,ge_pricechecker_init]
785	[clientscript,ge_pricechecker_prices]
786	[clientscript,ge_pricechecker_redraw]
787	[proc,ge_pricechecker_redraw]
788	[clientscript,ge_collect_init]
789	[proc,ge_collect_initslot]
790	[clientscript,ge_collect_drawslot]
791	[proc,ge_collect_drawslot]
792	[proc,ge_collect_drawitem]
793	[proc,ge_offers_index_inittop]
794	[proc,ge_offers_index_initslot]
795	[clientscript,ge_offers_index_drawtop]
796	[proc,ge_offers_index_drawtop]
797	[clientscript,ge_offers_index_drawslot]
798	[proc,ge_offers_index_drawslot]
799	[clientscript,ge_pricelist]
800	[clientscript,ge_pricelist_draw]
801	[clientscript,ge_pricelist_switchmode]
802	[proc,ge_pricelist_draw]
803	[clientscript,ge_offers_init]
804	[clientscript,ge_offers_switchpanel]
806	[proc,ge_offers_switchpanel]
807	[proc,ge_offers_initboxes]
808	[clientscript,ge_offers_priceicon]
809	[proc,ge_offers_priceicon]
810	[proc,ge_textbox]
813	[proc,ge_offers_checkavailable]
814	[clientscript,confirmdestroy_init]
815	[proc,confirmdestroy_setupbutton]
822	[clientscript,itemsets_init]
823	[clientscript,itemsets_redraw]
824	[proc,itemsets_redraw]
825	[clientscript,itemsets_side_init]
826	[clientscript,itemsets_side_draw]
827	[proc,itemsets_side_draw]
828	[clientscript,playermember]
829	[clientscript,duel_confirm_worn]
831	[clientscript,zoom_init]
832	[clientscript,zoom_outline]
833	[clientscript,zoom_slider_ondrag]
834	[clientscript,if_setnoclickthrough]
835	[proc,options_button_off]
836	[proc,options_button_on]
837	[proc,tooltip_mouserepeat]
838	[proc,scrollbar_vertical_repaint]
839	[clientscript,bankmain_size_check]
840	[proc,bankmain_size_check]
841	[clientscript,worldswitcher_loading]
843	[proc,soul_wars_rewards_drawitem]
844	[proc,soul_wars_rewards_select]
845	[clientscript,soul_wars_rewards_select]
846	[clientscript,soul_wars_rewards_refuse]
847	[proc,soul_wars_rewards_drawconfirm]
849	[proc,soul_wars_rewards_confirmtext]
850	[clientscript,soul_wars_rewards_confirmop]
860	[clientscript,mes]
865	[clientscript,pvp_icons_layout_init]
867	[clientscript,tutorial_progressbar_set]
870	[clientscript,model_dynamiczoom_init]
871	[clientscript,model_dynamiczoom]
872	[proc,model_dynamiczoom]
873	[clientscript,wom_telescope_init]
874	[clientscript,wom_telescope_borders]
875	[proc,wom_telescope_borders]
876	[clientscript,cc_outline_on]
877	[proc,br_hud_writetimer]
878	[clientscript,boardgames_rank_draughts]
879	[clientscript,boardgames_rank_runelink]
880	[clientscript,boardgames_rank_runeversi]
881	[clientscript,boardgames_rank_runesquares]
882	[clientscript,gnomeball_score]
884	[clientscript,castlewars_hud_init]
887	[clientscript,castlewars_overlay_saradomin]
888	[clientscript,castlewars_overlay_zamorak]
889	[clientscript,ii_impling_count]
891	[clientscript,worldswitcher_draw]
892	[proc,worldswitcher_draw]
893	[proc,worldswitcher_wipe_favourite]
894	[clientscript,barrows_nodnod]
895	[clientscript,barrows_nodnodoff]
896	[clientscript,mole_go_splat]
897	[clientscript,mole_go_splat_off]
898	[proc,mole_splat_zoom]
899	[clientscript,agilityarena_overlay]
900	[proc,toplevel_getcomponents]
901	[clientscript,toplevel_init]
902	[clientscript,toplevel_vartransmit]
903	[clientscript,toplevel_subchange]
904	[clientscript,toplevel_resize]
905	[clientscript,toplevel_keypress]
906	[clientscript,toplevel_timer]
907	[proc,toplevel_redraw]
908	[proc,toplevel_subchange]
909	[proc,toplevel_resize]
910	[proc,toplevel_mainmodal_bg_calculate]
911	[proc,toplevel_mainmodal_bg_build]
912	[proc,toplevel_sidebuttons_enable]
913	[proc,toplevel_flashicon]
914	[clientscript,toplevel_sidebutton_op]
915	[clientscript,toplevel_sidebutton_switch]
916	[proc,toplevel_sidebutton_switch]
917	[clientscript,toplevel_mainmodal_background]
918	[proc,toplevel_check_sidepanel]
919	[proc,toplevel_side_customise]
920	[proc,toplevel_resize_customise]
921	[proc,toplevel_chatbox_maketransparent]
922	[proc,toplevel_chatbox_permittransparent]
923	[proc,toplevel_chatbox_background]
925	[clientscript,chatbox_init]
926	[clientscript,pm_init]
927	[proc,chatdefault_setonkey]
928	[clientscript,smokeoverlay_init]
929	[clientscript,bankmain_toggleplaceholders]
930	[proc,bankmain_toggleplaceholders]
931	[clientscript,snow_flakes_init]
932	[clientscript,snowflakes_zoom]
933	[proc,snowflakes_zoom]
934	[clientscript,myq3_blackout_init]
935	[clientscript,myq3_blackout_borders]
936	[proc,myq3_blackout_borders]
940	[clientscript,peng_observer_init]
941	[clientscript,peng_observer_borders]
942	[proc,peng_observer_borders]
946	[clientscript,zep_balloon_map_hover]
948	[clientscript,fade_overlay]
949	[clientscript,fade_overlay_update]
950	[proc,fade_overlay_increment]
951	[clientscript,fade_overlay_legacy]
952	[clientscript,fade_overlay_legacy_update]
953	[clientscript,macro_pinball_score_init]
956	[clientscript,macro_maze_timer]
960	[proc,worldswitcher_entry_draw]
962	[clientscript,tournament_supplies_init]
963	[clientscript,tournament_supplies_search_update]
964	[clientscript,tournament_supplies_search_toggle]
965	[proc,tournament_supplies_search_off]
966	[proc,tournament_supplies_search_on]
967	[proc,tournament_supplies_drawlist]
968	[clientscript,duel_options_changed]
969	[clientscript,stake_tooltip]
970	[clientscript,logout_op]
971	[clientscript,ntk_init]
972	[clientscript,deathkeep_init]
973	[clientscript,deathkeep_left_redraw]
974	[proc,deathkeep_left_redraw]
975	[proc,deathkeep_left_setsection]
976	[proc,deathkeep_left_protecteditem]
977	[proc,deathkeep_left_keepitem]
978	[proc,deathkeep_left_deleteditem]
979	[proc,deathkeep_left_graveitem]
980	[proc,deathkeep_left_coinsitem]
981	[clientscript,keybind_build_dropdown]
982	[proc,keybind_build_dropdown]
983	[clientscript,keybind_dropdown_select]
985	[clientscript,keybind_open_menu]
986	[proc,keybind_get_slot]
987	[clientscript,keybind_close_dropdown]
991	[clientscript,stonepanel]
993	[clientscript,xpdrops_init]
994	[clientscript,xpdrops_vartransmit]
995	[proc,xpdrops_redraw]
996	[proc,xpdrops_setdropsize]
997	[proc,xpdrops_showpanel]
998	[clientscript,xpdrops_hidepanel]
999	[proc,xpdrops_setbar]
1000	[proc,xpdrops_setbar_progress]
1001	[proc,xpdrops_percentage_tostring]
1002	[proc,xpdrops_data_get]
1003	[proc,xpdrops_setstatlistener]
1004	[clientscript,xpdrops_stattransmit]
1005	[clientscript,xpdrops_dropletmove]
1007	[proc,stat_totallevel]
1008	[proc,stats_totalxp]
1009	[proc,stats_tostring_totalxp]
1010	[clientscript,xpdrops_setup_init]
1011	[proc,xpdrops_setup_init_display]
1012	[clientscript,xpdrops_setup_display_hover]
1013	[proc,xpdrops_setup_display_hoveroff]
1014	[proc,xpdrops_setup_display_hoverdisable]
1021	[clientscript,xpdrops_setup_display_update]
1022	[proc,xpdrops_setup_display_update]
1028	[proc,xpdrops_tostring]
1029	[proc,xpdrops_tostring_thousands]
1030	[clientscript,xpdrops_setup_display_open]
1034	[clientscript,welcome_screen_lastlogin]
1035	[clientscript,xpdrops_setup_display_dropdown_hover]
1036	[clientscript,xpdrops_setup_display_dropdown_op]
1037	[clientscript,xpdrops_setup_display_dropdown_close]
1038	[proc,xpdrops_setup_display_dropdown_close]
1039	[clientscript,orbs_xpdrops_init]
1040	[clientscript,orbs_xpdrops_op]
1041	[clientscript,orbs_xpdrops_update]
1042	[proc,orbs_xpdrops_update]
1043	[clientscript,zoom_slider_dragcomplete]
1045	[proc,max]
1046	[proc,min]
1047	[clientscript,camera_zoom_slider_click]
1048	[proc,zoom_slider_specific_pos]
1049	[proc,zoom_position_slider]
1050	[clientscript,toplevel_compass_op]
1051	[clientscript,spinmodel]
1052	[clientscript,mourning_toad_init]
1053	[clientscript,mourning_toad_settoad]
1054	[proc,mourning_toad_settoad]
1055	[clientscript,farming_tools_main_init]
1056	[clientscript,farming_tools_side_init]
1057	[proc,farming_tools_main_create]
1058	[proc,farming_tools_side_create]
1059	[clientscript,farming_tools_main_update]
1060	[proc,farming_tools_main_update]
1061	[clientscript,farming_tools_side_update]
1062	[proc,farming_tools_side_update]
1063	[proc,farming_tools_getstored]
1064	[proc,farming_tools_getcarried]
1065	[clientscript,barrows_reward_init]
1066	[clientscript,barrows_reward_draw]
1067	[proc,barrows_reward_draw]
1068	[clientscript,saradomin_light_effect]
1069	[clientscript,saradomin_light_effect_update]
1070	[proc,saradomin_light_effect_update]
1071	[proc,barrows_overlay_createbrother]
1072	[clientscript,barrows_overlay_killbrother]
1073	[proc,barrows_overlay_killbrother]
1074	[clientscript,shop_main_init]
1075	[clientscript,shop_main_update]
1076	[proc,shop_main_update]
1077	[clientscript,poh_options_tele]
1078	[clientscript,poh_options_doors_onop]
1079	[clientscript,poh_options_tele_onop]
1081	[clientscript,openurl_raw]
1082	[clientscript,options_button_off]
1083	[clientscript,options_button_on]
1084	[clientscript,stonebutton_url]
1085	[clientscript,stonebutton_url_draw]
1086	[proc,stonebutton_url_draw]
1087	[clientscript,stonebutton_url_click]
1088	[clientscript,stonebutton_url_clickrelease]
1089	[clientscript,slayer_rewards_extend_init]
1090	[proc,slayer_rewards_unlock_draw]
1091	[clientscript,slayer_rewards_etcetera_draw]
1092	[proc,slayer_rewards_etcetera_draw]
1093	[clientscript,bugreport_init]
1094	[proc,bugreport_init_textinput]
1095	[clientscript,bugreport_info]
1096	[clientscript,bugreport_switch_textinput]
1097	[proc,bugreport_switch_textinput]
1098	[clientscript,bugreport_keypress]
1099	[proc,bugreport_parse]
1100	[clientscript,bugreport_drawbutton]
1101	[proc,bugreport_drawbutton]
1102	[clientscript,bugreport_continue]
1103	[clientscript,bugreport_submit]
1104	[clientscript,snapshot_init]
1105	[clientscript,skill_guide_sidepanelop]
1106	[proc,abyssalsire_overseer_update]
1110	[clientscript,league_statistics_init]
1111	[proc,league_statistics_draw]
1112	[clientscript,farming_view_init]
1113	[proc,farming_view_createtab]
1114	[clientscript,farming_view_optab]
1115	[clientscript,farming_view_updatetabs]
1116	[proc,farming_view_updatetabs]
1117	[proc,farming_view_tabpanel]
1118	[clientscript,farming_view_panel_loading]
1119	[clientscript,farming_view_setpanel]
1120	[clientscript,snapshot_rules_init]
1121	[proc,snapshot_rules_write]
1122	[clientscript,snapshot_rules_hover]
1123	[clientscript,snapshot_rules_send]
1125	[proc,league_points_earned]
1138	[proc,deadman_world]
1139	[clientscript,deadmanprotect_onload]
1140	[clientscript,deadman_stats_init]
1141	[clientscript,deadman_stats_vartransmit]
1142	[clientscript,deadman_stats_click]
1145	[proc,deadman_stats_set_protect]
1146	[proc,deadman_stats_set_unprotect]
1147	[proc,deadmanprotect_xp_counts]
1148	[clientscript,deadmanprotect_bank_transmit]
1149	[clientscript,deadman_delay]
1150	[clientscript,deadman_delay_timer]
1163	[clientscript,deadmanloot_onload]
1164	[clientscript,deadmanloot_itemnote_transmit]
1165	[clientscript,deadmanloot_itemnote_switch]
1166	[proc,deadmanloot_itemnote_update]
1167	[clientscript,deadman_lootall]
1168	[clientscript,deadman_lootall_end]
1169	[clientscript,deadmanloot_value_update]
1170	[clientscript,deadmanloot_inv_transmit]
1171	[proc,deadmanloot_inv_update]
1172	[clientscript,deadmanloot_tab_transmit]
1173	[clientscript,deadmanloot_inv_switch]
1174	[proc,deadmanloot_inv_switch]
1175	[proc,deadmanloot_inv_tabs_redraw]
1179	[clientscript,dream_monster_populate]
1180	[clientscript,dream_monster_switch_tab]
1181	[proc,dream_monster_switch_tab]
1182	[proc,deadman_skullduration]
1183	[clientscript,clancup_hud_init]
1184	[clientscript,clancup_hud_update]
1185	[proc,clancup_hud_update]
1186	[clientscript,kourend_sidepanel_init]
1187	[clientscript,kourend_sidepanel_hudtoggle]
1188	[clientscript,kourend_sidepanel_hudop]
1189	[proc,kourend_sidepanel_hudtoggle]
1191	[clientscript,thinstonebox]
1192	[proc,thinstonebox]
1193	[clientscript,hosidius_tithe_hud]
1194	[clientscript,hosidius_tithe_hud_update]
1195	[proc,hosidius_tithe_hud_update]
1203	[clientscript,zeah_favour_init]
1204	[proc,zeah_favour_init_tab]
1205	[clientscript,zeah_favour_set_tab]
1206	[proc,zeah_favour_set_tab]
1207	[clientscript,zeah_favour_tab_mouserepeat]
1208	[proc,zeah_favour_init_list]
1209	[proc,zeah_favour_add_list]
1210	[clientscript,zeah_favour_settitle]
1211	[proc,zeah_favour_settitle]
1212	[clientscript,zeah_favour_recolour_list]
1213	[proc,zeah_favour_recolour_list]
1214	[proc,zeah_favour_sort]
1215	[clientscript,zeah_favour_overlay_init]
1218	[clientscript,zeah_favour_overlay_hideshow]
1219	[proc,zeah_favour_overlay_hideshow]
1220	[clientscript,zeah_border_flash]
1221	[clientscript,zeah_fade_border]
1222	[clientscript,zeah_overlay_grow]
1223	[clientscript,zeah_overlay_grow_it]
1224	[clientscript,zeah_overlay_shrink]
1225	[clientscript,zeah_overlay_shrink_it]
1226	[clientscript,zeah_favour_update]
1227	[proc,kourend_sidepanel_build]
1228	[clientscript,kourend_sidepanel_update]
1229	[proc,kourend_sidepanel_update]
1230	[proc,kourend_sidepanel_tasks_default]
1231	[clientscript,kourend_sidepanel_tasks_op]
1232	[clientscript,kourend_sidepanel_tasks_reset]
1234	[proc,trade_tooltip]
1240	[clientscript,forget_green_counter]
1241	[clientscript,forget_yellow_counter]
1242	[clientscript,forget_init_tracks]
1243	[clientscript,forget_init_switches]
1244	[clientscript,forget_set_switches]
1245	[proc,forget_set_switch]
1261	[proc,deadman_tournament_fogcolour]
1262	[clientscript,deadman_tournament_refresh_init]
1263	[clientscript,deadman_tournament_refresh_reset]
1264	[proc,deadman_tournament_refresh_reset]
1265	[clientscript,deadman_tournament_refresh_op]
1266	[clientscript,deadman_tournament_sendnames]
1267	[clientscript,deadman_tournament_op]
1268	[clientscript,deadman_tournament_submit]
1269	[clientscript,bankmain_toggleplaceholders_op]
1274	[proc,deadmanprotect_createtab]
1275	[clientscript,deadmanprotect_settab]
1276	[proc,deadmanprotect_settab]
1277	[proc,deadmanprotect_stats_refresh_all]
1278	[proc,deadmanprotect_stats_refresh]
1283	166417
1284	166416
1285	[proc,colour_interpolate]
1286	[clientscript,glidermap_init]
1287	[clientscript,glidermap_hideshow]
1288	[proc,glidermap_hideshow]
1289	[clientscript,glidermap_fly]
1290	[proc,glidermap_fly]
1292	[clientscript,br_loadout_init]
1293	[clientscript,br_loadout_menuop]
1294	[clientscript,br_loadout_menureset]
1295	[clientscript,br_loadout_menudragging]
1297	[clientscript,slayer_rewards_unlock_draw]
1298	[proc,slayer_rewards_unlock_desc]
1300	[clientscript,soulbearer_repair]
1301	[clientscript,soulbearer_repair_update]
1302	[proc,soulbearer_repair_update]
1303	[clientscript,tog_sidepanel_init]
1304	[clientscript,tog_sidepanel_tears]
1305	[proc,tog_sidepanel_tears]
1306	[clientscript,tog_sidepanel_timer]
1307	[clientscript,tog_sidepanel_timer_update]
1308	[clientscript,ge_history_mouseeffect]
1309	[clientscript,poh_options_viewer_init]
1310	[clientscript,poh_options_viewer_set]
1311	[proc,tog_sidepanel_timer]
1313	[clientscript,cata_altar_update]
1314	[clientscript,jar_of_darkness]
1315	[clientscript,jar_of_darkness_update]
1316	[proc,jar_of_darkness_update]
1318	[clientscript,bankmain_incinerator_enableop]
1319	[clientscript,bankmain_incinerator_close]
1320	[proc,stat_f2plevel]
1324	[clientscript,snapshot_mute]
1325	[clientscript,snapshot_ignore]
1326	[proc,snapshot_toggle]
1327	[clientscript,ii_tracker_build]
1328	[proc,ii_tracker_build_split]
1329	[proc,ii_tracker_build_element]
1330	[clientscript,ii_tracker_tab_select]
1331	[proc,ii_tracker_tab_select]
1333	[clientscript,light_puzzle_init]
1334	[proc,light_puzzle_button_setup]
1335	[clientscript,light_puzzle_text_mouseover]
1336	[clientscript,light_puzzle_op_button]
1337	[clientscript,light_puzzle_button_reset]
1338	[clientscript,light_puzzle_update]
1339	[proc,light_puzzle_update]
1340	[proc,quest_f2p_count]
1341	[proc,quicksort_componenttext]
1342	[proc,quicksort_componentenum]
1345	[clientscript,br_fogcolour]
1346	[proc,br_fogcolour]
1347	[clientscript,br_hud_init]
1349	[proc,settings_buff_resurrection]
1350	[clientscript,questlist_init]
1351	[proc,questlist_sectioninit]
1352	[proc,questlist_sort]
1353	[clientscript,questlist_showprogress]
1354	[proc,questlist_showprogress]
1355	[clientscript,questlist_qp]
1356	[proc,questlist_qp]
1357	[clientscript,mesoverlay_exit]
1360	[proc,quicksort_intenum]
1361	[proc,welcome_screen_lastlogin]
1362	[clientscript,welcome_screen_messages]
1363	[clientscript,br_loadout_drawloadout_request]
1364	[clientscript,barbassault_reward_shop_init]
1375	[proc,poh_options_viewer_set]
1376	[clientscript,poh_viewer_setroom]
1377	[proc,poh_viewer_roomtooltip]
1378	[clientscript,poh_viewer_room_indicator]
1379	[clientscript,poh_viewer_room_op]
1380	[proc,poh_viewer_room_indicator]
1381	[clientscript,poh_viewer_room_hover]
1384	[clientscript,poh_viewer_preview_position]
1388	[clientscript,poh_viewer_setlevel]
1389	[proc,poh_viewer_setlevel]
1390	[proc,poh_viewer_handlelevel]
1393	[clientscript,poh_viewer_preview_pulse]
1394	[proc,poh_viewer_preview_pulse]
1395	[clientscript,poh_viewer_sideinit]
1396	[clientscript,poh_viewer_sideupdate]
1397	[proc,poh_viewer_sideupdate]
1398	[proc,poh_viewer_disablebutton]
1399	[proc,poh_viewer_enablebutton]
1400	[proc,poh_viewer_disablerotate]
1401	[proc,poh_viewer_enablerotate]
1402	[clientscript,poh_viewer_opbutton]
1403	[clientscript,poh_viewer_rotate_timeout]
1408	[clientscript,champions_log]
1409	[proc,champions_log_build]
1410	[proc,inputstring_televertical]
1411	[clientscript,questdisplay]
1412	[proc,questdisplay_setup]
1414	[proc,hp_hud_update_bar]
1415	[clientscript,nightmare_portal_remove]
1418	[clientscript,br_tournament_sendnames]
1419	[clientscript,br_tournament_op]
1420	[clientscript,br_tournament_submit]
1421	[clientscript,wint_update]
1422	[clientscript,blast_furnace_hud_init]
1423	[clientscript,blast_furnace_hud_update]
1424	[proc,blast_furnace_hud_update]
1425	[proc,minmax]
1426	[clientscript,tol_pressure_leverop]
1427	[clientscript,tol_pressure_init]
1432	[clientscript,wint_outside_event]
1433	[clientscript,wint_inside_event]
1435	[clientscript,cws_init]
1436	[proc,cws_init_button]
1438	[clientscript,cws_set_toggle]
1439	[proc,cws_set_toggle]
1440	[clientscript,cws_response_toggle]
1441	[clientscript,duel_wait_button]
1442	[proc,duel_wait_button]
1443	[clientscript,duel_accept_button]
1444	[proc,duel_colour_variation]
1445	[clientscript,duel_options_exclamations]
1446	[proc,playermember]
1449	[clientscript,duel_stake_exclamations]
1450	[clientscript,duel_stake_changed]
1451	[proc,duel_check_button]
1454	[clientscript,zmi_bank_payment_init]
1455	[clientscript,bank_payment_draw]
1456	[proc,bank_payment_draw]
1458	[clientscript,zmi_bank_payment_selection_reset]
1459	[clientscript,agilityarena_rewards_draw]
1460	[clientscript,lumbridge_alchemy_init]
1461	[clientscript,lumbridge_alchemy_invtransmit]
1462	[proc,lumbridge_alchemy_invtransmit]
1463	[clientscript,lumbridge_alchemy_vartransmit]
1464	[proc,lumbridge_alchemy_vartransmit]
1475	[clientscript,hidey_build]
1476	[clientscript,hidey_buildlists]
1477	[proc,hidey_buildlists]
1478	[proc,hidey_build_tab]
1479	[proc,hh_get_status]
1480	[clientscript,hidey_tabbuttons]
1481	[proc,hidey_tabbuttons]
1482	[proc,hidey_tabbuttons_draw]
1483	[clientscript,hidey_tabbuttons_hover]
1484	[proc,trail_slidepuzzle_getint]
1485	[proc,trail_slidepuzzle_setint]
1491	[proc,quicksort_cctext]
1492	[clientscript,orbs_worldmap_init]
1493	[clientscript,pest_activity_update]
1494	[proc,pest_activity_update]
1496	[clientscript,agilityarena_rewards_setgracefulbutton]
1497	[proc,agilityarena_rewards_setgracefulbutton]
1503	[proc,string_prependzero]
1505	[clientscript,raids_overlay_init]
1510	[clientscript,raids_overlay_databox]
1511	[proc,raids_overlay_databox]
1512	[clientscript,raids_fadeout]
1513	[clientscript,raids_fadein]
1514	[clientscript,raids_fader_update]
1515	[proc,raids_fader_update]
1516	[clientscript,raids_partydetails_init]
1517	[clientscript,raids_partydetails_addline]
1518	[proc,raids_partydetails_entry_init]
1519	[clientscript,raids_partydetails_entry_setup]
1520	[proc,raids_partydetails_entry_setup]
1521	[clientscript,raids_partydetails_entry_op]
1522	[clientscript,raids_partydetails_closestats]
1523	[proc,raids_partydetails_closestats]
1525	[proc,raids_partydetails_setsort]
1526	[proc,raids_partydetails_sort]
1527	[proc,raids_partydetails_sortbutton_draw]
1529	[clientscript,raids_partydetails_clicksort]
1538	[clientscript,raids_sidepanel_init]
1539	[clientscript,raids_sidepanel_data]
1540	[proc,raids_sidepanel_data]
1541	[clientscript,raids_sidepanel_drawbutton]
1542	[proc,raids_sidepanel_drawbutton]
1543	[clientscript,raids_sidepanel_opbutton]
1544	[clientscript,raids_sidepanel_resetbutton]
1545	[clientscript,raids_sidepanel_refresh]
1546	[proc,raids_sidepanel_refresheffect]
1547	[clientscript,raids_sidepanel_initlines]
1548	[clientscript,raids_sidepanel_addline]
1549	[proc,raids_sidepanel_entry_init]
1550	[clientscript,raids_sidepanel_entry_setup]
1551	[proc,raids_sidepanel_entry_setup]
1552	[clientscript,raids_sidepanel_entry_op]
1554	[proc,raids_sidepanel_setsort]
1555	[proc,raids_sidepanel_sort]
1556	[proc,raids_sidepanel_sortbutton_draw]
1557	[clientscript,raids_sidepanel_clicksort]
1563	[clientscript,raids_partylist_init]
1564	[clientscript,raids_partylist_setpartybutton]
1565	[proc,raids_partylist_setpartybutton]
1566	[clientscript,raids_partylist_addline]
1567	[proc,raids_partylist_entry_init]
1568	[clientscript,raids_partylist_op]
1569	[clientscript,raids_partylist_reset]
1570	[proc,raids_partylist_setsort]
1571	[proc,raids_partylist_sort]
1572	[proc,raids_partylist_sortbutton_draw]
1573	[clientscript,raids_partylist_clicksort]
1574	[proc,raids_partylist_age_init]
1575	[proc,raids_partylist_age_write]
1576	[clientscript,raids_partylist_age_update]
1577	[clientscript,barrows_overlay_reward]
1578	[proc,barrows_overlay_reward]
1579	[clientscript,raids_rewards_init]
1580	[clientscript,raids_rewards_build]
1581	[proc,raids_rewards_build]
1582	[proc,raids_rewards_drawitem]
1583	[clientscript,raids_rewards_op]
1584	[clientscript,killcounts]
1589	[clientscript,raids_storage_shared_init]
1590	[clientscript,raids_storage_shared_warning]
1591	[clientscript,raids_storage_shared_items]
1592	[proc,raids_storage_shared_items]
1593	[clientscript,displayname_statusflash]
1594	[proc,displayname_statusstatic]
1595	[proc,agilityarena_rewards_draw]
1596	[clientscript,silver_crafting_init]
1597	[clientscript,deadman_delay_abortion]
1598	[proc,skill_guide_subsection_hunter]
1599	[proc,skill_guide_data_hunter]
1600	[proc,adventurepath_replace]
1601	[clientscript,button_select_sound]
1602	[proc,raids_storage_shared_warning]
1603	[clientscript,adventurepath_voucher_op_init]
1604	[clientscript,raids_storage_private_prompt]
1605	[proc,raids_storage_private_prompt]
1606	[clientscript,raids_storage_private_items]
1607	[proc,raids_storage_private_items]
1608	[proc,raids_storage_private_drawitem]
1609	[clientscript,raids_storage_private_dragscroll]
1610	[clientscript,raids_storage_private_reorder]
1611	[proc,raids_storage_drawbutton]
1612	[clientscript,raids_storage_opbutton]
1613	[clientscript,raids_storage_resetbutton]
1614	[clientscript,raids_storage_side_init]
1615	[clientscript,raids_storage_side_setup]
1616	[proc,raids_storage_side_setup]
1617	[proc,raids_storage_side_dismiss_enable]
1618	[clientscript,raids_storage_side_dismiss]
1619	[clientscript,raids_storage_side_dismiss_reset]
1620	[clientscript,raids_storage_side_items]
1621	[proc,raids_storage_side_items]
1622	[proc,raids_storage_side_private_drawitem]
1623	[clientscript,raids_storage_side_reorder]
1624	[proc,raids_storage_side_warning]
1625	[proc,glidermap_showglider]
1626	[proc,glidermap_hideglider]
1628	[proc,storage_main_update_vartransmit]
1629	[proc,storage_getstored]
1630	[proc,storage_getcarried]
1633	[clientscript,motherlode_hud_init]
1634	[clientscript,motherlode_hud_update]
1635	[proc,motherlode_hud_update]
1637	[clientscript,deadman_tournament_timer_resynch]
1638	[proc,deadman_tournament_timer_resynch]
1639	[clientscript,deadman_tournament_timer_update]
1640	[proc,deadman_tournament_timer_update]
1641	[clientscript,deadman_tournament_hudupdate]
1642	[proc,deadman_tournament_hudupdate]
1643	[clientscript,deadman_finallocation]
1644	[clientscript,ge_history_init]
1645	[clientscript,ge_history_addline]
1646	[clientscript,ge_history_finish]
1649	[proc,storage_main_create]
1650	[clientscript,storage_main_recreate]
1651	[clientscript,confirmdestroy_triggerbutton]
1652	[clientscript,confirmdestroy_resetbutton]
1653	[proc,ignore_sortbutton_draw]
1654	[clientscript,ignore_sortbutton_clicksort]
1656	[clientscript,chatchannel_current_init]
1657	[clientscript,chatchannel_current_build]
1658	[proc,chatchannel_current_build]
1660	[clientscript,chatchannel_current_sortbutton_clicksort]
1661	[clientscript,keyboard_key]
1662	[clientscript,chatchannel_setup_init]
1663	[clientscript,chatchannel_setup_build]
1664	[proc,chatchannel_setup_build]
1666	[clientscript,chatchannel_setup_sortbutton_clicksort]
1667	[clientscript,chatchannel_setup_setrank]
1669	[proc,friends_sortbutton_draw]
1670	[clientscript,friends_sortbutton_clicksort]
1672	[proc,displayname_button_init]
1673	[clientscript,displayname_button_op]
1674	[clientscript,displayname_button_reset]
1675	[clientscript,displayname_button_draw]
1676	[proc,displayname_button_draw]
1677	[proc,displayname_disablebutton]
1685	[clientscript,poh_jewellery_box_init]
1686	[proc,poh_jewellery_box_prepbox]
1687	[proc,poh_jewellerybox_getbuttonspacing]
1688	[proc,poh_jewellery_box_addbutton]
1689	[clientscript,poh_jewellery_box_keypress]
1690	[clientscript,poh_jewellery_box_op]
1691	[proc,poh_jewellery_box_activate]
1692	[clientscript,poh_jewellery_box_timeout]
1696	[proc,agilityarena_rewards_initbutton]
1697	[proc,magic_spacer_tostring]
1698	[clientscript,blast_mining_hud_init]
1699	[clientscript,orbs_worldmap_setup]
1700	[proc,orbs_worldmap_setup]
1701	[proc,keypress_permit]
1702	[proc,worldmap_elements_update]
1703	3338
1704	3594
1707	[clientscript,worldmap_init]
1708	[clientscript,worldmap_maplist_open]
1709	[clientscript,worldmap_maplist_close]
1710	[proc,worldmap_maplist_close]
1711	[clientscript,worldmap_maplist_select]
1712	[proc,worldmap_loadmap]
1714	[clientscript,worldmap_overview_click]
1715	[proc,worldmap_findcoordinmap]
1716	[proc,worldmap_toggles_createbutton]
1717	[proc,worldmap_toggles_enablebutton]
1718	[clientscript,worldmap_toggles_op]
1719	[clientscript,worldmap_toggles_vartransmit]
1720	[proc,worldmap_toggles_set]
1721	[proc,worldmap_applyzoom]
1722	[proc,worldmap_zoombutton_build]
1723	[clientscript,worldmap_zoombutton_draw]
1724	[proc,worldmap_zoombutton_draw]
1725	[clientscript,worldmap_zoom_scroll]
1726	[clientscript,worldmap_zoom_adjust]
1727	[proc,worldmap_zoom_adjust]
1728	[clientscript,worldmap_keyflash]
1729	[clientscript,worldmap_keyreset]
1730	[proc,worldmap_keybutton]
1731	[proc,worldmap_keysort]
1732	[clientscript,worldmap_key_toggle]
1733	[proc,worldmap_key_toggle]
1734	[clientscript,worldmap_search_off]
1735	[proc,worldmap_search_off]
1736	[clientscript,worldmap_search_start]
1737	[clientscript,worldmap_search_input]
1738	[proc,worldmap_search_update]
1740	[clientscript,worldmap_size_handler]
1741	[proc,worldmap_size_handler]
1742	[clientscript,worldmap_overview_toggle]
1743	[proc,worldmap_overview_toggle]
1744	[clientscript,worldmap_hovertoggle]
1745	[proc,worldmap_window_set]
1747	[clientscript,worldmap_window_drag_preview]
1748	[clientscript,worldmap_window_drag_end]
1749	[clientscript,worldmap_transmitdata]
1750	[clientscript,worldmap_overlay]
1751	[proc,worldmap_overlay_clear]
1752	[proc,worldmap_elements_chooseposition]
1757	[proc,worldmap_elements_marker]
1760	17
1761	273
1762	529
1763	785
1764	1041
1765	1297
1767	1809
1768	2065
1769	2321
1770	2577
1771	2833
1772	3089
1773	3345
1774	3601
1775	3857
1776	4113
1777	4369
1778	4625
1779	4881
1780	5137
1781	5393
1782	5649
1783	5905
1784	6161
1785	6417
1786	6673
1787	6929
1788	7185
1789	7441
1790	7697
1791	7953
1792	8209
1793	8465
1794	8721
1795	8977
1796	9233
1797	9489
1798	9745
1799	10001
1800	10257
1801	10513
1802	10769
1803	11025
1804	11281
1805	11537
1806	11793
1807	12049
1808	12305
1809	12561
1810	12817
1811	13073
1812	13329
1813	13585
1814	13841
1815	14097
1816	14353
1817	[clientscript,keyboard_show_string]
1818	14865
1819	15121
1820	15377
1821	15633
1822	15889
1823	16145
1824	16401
1825	16657
1826	16913
1827	17169
1828	17425
1829	17681
1830	17937
1831	18193
1832	18449
1833	18705
1834	18961
1835	19217
1836	19473
1837	19729
1838	[proc,keyboard_show_string]
1839	20241
1840	20497
1841	20753
1842	21009
1843	21265
1844	21521
1845	21777
1846	22033
1847	[proc,worldmap_element_tooltip]
1848	16
1849	272
1850	528
1851	784
1852	1040
1853	1296
1855	1808
1856	2064
1857	2320
1858	2576
1859	2832
1860	3088
1861	3344
1862	3600
1863	3856
1864	4112
1865	4368
1866	4624
1867	4880
1868	5136
1869	5392
1870	5648
1871	5904
1872	6160
1873	6416
1874	6672
1875	6928
1876	7184
1877	7440
1878	7696
1879	7952
1880	8208
1881	8464
1882	8720
1883	8976
1884	9232
1885	9488
1886	9744
1887	10000
1888	10256
1889	10512
1890	10768
1891	11024
1892	11280
1893	11536
1894	11792
1895	12048
1896	12304
1897	12560
1898	12816
1899	13072
1900	13328
1901	13584
1902	13840
1903	14096
1904	14352
1905	[clientscript,bugreport_textinput]
1906	14864
1907	15120
1908	15376
1909	15632
1910	15888
1911	16144
1912	16400
1913	16656
1914	16912
1915	17168
1916	17424
1917	17680
1918	17936
1919	18192
1920	18448
1921	18704
1922	18960
1923	19216
1924	19472
1925	19728
1927	20240
1928	20496
1929	20752
1930	21008
1931	21264
1932	21520
1933	21776
1934	22032
1935	[proc,instance_check]
1937	[clientscript,bookofscrolls_init]
1938	[clientscript,bookofscrolls_populate]
1939	[proc,bookofscrolls_populate]
1940	[clientscript,blast_mining_hud_update]
1941	[proc,blast_mining_hud_update]
1942	[proc,wildwars_world]
1943	[clientscript,wildwars_hudupdate]
1944	[proc,wildwars_hudupdate]
1945	[clientscript,wildwars_hud_init]
1956	[clientscript,wildwars_fogcolour]
1957	[proc,wildwars_fogcolour]
1958	[proc,storage_main_recreate]
1959	[proc,storage_side_create]
1960	[clientscript,storage_side_recreate]
1961	[proc,storage_side_recreate]
1965	[clientscript,storage_main_create_quantitybutton]
1966	[clientscript,tol_pressure_draw]
1967	[clientscript,vm_info_init]
1968	[proc,vm_kudos_get_status]
1969	[proc,vm_kudos_get_quest_status]
1970	[proc,deadman_lobby_tele]
1971	[proc,text_device]
1972	[proc,on_mobile]
1974	[clientscript,mesoverlay]
1980	[clientscript,keyboard_show]
1981	[clientscript,vm_info_draw]
1982	[proc,vm_info_draw]
1983	[proc,keyboard_show]
1984	[proc,keyboard_hide]
1985	[clientscript,gravestone_retrieval_init]
1986	[clientscript,gravestone_retrieval_unlockstatus]
1987	[proc,gravestone_retrieval_unlockstatus]
1988	[clientscript,gravestone_retrieval_buttonhover]
1989	[clientscript,gravestone_retrieval_buttonop]
1990	[clientscript,gravestone_retrieval_buttonreset]
1991	[clientscript,gravestone_retrieval_items]
1992	[proc,gravestone_retrieval_items]
1993	[clientscript,fossilquest_voyage]
1994	[clientscript,fossilquest_progress]
1996	[clientscript,fossilquest_voyage_end]
1997	[clientscript,fossil_oxygen_level]
1998	[clientscript,fossil_init]
1999	[clientscript,fossil_warning_flasher]
2000	[clientscript,fossil_fade_overlay]
2001	[clientscript,fossil_fade_overlay_update]
2002	[proc,fossil_fade_overlay_increment]
2003	[clientscript,fossil_swamp_splat]
2004	[proc,storage_main_create_quantitybutton]
2006	[clientscript,fossil_storage_init]
2007	[clientscript,fossil_storage_build]
2008	[proc,fossil_storage_build]
2009	[proc,fossil_storage_get]
2010	[clientscript,fossil_volcanic_shop_init]
2011	[clientscript,fossil_volcanic_shop_points]
2012	[proc,fossil_volcanic_shop_points]
2013	[clientscript,fossil_pool_init]
2014	[clientscript,fossil_pool_update]
2015	[clientscript,fossil_pool_progress_update]
2016	[clientscript,volcanic_mine_hud_init]
2017	[clientscript,mesoverlay_checkheight]
2018	[proc,mesoverlay_draw]
2019	[clientscript,volcanic_mine_updatehud]
2020	[proc,volcanic_mine_set_venttext]
2021	[proc,volcanic_mine_set_chambertext]
2022	[proc,volcanic_mine_set_otherinfo]
2023	[proc,volcanic_mine_ventcolour]
2024	[proc,volcanic_mine_chambercolour]
2025	[clientscript,volcanic_mine_setup_timer]
2032	[clientscript,agilityarena_rewards_hidelayer]
2033	[proc,agilityarena_rewards_gracefulbutton_init]
2034	[clientscript,agilityarena_rewards_gracefulbutton_draw]
2035	[proc,agilityarena_rewards_gracefulbutton_draw]
2036	[proc,agilityarena_rewards_gracefulbutton_drawcomponents]
2037	[clientscript,agilityarena_rewards_gracefulcancel]
2038	[proc,agilityarena_rewards_gracefulcancel]
2039	[clientscript,aide_map_init]
2040	[clientscript,aide_map_tutors]
2041	[proc,aide_map_tutors]
2042	[clientscript,aide_map_marker]
2043	[proc,aide_map_marker]
2044	[proc,beige_stone_button_in]
2045	[proc,beige_stone_button_out]
2046	[clientscript,skillmulti_setup]
2047	[proc,skillmulti_wipebutton]
2048	[proc,skillmulti_itembutton_init]
2049	[clientscript,skillmulti_itembutton_hover]
2050	[clientscript,skillmulti_itembutton_op]
2051	[clientscript,skillmulti_itembutton_key]
2052	[proc,skillmulti_itembutton_triggered]
2053	[clientscript,skillmulti_itembutton_reset]
2054	[proc,skillmulti_itembutton_draw]
2055	[proc,skillmulti_quantitybuttons_set]
2056	[proc,skillmulti_quantitybutton_setup]
2057	[clientscript,skillmulti_quantitybutton_draw]
2058	[proc,skillmulti_quantitybutton_draw]
2059	[clientscript,skillmulti_quantitybutton_op]
2060	[clientscript,skillmulti_quantitybutton_x_start]
2061	[proc,skillmulti_quantitybutton_x_receive]
2062	[clientscript,skillmulti_quantitybutton_x_end]
2063	[proc,meslayer_skillmulti]
2064	[clientscript,chat_broadcast_op]
2065	[clientscript,chat_recol]
2066	[proc,chat_broadcast_parseurl]
2067	[proc,v2_stone_button_in]
2068	[proc,toplevel_keypress]
2069	[clientscript,orbs_update_specenergy]
2070	[clientscript,deadman_spectator_enable]
2071	[clientscript,deadman_spectator_update]
2072	[proc,deadman_spectator_chat]
2073	[clientscript,sailing_menu_init]
2074	[clientscript,sailing_menu_filter]
2075	[proc,sailing_menu_filter]
2077	[clientscript,vm_kudos_init]
2078	[clientscript,vm_kudos_update]
2079	[proc,vm_kudos_update]
2081	[clientscript,magic_flash_spell]
2082	[proc,kots_world]
2083	[proc,kots_remaining_time]
2084	[proc,deadman_finallocation]
2087	[clientscript,tob_hud_init]
2088	[clientscript,trail_rewardscreen_init]
2089	[clientscript,trail_rewardscreen_draw]
2090	[proc,trail_rewardscreen_draw]
2091	[clientscript,xpdrops_fake]
2092	[clientscript,oculus_flashers]
2094	[proc,ironman_modebutton_init]
2095	[clientscript,ironman_modebutton_redraw]
2096	[proc,ironman_modebutton_redraw]
2097	[clientscript,autocast_setup]
2098	[proc,autocast_setup]
2099	[clientscript,hp_hud_init]
2100	[clientscript,hp_hud_pos]
2101	[proc,hp_hud_pos]
2102	[clientscript,hp_hud_update]
2103	[proc,hp_hud_update]
2104	[clientscript,ds2_fossil_map_init]
2105	[clientscript,ds2_fossil_map_redraw]
2106	[proc,ds2_fossil_map_redraw]
2107	[clientscript,ds2_fossil_map_rotate]
2108	[clientscript,ds2_fossil_map_swap]
2109	[clientscript,ds2_fossil_map_stuck]
2110	[proc,ds2_fossil_map_getslot]
2111	[proc,ds2_fossil_map_setslot]
2112	[clientscript,storage_main_update]
2115	[clientscript,ironman_modebutton_op]
2120	[clientscript,ironman_miscellaneous_redraw]
2121	[proc,ironman_miscellaneous_redraw]
2132	[clientscript,fossil_driftnet_init]
2140	[clientscript,fossil_driftnet_store_init]
2141	[clientscript,fossil_driftnet_store_update]
2142	[proc,fossil_driftnet_store_update]
2146	[proc,storage_main_update]
2147	[clientscript,storage_side_update]
2148	[proc,storage_side_update]
2151	[proc,graphic_device]
2152	[clientscript,chatbox_keyinput_clicklistener]
2153	[proc,chatbox_keyinput_matched]
2154	[clientscript,logout_layout_init]
2155	[clientscript,toplevel_chatbox_resetbackground_soon]
2156	[proc,poh_options_doors_render]
2157	[proc,chatdefault_stopinput]
2158	[clientscript,chatdefault_restoreinput]
2163	[proc,makeover_unlockedcolour]
2164	[clientscript,xpdrops_setposition]
2165	[proc,xpdrops_setposition]
2174	[proc,tournament_beta_world]
2176	[clientscript,logout_layout_update]
2177	[proc,setvolumesounds]
2178	[proc,setvolumeareasounds]
2179	[proc,deadman_spectator_names]
2180	[clientscript,deadman_spectator_single]
2181	[clientscript,deadman_spectator_double]
2182	[proc,deadman_spectator_stats]
2183	[proc,deadman_spectator_inventory]
2184	[proc,deadman_spectator_inventory_dummy]
2185	[clientscript,deadman_spectator_map]
2186	[proc,spectator_keypress]
2187	[proc,deadman_freecam_enable]
2188	[proc,deadman_overlay_hide]
2189	[proc,deadman_map_enable]
2190	[proc,deadman_area_switch]
2193	[proc,deadman_arenas_tele]
2195	[proc,scale_colour]
2196	[proc,current_date_time]
2197	[proc,setup_diary_entry]
2198	[clientscript,update_diary_entry]
2199	[proc,update_diary_entry]
2200	[proc,diary_completion_info]
2201	[clientscript,diary_mouseover]
2202	[clientscript,deadman_spectator_temp_name]
2203	[proc,deadman_map_keypress]
2204	[proc,deadman_1v1_tele]
2205	[clientscript,decant_init]
2206	[proc,decant_button]
2214	[proc,welcome_screen_messages]
2215	[clientscript,welcome_screen_credit]
2216	[proc,welcome_screen_credit]
2219	[proc,oculus_flashers]
2220	[clientscript,oculus_controls]
2221	[clientscript,set_renderself]
2225	[clientscript,bankmain_quantity_op]
2226	[clientscript,decant_mouseover]
2227	[proc,decant_mouseover]
2229	[clientscript,shop_quantity]
2230	[proc,shop_quantity]
2231	[clientscript,shop_quantity_op]
2232	[clientscript,magic_flash]
2233	[proc,welcome_screen_continue_text]
2234	[clientscript,camera_do_zoom]
2238	[proc,setbrightness]
2239	[clientscript,keyboard_hide]
2240	[clientscript,collection_init]
2243	[proc,logout_layout_update]
2244	[clientscript,logout_button_text]
2245	[proc,quest_members_count]
2246	[proc,hp_bar_reset]
2247	[proc,macro_pinball_score]
2248	[proc,deadman_mode]
2249	[clientscript,cc_deleteall]
2251	[clientscript,chatbox_open_input]
2252	[clientscript,bugreport_home]
2253	[clientscript,bugreport_next]
2254	[clientscript,bugreport_back]
2255	[proc,music_sort]
2256	[proc,zoom_init]
2257	[clientscript,music_init_counter]
2261	[proc,steam_set_unlocked]
2262	[clientscript,magic_spellbook_init]
2263	[proc,tutorial_progressbar_set]
2264	[proc,worldmap_mobile_layout]
2265	[proc,miniquest_count]
2268	[clientscript,text_device]
2269	[clientscript,telenexus_init]
2274	[proc,v2_stone_button_fadable]
2276	[proc,misc_collection_draw]
2277	[clientscript,bondif_redeem_subtitle]
2278	[clientscript,bondif_redeem_package_init]
2279	[clientscript,bondif_redeem_package_op_disabled]
2280	[clientscript,bondif_redeem_package_op_permitted]
2281	[clientscript,bondif_redeem_package_draw]
2282	[proc,bondif_redeem_package_draw]
2283	[proc,bondif_redeem_package_details]
2285	[clientscript,bondif_redeem_rightcolumn]
2286	[proc,bondif_redeem_rightcolumn]
2287	[clientscript,bondif_redeem_hover]
2288	[clientscript,bondif_redeem_fadeend]
2289	[clientscript,raids_overlay_timer]
2290	[clientscript,raids_challenge_build_list]
2291	[clientscript,raids_challenge_move_highlight]
2292	[clientscript,raids_score_display]
2293	[clientscript,raids_challenge_checkbox]
2294	[proc,raids_challenge_checkbox]
2295	[proc,tob_hud_status_init]
2296	[clientscript,tob_hud_draw]
2297	[proc,tob_hud_draw]
2298	[proc,tob_hud_statusrefresh]
2301	[clientscript,tob_hud_statusnames]
2302	[clientscript,tob_hud_nameflasher]
2303	[proc,tob_hud_nameflasher]
2304	[clientscript,tob_hud_updateprogress]
2305	[proc,tob_hud_updateprogress]
2306	[clientscript,tob_hud_fade]
2307	[clientscript,tob_hud_portal]
2309	[proc,tob_hud_fade]
2313	[proc,tob_hud_setportal]
2314	[clientscript,tob_hud_playportal]
2315	[clientscript,tob_hud_cleartext]
2316	[clientscript,tob_partydetails_init]
2317	[clientscript,tob_partydetails_addmember]
2319	[clientscript,tob_partydetails_kickmember]
2320	[clientscript,tob_partydetails_timeout_kickmember]
2321	[clientscript,tob_partydetails_addapplicant]
2329	[clientscript,tob_partydetails_forcebutton]
2332	[clientscript,tob_partydetails_autorefresh]
2333	[proc,tob_partydetails_setsort]
2334	[proc,tob_partydetails_sort]
2335	[proc,tob_partydetails_sortbutton_draw]
2336	[clientscript,tob_partydetails_clicksort]
2337	[clientscript,tob_partylist_init]
2338	[clientscript,tob_partylist_setpartybutton]
2339	[proc,tob_partylist_setpartybutton]
2340	[clientscript,tob_partylist_addline]
2341	[proc,tob_partylist_setsort]
2342	[proc,tob_partylist_sortbutton_draw]
2343	[clientscript,tob_partylist_clicksort]
2345	[clientscript,steam_set_stat_progress]
2350	[proc,deadman_chest_tele]
2351	[proc,steam_set_stat_progress]
2353	[clientscript,castlewars_waitingroom_init]
2354	[clientscript,castlewars_waitingroom_update]
2356	[clientscript,steam_lock_achievements]
2357	[proc,on_enhanced]
2358	[clientscript,settings_interface_scaling]
2359	[proc,shiftclick_toggle]
2362	[clientscript,telenexus_dragscroll]
2363	[proc,settings_interface_scaling]
2365	[clientscript,telenexus_left_click]
2366	[clientscript,stats_init_tooltip]
2367	[clientscript,stats_op]
2373	[proc,toplevel_chatbox_controls_maketransparent]
2374	[clientscript,fade_init]
2375	[proc,castlewars_waitingroom_update]
2377	[proc,hp_hud_open]
2379	[clientscript,toplevel_chatbox_resetbackground]
2383	[clientscript,wint_layout_init]
2384	[clientscript,wint_layout_update]
2385	[proc,wint_layout_update]
2388	[clientscript,collection_draw_tabs_all]
2389	[proc,collection_draw_tabs_all]
2391	[clientscript,mobile_billing_open]
2392	[clientscript,trawler_overlay_init]
2396	[clientscript,orbs_update_store]
2397	[clientscript,recurring_membership]
2398	[proc,recurring_membership]
2399	[proc,openurl_raw]
2400	[proc,settings_precise_timing]
2401	[proc,time_convert_option]
2402	[clientscript,nzone_game_init]
2405	[clientscript,nzone_lobby_init]
2407	[clientscript,tol_homonculus_overlay_init]
2409	[clientscript,soulbane_angerbar_init]
2410	[proc,time_convert]
2411	[proc,time_convert_ms]
2413	[clientscript,steelbox_background]
2414	[proc,show_storebutton]
2415	70161
2416	147473
2417	147729
2418	147985
2419	148241
2420	148497
2421	148753
2422	149009
2423	149265
2424	149521
2425	149777
2426	150033
2427	150289
2428	150545
2429	150801
2430	151313
2431	151569
2432	151825
2433	152081
2434	152337
2435	152593
2436	152849
2437	153105
2438	151057
2439	153361
2440	153617
2441	70160
2442	147472
2443	147728
2444	147984
2445	148240
2446	148496
2447	148752
2448	149008
2449	149264
2450	149520
2451	149776
2452	150032
2453	150288
2454	150544
2455	150800
2456	151312
2457	151568
2458	151824
2459	152080
2460	152336
2461	152592
2462	152848
2463	153104
2464	151056
2465	153360
2466	153616
2469	[proc,meslayer_button_draw]
2470	[clientscript,meslayer_mode7_button]
2471	[clientscript,meslayer_mode7_resetbutton]
2473	[clientscript,model_transparent_init]
2474	[clientscript,model_transparent_dynamiczoom_init]
2475	[clientscript,cc_outline_off]
2476	[clientscript,duel_info]
2477	[clientscript,duel_stake_inv_cash]
2478	[clientscript,duel_stake_inv_details]
2480	[clientscript,duel_stake_load_highlight]
2481	[clientscript,duel_stake_load_mouseout]
2482	[clientscript,duel_switch_setup_buttons]
2483	[clientscript,duel_invalid_option]
2484	[clientscript,duel_switch_to_info]
2485	[clientscript,duel_switch_to_inv]
2486	[clientscript,duel_switch_to_worn]
2487	[proc,duel_switch_buttons]
2488	[clientscript,duel_mouseout_buttons]
2489	[clientscript,duel_add_graphics]
2491	[proc,duel_stake_setup_buttontext]
2492	[proc,duel_stake_textbox]
2494	[proc,skill_guide_subsection_prayer]
2495	[clientscript,openurl_raw_silent]
2496	[proc,mobile_billing_open]
2497	[clientscript,account_init]
2498	[clientscript,account_info_update]
2499	[proc,skill_guide_data_prayer]
2500	[clientscript,account_update_display]
2501	[proc,account_update_display]
2502	[proc,account_update_layout]
2503	[proc,account_billing_update]
2504	[proc,xp_drops_tooltip_setup]
2505	[proc,account_inbox_update]
2506	[proc,account_name_update]
2507	[proc,ignore_appearance]
2508	[proc,friends_appearance]
2514	[proc,xpdrops_setup_appearance]
2515	[clientscript,pest_lander_overlay_init]
2516	[clientscript,pest_status_overlay_init]
2517	[proc,nzone_lobby_hint]
2521	[proc,skill_guide_subsection_defence]
2522	[clientscript,decant_itembutton_key]
2524	[clientscript,toplevel_mainmodal_open]
2525	[proc,toplevel_mainmodal_background]
2527	[proc,mobile_mainmodal_open]
2529	[proc,deadman_restricted_item]
2530	[clientscript,poh_ranging_init]
2531	[clientscript,corp_beast_init]
2532	[clientscript,boardgames_overlay_init]
2533	[clientscript,gnomeball_init]
2534	[clientscript,ii_scroll_init]
2535	[clientscript,barbassault_overlay_init]
2536	[clientscript,barbassault_healer_init]
2537	[clientscript,duel_hud_init]
2538	[clientscript,agilityarena_overlay_init]
2539	[clientscript,br_lobbyoverlay_init]
2540	[clientscript,brew_lobby_overlay_init]
2541	[clientscript,brew_game_overlay_init]
2542	[proc,toplevel_mainmodal_bg_calculate_mobile]
2544	[clientscript,100guide_overlay_init]
2546	[clientscript,ahoy_windspeed_init]
2547	[clientscript,macro_evil_bob_init]
2548	[clientscript,mazetimer_init]
2549	[clientscript,tzhaar_fightpit_init]
2551	[clientscript,cata_boss_init]
2552	[clientscript,magictraining_alch_init]
2553	[clientscript,magictraining_other_init]
2554	[clientscript,fossil_pool_overlay_init]
2556	[clientscript,membership_benefits_init]
2557	[clientscript,membership_benefits_hide_bonds]
2558	[clientscript,membership_benefits_hover]
2559	[proc,membership_benefits_hide_bonds]
2560	[proc,membership_benefits_button_init]
2561	[clientscript,membership_benefits_button_draw]
2562	[proc,membership_benefits_button_draw]
2563	[clientscript,membership_benefits_view]
2564	[clientscript,membership_benefits_close_benefit_hover]
2565	[clientscript,membership_benefits_close_benefit]
2566	[clientscript,membership_benefits_prompt_init]
2567	[clientscript,membership_benefits_prompt_hide_bonds]
2568	[proc,membership_benefits_prompt_hide_bonds]
2569	[proc,membership_benefits_prompt_button_init]
2570	[clientscript,membership_benefits_prompt_button_draw]
2571	[proc,membership_benefits_prompt_button_draw]
2572	[clientscript,tutorial_displayname_border]
2573	[clientscript,tutorial_displayname_init]
2574	[clientscript,tutorial_displayname_buttons]
2575	[proc,tutorial_displayname_buttons]
2576	[proc,bankside_extraop]
2577	[clientscript,bankmain_quantity_buttons]
2578	[proc,bankmain_quantity_buttons]
2580	[proc,int_device]
2581	[proc,mobile_chatbox_resize]
2582	[proc,skill_guide_data_defence]
2583	[proc,telenexus_left_click]
2584	[clientscript,tutorial_overlay_hint]
2585	[proc,tutorial_hint_chatbox]
2586	[proc,tutorial_hint_camera]
2587	[proc,tutorial_hint_camera_desktop]
2588	[proc,tutorial_hint_camera_mobile]
2589	[proc,tutorial_hint_move]
2590	[proc,tutorial_hint_move_desktop]
2591	[proc,tutorial_hint_move_mobile]
2592	[proc,tutorial_hint_interact]
2593	[proc,tutorial_hint_interact_desktop]
2594	[proc,tutorial_hint_interact_mobile]
2595	[proc,bank_depositbox_layout]
2596	[clientscript,depositbox_mobile_button_init]
2597	[clientscript,depositbox_mobile_button_update]
2598	[proc,depositbox_mobile_button_optext]
2600	[clientscript,depositbox_mobile_button_op]
2601	[proc,depositbox_mobile_button_update]
2610	[clientscript,magic_spellbook_redraw]
2611	[proc,magic_spellbook_redraw]
2615	[proc,magic_spellbook_modifyops]
2616	[proc,magic_spellbook_initialisespells]
2617	[clientscript,magic_spellbook_targetmode]
2618	[proc,magic_spellbook_hidespells]
2619	[proc,magic_spellbook_passfilter]
2620	[proc,magic_spellbook_hasrunes]
2621	[proc,magic_spellbook_sort]
2622	[clientscript,magic_spellbook_settooltip]
2623	[proc,magic_spellbook_settooltip]
2624	[proc,wom_tab_highlight]
2625	[proc,wom_get_graphics]
2626	[proc,wom_get_tab]
2627	[proc,wom_get_text]
2630	[clientscript,book_indexed_init]
2632	[clientscript,book_init]
2633	[proc,questlist_draw]
2634	[clientscript,mobile_rating_init]
2635	[clientscript,boardgames_button_init]
2636	[clientscript,boardgames_runelink_options_init]
2637	[proc,xp_drops_tooltip_desktop]
2638	[proc,xp_drops_tooltip_mobile]
2639	[clientscript,xpdrops_tooltip_mobile]
2640	[clientscript,xpdrops_tooltip_mobile_remove]
2641	[clientscript,equipment_icon_init]
2642	[clientscript,equipment_icon_check]
2643	[clientscript,equipment_icon_flash]
2644	[clientscript,tutorial_default_settings]
2645	[clientscript,tutorial_end]
2646	[proc,toplevel_sidebutton_highlight]
2650	[clientscript,telenexus_radio_click]
2651	[clientscript,telenexus_tick_op]
2652	[clientscript,telenexus_tick_mouseover]
2653	[clientscript,telenexus_tick_mouseleave]
2654	[clientscript,telenexus_show_cost]
2655	[proc,telenexus_show_cost]
2656	[clientscript,telenexus_examine_cost]
2657	[clientscript,telenexus_hide_cost]
2658	[proc,telenexus_hide_cost]
2659	[clientscript,telenexus_okbutton_click]
2660	[clientscript,telenexus_done_text]
2661	[clientscript,telenexus_cancel]
2662	[clientscript,poh_nexus_model]
2663	[proc,poh_nexus_model]
2666	[proc,telenexus_set_slot]
2667	[proc,telenexus_remove_tele]
2668	[proc,telenexus_slots_full]
2669	[proc,telenexus_get_slot_value]
2672	[clientscript,telenexus_teleport_init]
2676	[clientscript,telenexus_options_mouseover]
2677	[clientscript,telenexus_options_mouseleave]
2678	[clientscript,telenexus_scry_click]
2679	[clientscript,telenexus_scry_vartrans]
2680	[proc,telenexus_options]
2681	[proc,telenexus_get_slot]
2682	[proc,magic_stop_flash]
2683	[clientscript,boardgames_runelink_options_update_selections]
2684	[clientscript,bond_prompt_init]
2685	[clientscript,telenexus_keyinput_listener]
2686	[proc,telenexus_keyinput_matched]
2695	[clientscript,tournament_supplies_previewarea]
2696	[proc,tournament_supplies_previewarea]
2697	[clientscript,tournament_supplies_switchlayer]
2698	[proc,tournament_supplies_switchlayer]
2701	[proc,tooltip_build]
2706	[clientscript,show_ios_review]
2707	[clientscript,1v1arena_review_init]
2708	[clientscript,1v1arena_review_draw]
2709	[proc,1v1arena_review_draw]
2710	[clientscript,1v1arena_results_init]
2711	[clientscript,1v1arena_clear_opbutton]
2712	[clientscript,1v1arena_clear_resetbutton]
2713	[clientscript,1v1arena_hud_init]
2714	[clientscript,1v1arena_hud_button]
2715	[clientscript,1v1arena_hud_resetbutton]
2716	[clientscript,1v1arena_hud_toggle]
2717	[proc,1v1arena_hud_toggle]
2718	[clientscript,1v1arena_hud_transmit]
2719	[proc,1v1arena_hud_buildplayer]
2721	[proc,1v1arena_hud_buildarena]
2722	[clientscript,1v1arena_spectator_init]
2723	[clientscript,1v1arena_spectator_endhint]
2724	[proc,boardgames_runelink_options_update_selections]
2725	[clientscript,boardgames_runelink_options_update_rune]
2726	[proc,boardgames_runelink_options_update_rune]
2727	[clientscript,meslayer_keyboard_show]
2728	[proc,collection_draw_tab]
2729	[clientscript,collection_hover_tab]
2730	[clientscript,collection_draw_list]
2731	[proc,collection_draw_list]
2732	[proc,collection_draw_log]
2733	[clientscript,collection_item_click]
2735	[proc,collection_category_count]
2736	[clientscript,lovaquest_locks_init]
2737	[clientscript,lovaquest_power_grid_init]
2738	[clientscript,lovaquest_power_grid_redraw]
2739	[proc,lovaquest_power_grid_redraw]
2740	[clientscript,lovaquest_power_grid_rotate]
2741	[proc,lovaquest_power_grid_getslot]
2742	[proc,lovaquest_power_grid_setslot]
2743	[proc,skill_guide_data_slayer]
2744	[clientscript,boardgames_runelink_options_update_time]
2745	[proc,boardgames_runelink_options_update_time]
2746	[clientscript,boardgames_runelink_options_update_ranked]
2747	[proc,boardgames_runelink_options_update_ranked]
2748	[clientscript,farming_tools_main_recreate]
2749	[proc,farming_tools_main_recreate]
2750	[clientscript,farming_tools_side_recreate]
2751	[proc,farming_tools_side_recreate]
2752	[clientscript,snapshot_openkeyboard]
2753	[clientscript,wint_timer_init]
2754	[clientscript,wint_timer_resynch]
2755	[proc,wint_timer_resynch]
2756	[clientscript,wint_timer_countdown]
2757	[proc,wint_timer_countdown]
2758	[proc,prayer_infobutton]
2759	[clientscript,prayer_infomode]
2760	[clientscript,prayer_levelupdate]
2761	[proc,prayer_levelupdate]
2762	[proc,prayer_gettooltiptext]
2763	[proc,tol_pressure_draw]
2764	[proc,tol_pressure_valveinit]
2765	[clientscript,tol_pressure_valvelistener]
2766	[proc,tol_pressure_valvelistener]
2767	[clientscript,tol_pressure_valveop]
2768	[clientscript,tol_pipe_init]
2769	[clientscript,tol_pipe_setbuttons]
2770	[proc,tol_pipe_setbuttons]
2771	[clientscript,pest_rewards_init]
2772	[clientscript,pest_rewards_showpoints]
2773	[proc,pest_rewards_showpoints]
2774	[clientscript,pest_rewards_confirmop]
2775	[clientscript,pest_rewards_draw]
2776	[proc,pest_rewards_draw]
2777	[clientscript,pest_rewards_confirmdraw]
2778	[proc,pest_rewards_confirmdraw]
2779	[proc,pest_rewards_itemdraw]
2780	[proc,pest_rewards_statdraw]
2781	[proc,pest_rewards_statbuttondraw]
2782	[clientscript,pest_rewards_select]
2783	[clientscript,pest_rewards_refuse]
2784	[proc,pest_rewards_costpoints]
2785	[proc,pest_rewards_costpts]
2787	[clientscript,barrows_puzzle_init]
2788	[proc,barrows_puzzle_buttoninit]
2789	[clientscript,barrows_puzzle_buttonhover]
2790	[proc,skill_guide_subsection_magic]
2791	[proc,skill_guide_data_magic]
2792	[proc,orbs_spec_draw_button]
2793	[clientscript,orbs_toggle_spec_op]
2794	[clientscript,orbs_blocked_spec_op]
2795	[clientscript,itemsets_info_init]
2797	[clientscript,journal_list_init]
2798	[clientscript,side_journal_switchtab_onop]
2799	[clientscript,side_journal_switchtab_ontransmit]
2800	[proc,side_journal_switchtab]
2801	[proc,side_journal_active]
2802	[proc,side_journal_inactive]
2805	[clientscript,deadman_broadcast_message]
2807	[proc,deadman_broadcast_message_sizing]
2808	[clientscript,deadman_broadcast_message_timer]
2811	[clientscript,seed_vault_side_init]
2812	[clientscript,seed_vault_side_draw]
2813	[proc,seed_vault_side_draw]
2814	[clientscript,seed_vault_build_category_list]
2815	[clientscript,seed_vault_catop]
2816	[clientscript,seed_vault_catvar]
2817	[proc,seed_vault_catselect]
2818	[clientscript,seed_vault_init]
2819	[clientscript,seed_vault_build]
2820	[proc,seed_vault_build]
2824	[clientscript,seed_vault_drag_obj]
2825	[clientscript,seed_vault_drag_text]
2826	[proc,seed_vault_doscroll]
2827	[clientscript,seed_vault_drop]
2834	[clientscript,if_flash_component]
2835	158481
2836	158480
2840	[clientscript,adventurepath_voucher_init]
2841	[proc,adventurepath_voucher_entry]
2842	[clientscript,adventurepath_voucher_item_op]
2843	[clientscript,adventurepath_item_init]
2844	[proc,adventurepath_item_entry]
2845	[proc,adventurepath_return_head_pos]
2846	[clientscript,adventurepath_init]
2847	[proc,adventurepath_change_page]
2852	[proc,seed_vault_size_check]
2859	[clientscript,clock_fake_update]
2860	[clientscript,clock_update]
2861	[proc,clock_set]
2862	[proc,seed_vault_searching]
2863	[proc,seed_vault_filteritem]
2864	[proc,seed_vault_filterstring]
2865	[clientscript,seed_vault_search_toggle]
2866	[proc,seed_vault_search_setbutton]
2867	[clientscript,seed_vault_search_refresh]
2868	[clientscript,objbox_setbuttons]
2869	[clientscript,objbox_listener]
2870	[clientscript,objbox_reset]
2873	[clientscript,kdr_update]
2874	[proc,kdr_update]
2877	[clientscript,clanwars_setup_smallbox_open]
2878	[clientscript,clanwars_setup_smallbox_close]
2879	[clientscript,clanwars_setup_smallbox_updatetext]
2880	[proc,clanwars_setup_smallbox_updatetext]
2882	[clientscript,if_flash_component_timer]
2884	[clientscript,cc_flash_component]
2885	[clientscript,cc_flash_component_timer]
2886	[clientscript,hp_hud_fade_in_check]
2887	[clientscript,hp_hud_fade_in]
2888	[proc,hp_hud_fade_in]
2889	[clientscript,hp_hud_fade_out]
2890	[proc,hp_hud_fade_out]
2891	[proc,orbs_worldmap_graphics]
2892	[proc,orbs_worldmap_op1]
2893	[clientscript,overlay_portal]
2894	[clientscript,overlay_portal_remove]
2895	[clientscript,overlay_portal_adjust]
2896	[clientscript,mirror_interface_init]
2897	[clientscript,mirror_interface_keypress]
2898	[clientscript,mirror_interface_button_reset]
2899	[clientscript,mirror_interface_button_draw]
2900	[proc,mirror_interface_button_draw]
2904	161297
2905	161553
2906	161296
2907	161552
2908	[clientscript,gauntlet_map_init]
2909	[proc,gauntlet_map_update]
2910	[proc,gauntlet_map_start_update]
2911	[proc,gauntlet_map_room_update]
2912	[proc,gauntlet_map_player_update]
2913	[clientscript,gauntlet_overlay_init]
2914	[clientscript,gauntlet_timer_update]
2915	[clientscript,gauntlet_timer]
2916	[clientscript,gauntlet_timer_end]
2917	[clientscript,gauntlet_recipes_init]
2919	[proc,gauntlet_select_item]
2921	[clientscript,prif_overlay_portal]
2922	[clientscript,prif_remove_portal]
2923	[clientscript,prif_adjust_portal]
2924	[clientscript,prif_recipes_init]
2926	[clientscript,skillmain_init]
2927	[clientscript,skillmain_setup]
2928	[proc,skillmain_setup]
2929	[proc,skillmain_button_create]
2930	[clientscript,skillmain_setquantity]
2931	[clientscript,skillmain_button_draw]
2932	[proc,skillmain_button_draw]
2933	[proc,skillmain_button_wipe]
2934	[clientscript,silver_crafting_mouseeffect]
2935	[proc,silver_crafting_jewellerytitle_setup]
2936	[clientscript,silver_crafting_jewellerytitle_draw]
2937	[proc,silver_crafting_jewellerytitle_draw]
2938	[proc,silver_crafting_jewellery_setup]
2939	[clientscript,silver_crafting_jewellery_draw]
2940	[proc,silver_crafting_jewellery_draw]
2941	[proc,silver_crafting_miscellaneous_setup]
2942	[clientscript,silver_crafting_miscellaneous_draw]
2943	[proc,silver_crafting_miscellaneous_draw]
2945	[proc,adventurepath_init]
2946	[proc,adventurepath_draw_path]
2947	[clientscript,adventurepath_on_mouse_over]
2948	[clientscript,adventurepath_task_init_transmit]
2949	[clientscript,adventurepath_task_init]
2950	[proc,adventurepath_task_init_op]
2951	[proc,adventurepath_task_init]
2952	[clientscript,adventurepath_mouseover_path_reward]
2953	[clientscript,adventurepath_mouseleave_path_reward]
2954	[clientscript,adventurepath_set_info_transmit]
2955	[clientscript,adventurepath_set_info]
2956	[clientscript,ap_icon_flash]
2957	[clientscript,ap_flash_timer]
2958	[proc,adventurepath_flashicon]
2959	[proc,adventurepath_return_task_status]
2960	[proc,adventurepath_return_reward_f2p]
2961	[proc,adventurepath_return_reward_p2p]
2962	[proc,adventurepath_return_kit]
2963	[proc,adventurepath_return_path_status]
2964	[proc,adventurepath_return_path_reward_f2p]
2965	[proc,adventurepath_return_path_reward_p2p]
2966	[proc,adventurepath_return_voucher]
2967	[proc,adventurepath_return_path_length]
2968	[proc,adventurepath_return_path_tasks_done]
2969	[proc,adventurepath_return_task_focus]
2970	[proc,adventurepath_set_task_focus]
2971	[clientscript,boardgames_draughts_options_init]
2974	[proc,create_v2_stone_button_filled_free]
2975	[clientscript,v2_stone_button_filled_free]
2976	[proc,v2_stone_button_filled_free]
2977	[clientscript,v2_stone_button_in_filled_free]
2978	[proc,v2_stone_button_in_filled_free]
2979	[proc,create_v2_stone_button_filled_free_icon]
2980	[clientscript,v2_stone_button_filled_free_icon]
2981	[proc,v2_stone_button_filled_free_icon]
2982	[clientscript,v2_stone_button_in_filled_free_icon]
2983	[proc,v2_stone_button_in_filled_free_icon]
2991	[proc,stoneborder_free]
2992	[clientscript,adventurepath_do_nothing]
2993	[proc,adventurepath_hint_list]
2997	[proc,adventurepath_set_info]
3002	[proc,adventurepath_task_focus]
3003	[clientscript,adventurepath_open_url]
3005	[proc,adventurepath_spawn_item]
3006	[clientscript,adventurepath_say_item]
3007	[proc,adventurepath_button_text]
3008	[clientscript,adventurepath_back_mouseover]
3009	[clientscript,adventurepath_back_mouseleave]
3010	[proc,adventurepath_back_button_text]
3011	[clientscript,adventurepath_back_to_paths]
3012	[clientscript,adventurepath_show_more_info]
3018	[clientscript,adventurepath_side_init]
3019	[clientscript,adventurepath_side_info_transmit]
3021	[clientscript,adventurepath_side_layer]
3022	[proc,adventurepath_side_info]
3023	[proc,adventurepath_side_settings]
3024	[clientscript,adventurepath_side_settings_button_op]
3025	[proc,adventurepath_side_settings_content]
3035	[proc,adventurepath_side_create_toggle]
3036	[clientscript,adventurepath_side_settings_back_leave]
3037	[proc,adventurepath_side_settings_back_leave]
3038	[clientscript,adventurepath_side_settings_back_over]
3039	[clientscript,adventurepath_side_settings_back_op]
3041	[proc,league_create_dropdown]
3042	[proc,poh_tablets_buildbutton]
3043	[clientscript,poh_tablets_updatebutton]
3044	[clientscript,poh_tablets_hidehighlight]
3045	[clientscript,trawler_water_init]
3046	[clientscript,trawler_water_update]
3047	[proc,trawler_water_update]
3048	[clientscript,trawler_activity_init]
3049	[clientscript,trawler_activity_update]
3050	[proc,trawler_activity_update]
3054	[clientscript,skill_guide_magic_op]
3055	[proc,modulo]
3056	[clientscript,macro_beekeeper_init]
3057	[clientscript,macro_beekeeper_transmit]
3058	[clientscript,macro_beekeeper_confirmbutton]
3059	[clientscript,macro_beekeeper_timer]
3060	[proc,macro_beekeeper_update]
3061	[proc,macro_beekeeper_setcomponent]
3062	[clientscript,macro_beekeeper_rearrange]
3063	[clientscript,bees_init]
3064	[clientscript,bees]
3065	[clientscript,bees_mouseover]
3066	[clientscript,bees_mouseleave]
3067	[proc,br_loadout_drawloadout]
3068	[clientscript,br_loadout_insert]
3069	[clientscript,br_loadout_rearrange]
3070	[proc,br_loadout_additem]
3071	[clientscript,br_loadout_hidewarning]
3072	[clientscript,br_loadout_restorewarning]
3073	[proc,br_loadout_sort]
3074	[proc,br_loadout_get_inv]
3075	[proc,br_loadout_get_worn]
3076	[proc,br_loadout_set_inv]
3077	[proc,br_loadout_set_worn]
3078	[clientscript,br_tournament_button_init]
3079	[clientscript,br_tournament_button_reset]
3080	[proc,br_tournament_button_reset]
3081	[clientscript,br_tournament_button_op]
3082	[clientscript,br_reward_shop_init]
3083	[clientscript,br_reward_shop_showpoints]
3084	[proc,br_reward_shop_showpoints]
3085	[clientscript,br_reward_shop_confirmop]
3086	[clientscript,br_reward_shop_draw]
3087	[proc,br_reward_shop_draw]
3088	[proc,br_reward_shop_itemdraw]
3089	[clientscript,br_reward_shop_select]
3090	[clientscript,br_reward_shop_refuse]
3091	[proc,welcome_screen_banner]
3093	[clientscript,canoe_init]
3094	[clientscript,canoe_remake]
3095	[proc,canoe_setup]
3096	[proc,canoe_name]
3097	[clientscript,close_mouse_interact]
3098	[clientscript,canoe_close]
3099	[clientscript,canoe_map_init]
3100	[proc,canoe_location_setup]
3101	[clientscript,canoe_location_hover]
3103	[proc,canoe_get_state]
3104	[proc,canoe_return_locations]
3105	[proc,canoe_paddle_log]
3106	[proc,canoe_paddle_dugout]
3107	[proc,canoe_paddle_stable_dugout]
3108	[proc,canoe_paddle_stable_waka]
3109	[clientscript,poh_board_init]
3110	[clientscript,poh_board_addline]
3111	[clientscript,poh_board_set_last_name]
3112	[proc,poh_board_setsort]
3113	[proc,poh_board_redraw]
3114	[proc,poh_board_v_line]
3115	[proc,poh_board_sortbutton_draw]
3116	[clientscript,poh_board_hoversort]
3117	[clientscript,poh_board_clicksort]
3118	[clientscript,poh_board_location_filter_init]
3119	[clientscript,poh_board_location_filter_ontransmit]
3120	[clientscript,poh_board_location_filter_clicked]
3121	[proc,poh_board_location_filter_activate]
3122	[proc,poh_board_location_filter_build]
3123	[proc,poh_board_loc]
3124	[clientscript,poh_board_explain_location]
3125	[clientscript,poh_board_send_name]
3126	[clientscript,poh_board_validate_name]
3128	[clientscript,screen_glow_start]
3129	[clientscript,screen_glow_end]
3130	[clientscript,screen_glow_fadein]
3131	[clientscript,screen_glow_fadeout]
3141	[clientscript,target_system_init]
3142	[clientscript,target_system_hideop]
3143	[clientscript,target_system_redraw]
3144	[proc,target_system_redraw]
3151	[clientscript,boardgames_draughts_options_update_selections]
3152	[proc,boardgames_draughts_options_update_selections]
3153	[clientscript,offer_banner_init]
3154	[proc,offer_banner_close]
3155	[clientscript,offer_banner]
3156	[proc,offer_banner]
3157	[clientscript,offer_button]
3158	[proc,offer_button]
3160	[proc,league_world]
3161	[clientscript,clear_chat_all]
3162	[clientscript,league_icon_flash]
3163	[clientscript,league_flash_timer]
3164	[proc,league_flashicon]
3165	[clientscript,buff_bar_init]
3166	[clientscript,buff_bar_setup]
3167	[proc,buff_bar_setup]
3168	[proc,buff_bar_buff_create]
3169	[proc,buff_bar_buff_create_tooltip]
3170	[clientscript,buff_bar_mobile_tooltip]
3171	[clientscript,buff_bar_mobile_tooltip_remove]
3172	[proc,buff_bar_buff_position]
3175	[clientscript,summary_sidepanel_draw]
3176	[proc,side_journal_tab_highlight]
3177	[proc,side_journal_tab_highlight_reset]
3178	[proc,side_journal_additional]
3179	[proc,chatbox_open]
3181	[clientscript,league_points_progress_bar]
3182	[proc,league_points_progress_bar]
3183	[clientscript,league_progress_bar_mobile_tooltip]
3184	[clientscript,league_points_progress_bar_expand]
3185	[clientscript,league_border_pulse_start]
3186	[clientscript,league_border_pulse_stop]
3187	[clientscript,league_border_pulse]
3188	[clientscript,league_relics_init]
3189	[proc,league_relics_draw_selections]
3190	[clientscript,league_relic_hover]
3191	[clientscript,league_relics_loading]
3192	[clientscript,league_relics_stop_loading]
3193	[clientscript,league_relic_expanded_view]
3194	[clientscript,league_relic_not_available]
3195	[clientscript,league_relic_button_hover]
3196	[clientscript,league_relic_back]
3197	[clientscript,league_relic_confirm]
3198	[clientscript,league_relic_confirm_back]
3199	[proc,league_relic_display]
3200	[clientscript,clock_init]
3202	[clientscript,league_tasks_init]
3203	[clientscript,league_tasks_draw_list]
3204	[proc,league_tasks_draw_list]
3205	[proc,account_setup_tabs]
3206	[clientscript,league_task_hover]
3207	[clientscript,league_task_filter_init]
3208	[clientscript,league_task_updatetext]
3209	[proc,league_task_filter_updatetext]
3212	[clientscript,league_task_filter_open]
3214	[clientscript,league_task_filter_close]
3215	[proc,league_task_updatetext]
3216	[proc,league_task_is_completed]
3217	[clientscript,league_info_init]
3218	[proc,league_info_draw_interface]
3219	[proc,league_get_league_name]
3220	[clientscript,league_info_mouseover]
3221	[clientscript,league_info_mouseleave]
3222	[clientscript,league_blog_click]
3223	[clientscript,league_menu_click]
3224	[clientscript,league_menu_frame_click]
3225	[clientscript,league_side_panel_init]
3226	[clientscript,league_side_panel_update_bar]
3227	[proc,league_side_panel_update_bar]
3228	[proc,league_side_panel_update_text]
3229	[clientscript,league_side_panel_button_hover]
3230	[clientscript,league_side_panel_hudtoggle]
3231	[clientscript,league_side_panel_hudop]
3232	[proc,league_side_panel_hudtoggle]
3233	165137
3234	165136
3235	[proc,update_diary_entry_grey]
3236	[proc,questlist_f2p_progress_grey]
3237	[proc,questlist_members_progress_grey]
3238	[proc,questlist_miniquests_progress_grey]
3239	[proc,questlist_showprogress_grey]
3240	[clientscript,v2_stone_button_change_out]
3241	[proc,v2_stone_button_change_out]
3242	[clientscript,v2_stone_button_change_in]
3243	[proc,v2_stone_button_change_in]
3253	[clientscript,magic_training_init]
3254	[clientscript,magic_training_rewards]
3255	[proc,magic_training_rewards]
3256	[clientscript,magic_training_rewards_op]
3257	[proc,magic_training_highlight_selected]
3258	[proc,magic_training_costs_background]
3259	[clientscript,magic_training_costs_transmit]
3260	[proc,magic_training_costs]
3261	[clientscript,magic_training_confirm_transmit]
3262	[proc,magic_training_confirm]
3263	[clientscript,account_tab_highlight]
3264	[clientscript,account_tab_highlight_reset]
3265	[proc,account_tab_highlight]
3266	[proc,account_tab_highlight_reset]
3267	[clientscript,account_switch_tab]
3268	[clientscript,account_update_tabs]
3269	[proc,account_update_tabs]
3270	[proc,account_polls_update]
3271	[clientscript,boardgames_draughts_options_update_rune]
3272	[proc,boardgames_draughts_options_update_rune]
3273	[clientscript,boardgames_draughts_options_update_time]
3274	[proc,boardgames_draughts_options_update_time]
3275	[clientscript,bankmain_viewbuttons]
3276	[proc,bankmain_viewbuttons]
3281	[clientscript,wear_initslots]
3282	[proc,wear_initslot]
3284	[clientscript,league_reward_shop_select]
3285	[clientscript,league_reward_shop_refuse]
3287	[clientscript,league_reward_shop_confirmop]
3288	[clientscript,league_reward_shop_buy]
3289	[clientscript,league_reward_confirm_back]
3290	[proc,league_reward_shop_showpoints]
3291	[clientscript,league_reward_shop_showpoints]
3293	[clientscript,boardgames_draughts_options_update_ranked]
3294	[proc,boardgames_draughts_options_update_ranked]
3295	[clientscript,boardgames_runeversi_options_init]
3296	[clientscript,boardgames_runeversi_options_update_selections]
3297	[proc,wiki_icon_enabled]
3299	[proc,wiki_chat_search]
3300	[clientscript,wiki_lookup]
3301	[proc,wiki_lookup]
3303	[proc,encode_for_url]
3304	[clientscript,wiki_icon_init]
3305	[clientscript,wiki_icon_update]
3306	[proc,wiki_icon_update]
3307	[clientscript,welcome_type_setup_transmit]
3308	[clientscript,welcome_type_setup_op]
3309	[proc,welcome_type_setup]
3310	[proc,summary_sidepanel_draw]
3311	[clientscript,nightmare_totems_init]
3312	[clientscript,nightmare_totem_hud_fade_in]
3313	[proc,nightmare_totem_hud_fade_in]
3314	[proc,nightmare_totem_hud_pos]
3315	[clientscript,nightmare_totem_hud_update]
3316	[proc,nightmare_totem_hud_update]
3317	[clientscript,nightmare_totem_hud_update_bar]
3318	[proc,nightmare_totem_hud_update_bar]
3319	[clientscript,nightmare_totem_hud_bar_fadeout]
3320	[proc,nightmare_totem_hud_bar_fadeout]
3321	[clientscript,nightmare_totem_hud_bar_fadein]
3322	[proc,nightmare_totem_hud_bar_fadein]
3323	[clientscript,nightmare_totem_hud_fade_out]
3324	[proc,nightmare_totem_hud_fade_out]
3325	[proc,bonds_hide]
3327	[proc,bankside_worn_drawitem]
3328	[clientscript,bankside_worn_reorder]
3329	[clientscript,newspaper_init]
3330	[clientscript,newspaper_content]
3333	[proc,skill_guide_subsection_firemaking]
3334	[proc,skill_guide_data_firemaking]
3337	[clientscript,adventurepath_side_hint_mouseover]
3338	[clientscript,adventurepath_side_hint_mouseleave]
3339	[clientscript,adventurepath_side_settings_mouseover]
3340	[clientscript,adventurepath_side_settings_mouseleave]
3343	[clientscript,notification_display_init]
3344	[clientscript,notification_tli_update]
3345	[clientscript,notification_store_data]
3346	[clientscript,notification_start]
3347	[clientscript,notification_delay]
3348	[clientscript,notification_end]
3349	[proc,notification_init]
3350	[clientscript,notification_positioning]
3351	[proc,notification_positioning]
3352	[proc,boardgames_runeversi_options_update_selections]
3353	[clientscript,boardgames_runeversi_options_update_rune]
3354	[proc,boardgames_runeversi_options_update_rune]
3355	[clientscript,boardgames_runeversi_options_update_time]
3356	[proc,boardgames_runeversi_options_update_time]
3357	[clientscript,boardgames_runeversi_options_update_ranked]
3358	[proc,boardgames_runeversi_options_update_ranked]
3360	[clientscript,gazette_button]
3361	[proc,gazette_button]
3365	[clientscript,pvp_store_side_init]
3366	[clientscript,pvp_store_side_draw]
3367	[proc,pvp_store_side_draw]
3371	[clientscript,graphicbox_setbuttons]
3372	[clientscript,graphicbox_listener]
3373	[clientscript,graphicbox_reset]
3374	[clientscript,graphicbox_set_graphic]
3376	[clientscript,tut2_overlay_hint]
3377	[proc,tut2_hint_chatbox]
3378	[proc,tut2_hint_camera]
3379	[proc,tut2_hint_camera_desktop]
3380	[proc,tut2_hint_camera_mobile]
3381	[proc,tut2_hint_move]
3382	[proc,tut2_hint_move_desktop]
3383	[proc,tut2_hint_move_mobile]
3384	[proc,tut2_hint_interact]
3385	[proc,tut2_hint_interact_desktop]
3386	[proc,tut2_hint_interact_mobile]
3387	[clientscript,tut2_progressbar_init]
3388	[clientscript,tut2_progressbar_set]
3389	[proc,tut2_progressbar_set]
3390	[clientscript,tut2_default_settings]
3391	[clientscript,tut2_end]
3396	[clientscript,meslayer_key_close]
3402	[clientscript,worldmap_key_toggle_transmit]
3403	[clientscript,prayer_flash_prayer]
3404	[clientscript,prayer_flash_timer]
3405	[clientscript,prayer_stop_flash]
3406	[clientscript,highlight_noclick]
3407	[clientscript,highlight_noclick_clear]
3408	[clientscript,highlight_start]
3409	[clientscript,highlight_screen]
3410	[proc,highlight_screen]
3411	[clientscript,highlight_resize]
3412	[clientscript,highlight_screen_hide_layer]
3413	[clientscript,highlight_textbox]
3414	[clientscript,highlight_textbox_resize]
3415	[proc,highlight_textbox]
3417	[clientscript,highlight_close_button]
3418	[clientscript,highlight_close_op]
3419	[proc,highlight_button]
3420	[clientscript,highlight_next]
3423	[clientscript,boardgames_runeversi_update_grid]
3424	[clientscript,boardgames_challenge_init]
3425	[clientscript,boardgames_challenge_update]
3426	[proc,boardgames_challenge_update]
3428	[proc,bool_to_int]
3429	[clientscript,hp_bar_reset]
3436	[proc,string_to_int]
3441	[clientscript,hallowed_overlay_init]
3442	[clientscript,hallowed_timer_update]
3443	[clientscript,hallowed_timer_pause]
3444	[clientscript,hallowed_timer]
3445	[proc,hallowed_timer_text]
3452	[clientscript,deathkeep_opitem]
3453	[proc,deathkeep_right_initbutton]
3454	[proc,deathkeep_right_drawbutton]
3455	[clientscript,deathkeep_right_opbutton]
3456	[clientscript,deathkeep_right_resetbutton]
3457	[proc,gravestone_hud_init]
3458	[clientscript,gravestone_hud_resynch]
3459	[proc,gravestone_hud_resynch]
3460	[clientscript,gravestone_hud_tickdown]
3461	[proc,gravestone_hud_write]
3462	[clientscript,gravestone_generic_init]
3463	[clientscript,gravestone_generic_window_set]
3464	[proc,gravestone_generic_window_set]
3466	[clientscript,gravestone_generic_drag_preview]
3467	[clientscript,gravestone_generic_drag_end]
3468	[clientscript,gravestone_generic_size_handler]
3469	[proc,gravestone_generic_size_handler]
3470	[proc,gravestone_generic_resetbutton]
3471	[clientscript,gravestone_generic_opbutton]
3472	[clientscript,gravestone_generic_resetbutton]
3473	[clientscript,gravestone_generic_title]
3474	[proc,gravestone_generic_title]
3475	[proc,gravestone_generic_parsefee]
3476	[proc,gravestone_generic_parsecoffer]
3477	[clientscript,gravestone_generic_resynchdata]
3478	[clientscript,gravestone_transmit_data]
3479	[clientscript,death_coffer_init]
3480	[clientscript,death_coffer_setcontents]
3481	[proc,death_coffer_setcontents]
3484	[proc,death_coffer_activebutton]
3485	[proc,death_coffer_inactivebutton]
3486	[clientscript,death_coffer_opbutton]
3487	[clientscript,death_coffer_resetbutton]
3488	[clientscript,death_coffer_drawbutton]
3489	[proc,death_coffer_drawbutton]
3490	[clientscript,death_office_init]
3491	[clientscript,death_office_redraw]
3492	[proc,death_office_redraw]
3493	[clientscript,death_office_title]
3494	[proc,death_office_title]
3495	[clientscript,death_office_resetquantity]
3496	[proc,death_office_resetquantity]
3497	[clientscript,death_office_drawquantity]
3498	[proc,death_office_drawquantity]
3499	[clientscript,death_office_opquantity]
3505	[proc,inv_freespace]
3507	166929
3508	166928
3509	[proc,adventurepath_voucher_init]
3513	[clientscript,teleblock_timer_tooltip]
3514	[clientscript,teleblock_timer]
3515	[clientscript,teleblock_toggle_onmouseleave]
3531	[clientscript,hallowed_tools_main_init]
3532	[clientscript,poh_costumes_init]
3533	[clientscript,poh_costumes_build]
3534	[proc,poh_costumes_build]
3535	[proc,poh_costumes_draw_big]
3536	[proc,poh_costumes_draw_small]
3537	[proc,poh_costumes_drawalternates]
3538	[proc,poh_costumes_drawitem]
3539	[proc,poh_costumes_hideitem]
3540	[proc,poh_costumes_countmembers]
3541	[proc,poh_costumes_countalternates]
3542	[clientscript,poh_costumes_opgraphic]
3543	[proc,poh_costumes_quicksort]
3544	[proc,poh_costumes_rightborder]
3545	[proc,poh_costumes_bottomfiller]
3546	[clientscript,poh_costumes_heightstretch]
3547	[proc,poh_costumes_heightstretch]
3548	[clientscript,poh_costumes_buttons_dynamicsetup]
3549	[proc,poh_costumes_buttons_dynamicsetup]
3550	[clientscript,poh_costumes_buttons_draw]
3551	[proc,poh_costumes_buttons_draw]
3552	[clientscript,poh_costumes_buttons_1off_op]
3553	[clientscript,poh_costumes_buttons_1off_reset]
3554	[proc,poh_costumes_buttons_1off_reset]
3555	[clientscript,poh_costumes_buttons_toggle_op]
3556	[clientscript,poh_costumes_buttons_toggle_reset]
3557	[proc,poh_costumes_buttons_toggle_reset]
3558	[clientscript,poh_costumes_buttons_searchop]
3561	[proc,poh_costumes_searchsort]
3562	[clientscript,poh_costumes_searchresult]
3563	[clientscript,poh_costumes_searchtimeout]
3564	[clientscript,poh_costumes_buttons_search_reset]
3565	[proc,poh_costumes_buttons_search_reset]
3566	[clientscript,boardgames_runesquares_options_init]
3567	[clientscript,hallowed_tools_side_init]
3568	[proc,hallowed_tools_main_create]
3569	[clientscript,hallowed_tools_main_recreate]
3570	[proc,hallowed_tools_main_recreate]
3571	[proc,hallowed_tools_side_create]
3572	[clientscript,hallowed_tools_side_recreate]
3573	[proc,hallowed_tools_side_recreate]
3577	[clientscript,hallowed_tools_main_update]
3578	[proc,hallowed_tools_main_update]
3579	[clientscript,hallowed_tools_side_update]
3580	[proc,hallowed_tools_side_update]
3581	[proc,hallowed_tools_getstored]
3582	[proc,hallowed_tools_getcarried]
3583	[clientscript,teletab_init]
3584	[clientscript,teletab_steelborder]
3585	[clientscript,teletab_skillmain_init]
3586	[clientscript,teletab_redraw]
3587	[proc,teletab_redraw]
3589	[proc,teletab_addingredient]
3590	[proc,teletab_placeingredient]
3591	[proc,teletab_button_redraw]
3592	[clientscript,teletab_button_draw]
3593	[proc,teletab_button_draw]
3594	[clientscript,teletab_button_op]
3595	[clientscript,teletab_confirm_draw]
3596	[proc,teletab_confirm_draw]
3597	[clientscript,teletab_confirm_op]
3598	[clientscript,teletab_confirm_reset]
3599	[proc,skillmain_init]
3600	[clientscript,bees_mouserepeat]
3601	[clientscript,build_makeover_feet]
3602	[clientscript,makeover_feet_select]
3603	[proc,makeover_feet_add_option]
3604	[clientscript,100guide_flour_init]
3605	[clientscript,100guide_flour_drawitems]
3606	[proc,100guide_flour_drawitems]
3607	[clientscript,partydrop_main_init]
3608	[clientscript,partydrop_main_chestitems]
3609	[proc,partydrop_main_chestitems]
3610	[clientscript,partydrop_quantity_op]
3611	[clientscript,partydrop_main_offeritems]
3612	[proc,partydrop_main_offeritems]
3613	[clientscript,partydrop_main_quantitybutton]
3614	[proc,partydrop_main_quantitybutton]
3615	[clientscript,partydrop_main_confirm_op]
3616	[clientscript,partydrop_main_confirm_reset]
3617	[clientscript,partydrop_side_init]
3618	[clientscript,partydrop_side_build]
3619	[proc,partydrop_side_build]
3620	[proc,partydrop_side_drawitem]
3621	[clientscript,partydrop_side_reorder]
3622	[proc,clan_stonebutton_init]
3623	[clientscript,clan_stonebutton_draw]
3624	[proc,clan_stonebutton_draw]
3625	[clientscript,imp_box_deposit_init]
3626	[clientscript,imp_box_text_update]
3627	[proc,imp_box_text_update]
3628	[clientscript,interface_inv_overlay_init]
3634	[clientscript,castlewars_catapult_init]
3635	[clientscript,castlewars_catapult_hover]
3636	[proc,castlewars_catapult_hover]
3650	[clientscript,bondif_init]
3657	[clientscript,trailblazer_areas_init]
3658	[proc,league_areas_draw_interface]
3660	[proc,league_areas_setup_events]
3661	[clientscript,league_areas_onclick]
3662	[proc,league_areas_onclick]
3663	[clientscript,league_areas_mouseover]
3664	[proc,league_areas_mouseover]
3665	[clientscript,league_areas_mouseleave]
3666	[proc,league_areas_mouseleave]
3667	[clientscript,league_areas_stop_loading]
3668	[clientscript,league_areas_show_detailed]
3669	[proc,league_areas_show_detailed]
3670	[clientscript,league_areas_tab_click]
3671	[proc,league_areas_tab_details]
3672	[clientscript,league_area_button_hover]
3673	[proc,league_area_button_hover]
3674	[clientscript,league_area_confirm]
3675	[clientscript,league_area_teleport]
3676	[clientscript,league_area_not_available]
3678	[clientscript,league_area_back]
3679	[clientscript,league_area_confirm_selection]
3680	[clientscript,league_area_confirm_back]
3683	[clientscript,league_areas_progress_bar]
3684	[proc,league_areas_progress_bar]
3685	[clientscript,league_area_flash_start]
3686	[clientscript,league_area_flash_stop]
3687	[clientscript,league_area_flash]
3691	[proc,leagues_close_button]
3692	[clientscript,leagues_closebutton_click]
3693	[proc,leagues_menu_button]
3694	[proc,league_relic_hover]
3695	[proc,league_relic_button_hover]
3696	[proc,league_relic_display_core]
3697	[proc,league_relic_active]
3699	[proc,league_task_display]
3700	[clientscript,league_setup_display_hover]
3701	[proc,league_setup_display_hoveroff]
3702	[proc,league_setup_display_hoverdisable]
3703	[clientscript,league_setup_display_dropdown_hover]
3707	[clientscript,league_tutorial_main_init]
3708	[clientscript,league_tutorial_interface_update]
3709	[proc,league_tutorial_interface_update]
3710	[clientscript,league_tutorial_button_hover]
3711	[clientscript,league_tutorial_button_op]
3712	[clientscript,league_tutorial_button_reset]
3713	[proc,league_tutorial_button_draw]
3714	[proc,league_tutorial_interface_update_header]
3715	[proc,league_tutorial_interface_update_text]
3716	[proc,league_tutorial_interface_update_button1]
3717	[proc,league_tutorial_interface_update_button2]
3718	[proc,league_tutorial_interface_update_graphic]
3719	[proc,in_league_tutorial]
3729	[clientscript,start_slideshow]
3730	[proc,start_slideshow]
3731	[clientscript,stop_slideshow]
3732	[proc,stop_slideshow]
3733	[clientscript,slideshow_timer]
3734	[proc,slideshow_transition]
3735	[clientscript,slideshow_fadeout]
3736	[clientscript,slideshow_skip]
3738	[clientscript,closebutton_click_sound]
3740	[clientscript,sound_synth]
3741	[proc,in_tutorial]
3742	[proc,get_quest_name]
3743	[clientscript,bankside_bag_draw]
3744	[proc,bankside_bag_draw]
3745	[proc,meslayer_mode1_enabled]
3763	[clientscript,friendschat_op]
3764	[proc,friendschat_sendkick]
3765	[clientscript,chatchannel_login]
3766	[clientscript,friendschat_leavechat]
3769	[proc,on_steam]
3775	[clientscript,pillory_button_init]
3776	[proc,pillory_button_reset]
3777	[clientscript,pillory_button_op]
3778	[clientscript,pillory_button_reset]
3779	[clientscript,gnome_cuisine_init]
3780	[proc,gnome_cuisine_enum]
3781	[proc,gnome_cuisine_title]
3782	[clientscript,gnome_cuisine_confirmation_in]
3783	[clientscript,gnome_cuisine_confirmation_out]
3784	[clientscript,gnome_cuisine_update_if]
3785	[proc,gnome_show_cuisine]
3786	[clientscript,gnome_show_ingredients]
3787	[proc,gnome_show_ingredients]
3788	[proc,gnome_create_ingredient]
3789	[proc,gnome_return_dummy]
3790	[proc,cuisine_check_inv]
3799	[clientscript,quiz_init]
3800	[clientscript,macro_mime_emotes_build]
3801	[clientscript,macro_mime_emotes_drawbutton]
3802	[proc,macro_mime_emotes_drawbutton]
3803	[proc,macro_mime_emotes_buttontext]
3804	[clientscript,xpreward_init]
3805	[clientscript,xpreward_update]
3806	[proc,xpreward_update]
3807	[clientscript,xpreward_select]
3808	[clientscript,xpreward_confirm]
3811	[clientscript,brew_tools_init]
3812	[proc,brew_tools_setupbutton]
3813	[proc,brew_tools_resetbutton]
3814	[clientscript,brew_tools_op]
3815	[clientscript,brew_tools_resetbutton]
3816	[clientscript,brew_tools_supplies]
3817	[proc,brew_tools_supplies]
3818	[clientscript,hauntedmine_controls_init]
3819	[clientscript,hauntedmine_controls_update]
3820	[proc,hauntedmine_controls_update]
3825	[clientscript,prisonpete_init]
3826	[clientscript,settings_init]
3827	[proc,settings_init]
3828	[proc,settings_window_setup]
3831	[clientscript,settings_window_resize]
3832	[proc,settings_window_resize]
3835	[clientscript,settings_tab_op]
3836	[clientscript,settings_tab_transmit]
3837	[proc,settings_create_tabs]
3838	[proc,settings_tab_selected]
3839	[proc,settings_tab_not_selected]
3840	[proc,settings_populate_settings]
3841	[proc,settings_create_setting]
3842	[proc,settings_seemore]
3843	[clientscript,settings_seemore_op]
3844	[proc,settings_seemore_check]
3846	[proc,settings_create_toggle]
3847	[clientscript,settings_toggle_op]
3848	[clientscript,settings_toggle_synch]
3849	[clientscript,settings_toggle_transmit]
3850	[proc,settings_create_drop_down]
3851	[clientscript,setting_show_dropdown]
3852	[clientscript,setting_dropdown_entry_op]
3853	[clientscript,setting_dropdown_entry_close]
3854	[clientscript,setting_dropdown_synch]
3855	[clientscript,setting_dropdown_transmit]
3856	[proc,settings_create_input_setting]
3857	[clientscript,settings_input_op]
3858	[clientscript,settings_input_setting_transmit]
3859	[clientscript,settings_input_timer]
3860	[proc,settings_create_slider]
3861	[clientscript,settings_slider_op]
3862	[clientscript,settings_slider_synch]
3863	[clientscript,settings_slider_on_transmit]
3864	[proc,settings_create_keybind_entry]
3865	[proc,settings_create_button]
3866	[clientscript,settings_button_op]
3867	[clientscript,settings_button_on_transmit]
3868	[proc,header_create]
3869	[proc,text_create]
3870	[proc,searchbar_create]
3871	[proc,searchbar_setup_op]
3872	[clientscript,settings_show_keyboard]
3873	[clientscript,searchbar_op]
3874	[clientscript,searchbar_mouseover]
3875	[clientscript,searchbar_mouseleave]
3876	[clientscript,searchbar_keypress]
3877	[proc,searchbar_set_text]
3878	[clientscript,settings_op_sound]
3879	[proc,settings_op_sound]
3881	[proc,settings_toggle_choose_transmit]
3882	[proc,settings_dropdown_choose_transmit]
3883	[proc,settings_input_setting_set_transmit]
3884	[proc,settings_slider_choose_transmit]
3885	[proc,settings_slider_choose_onop]
3886	[proc,settings_slider_choose_ondrag]
3887	[proc,settings_slider_choose_ondragcomplete]
3894	[clientscript,settings_zoom_position_timer]
3895	[clientscript,settings_camera_zoom_slider_click]
3896	[clientscript,settings_zoom_slider_ondrag]
3897	[clientscript,settings_zoom_slider_dragcomplete]
3898	[proc,settings_zoom_slider_specific_pos]
3899	[proc,settings_camera_do_zoom]
3900	[proc,settings_zoom_position_slider]
3902	[proc,settings_button_choose_transmit]
3903	[clientscript,settings_button_init]
3904	[clientscript,settings_button_repeat]
3905	[clientscript,settings_button_leave]
3906	[clientscript,boardgames_runesquares_options_update_selections]
3907	[proc,boardgames_runesquares_options_update_selections]
3908	[clientscript,settings_side_bottom_init]
3909	[clientscript,settings_side_tabs_init]
3910	[proc,settings_side_tabs]
3911	[clientscript,settings_side_tab_highlight]
3912	[clientscript,settings_side_tab_highlight_reset]
3913	[proc,settings_side_tab_highlight]
3914	[proc,settings_side_tab_highlight_reset]
3915	[clientscript,settings_side_switch_tab]
3916	[clientscript,settings_side_update_tabs]
3917	[proc,settings_side_update_tabs]
3918	[proc,settings_side_house_create]
3919	[proc,settings_side_accept_aid]
3920	[clientscript,settings_side_accept_aid_resynch]
3921	[clientscript,settings_accept_aid_op]
3922	[proc,settings_side_accept_aid_create]
3923	[proc,settings_side_runmode]
3924	[clientscript,settings_side_runmode_resynch]
3925	[clientscript,settings_side_runmode_op]
3926	[proc,settings_side_runmode_create]
3936	[proc,settings_create_icon]
3937	[proc,settings_update_icon]
3938	[clientscript,settings_icon_op]
3942	[clientscript,zoom_mouse_transmit]
3943	[clientscript,zoom_timer]
3944	[clientscript,settings_side_op_sound]
3945	[proc,settings_side_op_sound]
3954	[clientscript,summary_sidepanel_combat_level_transmit]
3955	[proc,settings_check_block]
3956	[proc,settings_op_checker]
3959	[proc,settings_custom_check]
3960	[proc,settings_get_toggle]
3961	[proc,settings_get_slider]
3962	[proc,settings_get_dropdown]
3963	[proc,settings_get_keybind]
3964	[proc,settings_get_number_input]
3965	[proc,settings_clicked_toggle]
3966	[proc,settings_set_slider]
3967	[proc,settings_set_dropdown]
3968	[proc,settings_set_keybind]
3969	[proc,settings_clicked_button]
3973	[proc,settings_hitsplat_tinting]
3974	[proc,settings_wiki_lookup]
3975	[proc,settings_data_orbs]
3979	[proc,settings_special_attack_bar_tooltip]
3984	[proc,settings_split_private_chat]
3985	[proc,settings_hide_private_chat]
3986	[proc,settings_profanity_filter]
3989	[proc,settings_store_button]
3990	[proc,settings_mouse_camera]
3994	[proc,settings_function_button]
3997	[clientscript,boardgames_runesquares_options_update_rune]
3998	[clientscript,settings_client_mode]
3999	[proc,boardgames_runesquares_options_update_rune]
4000	[clientscript,boardgames_runesquares_options_update_time]
4002	[proc,settings_warning_dareeyak_teleport]
4003	[proc,boardgames_runesquares_options_update_time]
4004	[proc,settings_warning_annakarl_teleport]
4005	[proc,settings_warning_ghorrock_teleport]
4006	[proc,settings_warning_ice_plateau_tablet]
4007	[proc,settings_warning_cemetery_tablet]
4008	[proc,settings_warning_crabs_tablet]
4010	[proc,settings_warning_dareeyak_tablet]
4011	[clientscript,boardgames_runesquares_options_update_ranked]
4012	[proc,settings_warning_annakarl_tablet]
4013	[proc,settings_warning_ghorrock_tablet]
4014	[proc,settings_loot_drop_notification]
4015	[proc,settings_drop_item_warnings]
4018	[proc,settings_accept_aid]
4019	[proc,boardgames_runesquares_options_update_ranked]
4020	[clientscript,chatdefault_stopinput]
4022	[clientscript,boardgames_runesquares_update_grid]
4024	[proc,quest_progress_get]
4025	[proc,quest_f2p_index_status_get]
4026	[proc,quest_members_index_status_get]
4027	[proc,miniquest_index_status_get]
4028	[proc,quest_index_status_get]
4029	[proc,quest_status_get]
4030	[proc,quest_status_get_exceptions]
4031	174097
4032	174096
4033	[clientscript,warguild_defence_mini_highlight]
4034	[proc,settings_separate_hours]
4035	[clientscript,publishing_platform]
4037	[clientscript,bolt_pouch_extra_ammo_init]
4038	[clientscript,bolt_pouch_extra_ammo_draw]
4039	[proc,bolt_pouch_extra_ammo_draw]
4040	[clientscript,bolt_pouch_init]
4041	[clientscript,bolt_pouch_setup_entry]
4042	[proc,bolt_pouch_setup_entry]
4043	[clientscript,bolt_pouch_drag_complete]
4044	[clientscript,bolt_pouch_setup_entry_timer]
4045	[proc,bolt_pouch_create_entry]
4046	[clientscript,bolt_pouch_op]
4047	[proc,bolt_pouch_return_slot]
4048	[proc,bolt_pouch_return_id]
4049	[clientscript,tackle_box_init]
4050	[clientscript,tackle_box_draw]
4051	[proc,tackle_box_draw]
4052	[proc,tackle_box_setitem]
4053	[proc,tackle_box_setsideitem]
4054	[proc,tackle_box_setblank]
4055	[clientscript,tackle_box_opitem]
4056	[clientscript,tackle_box_swapitem]
4057	[clientscript,tackle_box_swapsideitem]
4058	[clientscript,tackle_box_dragitem]
4059	[proc,tackle_box_button_draw]
4060	[clientscript,tackle_box_button_op]
4061	[clientscript,tackle_box_side_init]
4062	[clientscript,tackle_box_side_draw]
4063	[proc,tackle_box_side_draw]
4071	[clientscript,tempoross_hud_init]
4074	[clientscript,tempoross_hud_update]
4075	[proc,tempoross_hud_update_bar]
4076	[clientscript,tempoross_hud_bar_movement]
4077	[proc,tempoross_hud_bar_movement]
4078	[proc,debug_mes]
4079	[proc,chatdefault_restoreinput]
4080	[clientscript,collection_init_frame]
4082	[proc,collection_search_init]
4083	[proc,collection_search_button]
4084	[clientscript,collection_toggle_search]
4085	[proc,collection_toggle_search]
4086	[proc,collection_searchbar_create]
4087	[clientscript,collection_search_button_press]
4088	[proc,collection_searchbar_setup_op]
4089	[clientscript,collection_settings_show_keyboard]
4090	[clientscript,collection_searchbar_op]
4091	[proc,collection_searchbar_op]
4092	[clientscript,collection_searchbar_mouseover]
4093	[clientscript,collection_searchbar_mouseleave]
4094	[clientscript,collection_searchbar_keypress]
4095	[proc,collection_searchbar_set_text]
4096	[proc,collection_populate_search_results]
4097	[proc,collection_search_scrollbar]
4098	[proc,collection_search_display_item]
4099	[proc,collection_display_header]
4101	[proc,collection_search_keyboard_listener]
4104	[proc,emote_set_graphic]
4105	[proc,emote_op1_configure]
4106	[proc,emote_op2_configure]
4107	[clientscript,camdozaal_ramarno_shop_setup]
4110	[proc,camdozaal_ramarno_shop_setup_button]
4111	[clientscript,camdozaal_vault_init]
4112	[clientscript,camdozaal_timer_update]
4113	[clientscript,camdozaal_timer_pause]
4114	[clientscript,camdozaal_timer]
4115	[proc,camdozaal_timer_text]
4123	[clientscript,soul_wars_rewards_drawconfirm]
4125	[clientscript,side_container_init]
4126	[clientscript,side_container_reload]
4127	[proc,side_container_reload]
4131	[proc,magic_spellbook_unlocked]
4133	[proc,arceuus_autocast_spellpos]
4141	[clientscript,tutorial_displayname_onkey]
4158	[proc,getbit_range]
4159	[proc,setbit_range]
4160	[proc,clearbit_range]
4161	[proc,setbit_range_toint]
4162	[clientscript,roguetrader_init]
4163	[proc,roguetrader_game_size]
4164	[proc,roguetrader_draw_frames]
4165	[proc,roguetrader_draw_runes]
4166	[clientscript,roguetrader_selectrune_transmit]
4167	[clientscript,roguetrader_selectrune]
4168	[proc,roguetrader_draw_game]
4170	[clientscript,roguetrader_draw_game_transmit]
4171	[clientscript,roguetrader_placerune]
4174	[proc,roguetrader_tile_get]
4175	[proc,roguetrader_tile_set]
4176	[clientscript,trawler_start_toggle]
4177	[clientscript,trawler_start_setgraphic]
4178	[proc,trawler_start_setgraphic]
4179	[clientscript,chat_sendabusereport]
4180	[proc,settings_colour_choose_transmit]
4181	[proc,settings_get_colour]
4183	[clientscript,settings_colour_input_click]
4184	[clientscript,settings_colour_input_update]
4192	[proc,colour_to_string]
4194	[proc,sin_deg]
4195	[proc,cos_deg]
4196	[clientscript,audio_tool]
4197	[proc,audio_draw_menu]
4198	[proc,audio_menu]
4199	[proc,audio_menu_createentry_indexed]
4200	[clientscript,audio_category_select]
4201	[proc,audio_category_select]
4203	[clientscript,audio_synth_play]
4204	[clientscript,audio_loop_click]
4216	[proc,thinbox_specific]
4217	[clientscript,loading_icon_init]
4218	[proc,loading_icon_init]
4219	[proc,loading_icon_draw]
4220	[clientscript,loading_icon_resize]
4221	176657
4222	176656
4223	[clientscript,clan_members_init]
4229	[proc,clan_members_setsize]
4230	[clientscript,clan_members_checksize]
4231	[clientscript,clan_members_draw]
4232	[proc,clan_members_draw]
4233	[clientscript,clan_members_hover]
4234	[clientscript,clan_members_op]
4235	[clientscript,clan_members_buttonreset]
4236	[proc,clan_members_filtercheck]
4237	[proc,clan_members_filter_init]
4238	[clientscript,clan_members_filter_setrank]
4239	[proc,clan_members_filter_setrank]
4241	[clientscript,clan_members_filter_hover]
4243	[clientscript,clan_members_filter_op]
4245	[proc,clan_members_searching]
4246	[proc,clan_members_filtername]
4247	[proc,clan_members_search_setbutton]
4248	[clientscript,clan_members_search_refresh]
4249	[clientscript,clan_members_search_toggle]
4250	[clientscript,clan_members_search_drawbutton]
4252	[proc,clan_members_search_drawbutton]
4253	[proc,clan_members_showrank]
4254	[proc,clan_members_showjoined]
4263	[clientscript,clan_interests]
4264	[proc,clan_interests_setsize]
4265	[clientscript,clan_interests_checksetsize]
4266	[clientscript,clan_interests_synch]
4267	[proc,clan_interests_synch]
4268	[clientscript,clan_interests_op]
4269	[clientscript,clan_banned_init]
4270	[clientscript,clan_banned_draw]
4271	[proc,clan_banned_draw]
4272	[proc,clan_banned_setsize]
4273	[clientscript,clan_banned_checksize]
4274	[proc,clan_banned_wipedisplay]
4275	[proc,clan_banned_populatedisplay]
4276	[clientscript,clan_banned_op]
4277	[clientscript,clan_banned_buttonreset]
4278	[clientscript,clan_banned_unban]
4279	[clientscript,clan_banned_blocker]
4280	[proc,clan_banned_blocker]
4281	[proc,clan_banned_search_setbutton]
4282	[clientscript,clan_banned_search_refresh]
4283	[clientscript,clan_banned_search_toggle]
4284	[clientscript,clan_info_init]
4285	[clientscript,clan_info_draw]
4286	[proc,clan_info_draw]
4289	[clientscript,clan_info_static]
4290	[clientscript,clan_info_editable]
4291	[proc,clan_info_setsize]
4292	[clientscript,clan_info_checksize]
4294	[clientscript,clan_info_editable_hover]
4295	[clientscript,clan_info_editable_op]
4296	[clientscript,clan_info_editable_reset]
4299	[proc,clan_interfacetitle]
4300	[clientscript,clan_interfacetitle_settitle]
4301	[proc,clan_interfacetitle_settitle]
4307	[clientscript,clan_ranktitles_init]
4308	[proc,clan_ranktitles_setsize]
4309	[clientscript,clan_ranktitles_checksize]
4310	[clientscript,clan_ranktitles_draw]
4311	[proc,clan_ranktitles_draw]
4312	[clientscript,clan_ranktitle_wipe]
4313	[clientscript,clan_ranktitle_dragcomplete]
4314	[clientscript,clan_ranktitle_dragtitle]
4315	[clientscript,clan_ranktitle_dragtimeout]
4316	[clientscript,clan_rankpopup]
4317	[proc,clan_rankpopup_setsize]
4318	[clientscript,clan_rankpopup_checksize]
4319	[clientscript,clan_rankpopup_updatetitle]
4320	[proc,clan_rankpopup_updatetitle]
4321	[clientscript,clan_hall_piano]
4330	[clientscript,clan_hall_init]
4332	[proc,clan_hall_settings_create_toggle]
4333	[proc,clan_hall_settings_get_value]
4334	[proc,clan_hall_settings_set_value]
4336	[proc,clan_hall_settings_set_toggle]
4337	[clientscript,clan_hall_settings_toggle_op]
4341	[clientscript,clan_storage_main_init]
4342	[clientscript,clan_storage_main_hint]
4343	[proc,clan_storage_main_hint]
4344	[clientscript,clan_storage_main_drawcoins]
4345	[proc,clan_storage_main_drawcoins]
4346	[clientscript,clan_storage_opitem]
4347	[clientscript,clan_storage_side_init]
4348	[clientscript,clan_storage_side_drawcoins]
4349	[proc,clan_storage_side_drawcoins]
4350	[clientscript,clan_storage_button_op]
4351	[clientscript,clan_storage_button_reset]
4352	[proc,clan_storage_button_reset]
4353	[proc,clan_storage_button_draw]
4354	[clientscript,clan_board_init]
4357	[clientscript,clan_board_addline]
4358	[proc,clan_board_entry_init]
4361	[clientscript,clan_board_entry_op]
4362	[clientscript,clan_board_entry_reset]
4363	[proc,clan_board_setsort]
4364	[proc,clan_board_sortbutton_draw]
4365	[clientscript,clan_board_clicksort]
4366	[clientscript,clan_board_filterupdate]
4367	[proc,clan_board_filterupdate]
4369	[clientscript,clan_board_filterop]
4370	[clientscript,clan_board_filterreset]
4372	[proc,clan_board_setsize]
4373	[clientscript,clan_board_checksize]
4374	[clientscript,clan_applicants_init]
4376	[clientscript,clan_applicants_addapplicant]
4377	[proc,clan_applicants_entry_init]
4378	[clientscript,clan_applicants_entry_op]
4379	[clientscript,clan_applicants_entry_reset]
4380	[clientscript,clan_applicants_setcolour]
4381	[proc,clan_applicants_setcolour]
4382	[proc,clan_applicants_setsize]
4383	[clientscript,clan_applicants_checksize]
4388	[proc,clan_property_updatebutton]
4389	[clientscript,clan_applicants_frameinit]
4390	[clientscript,clan_applicants_framedraw]
4391	[proc,clan_applicants_framedraw]
4392	[clientscript,clan_debug_profile]
4395	[clientscript,clan_sidepanel_init]
4396	[clientscript,clan_sidepanel_draw]
4397	[proc,clan_sidepanel_draw]
4398	[clientscript,clan_sidepanel_chatmute]
4399	[proc,clan_sidepanel_drawchannel]
4400	[clientscript,clan_sidepanel_op]
4401	[proc,clan_sidepanel_op]
4402	[clientscript,clan_sidepanel_refresh_init]
4403	[clientscript,clan_sidepanel_refresh_op]
4404	[clientscript,clan_sidepanel_refresh_reset]
4405	[proc,clan_sidepanel_refresh_reset]
4406	[clientscript,clan_events_init]
4407	[clientscript,clan_events_addevent]
4408	[proc,clan_events_create_text_component]
4409	[proc,clan_events_setsize]
4410	[clientscript,clan_events_checksize]
4411	[proc,clan_events_wipedisplay]
4412	[proc,clan_events_has_permission]
4413	[clientscript,clan_events_create_init]
4417	[proc,settings_int_date]
4418	[clientscript,clan_events_selection_updatetext]
4419	[proc,clan_events_selection_updatetext]
4420	[clientscript,clan_events_int_updatetext]
4421	[proc,clan_events_int_updatetext]
4429	[proc,clan_outfit_draw_button]
4430	[clientscript,clan_outfit_button_click]
4431	[clientscript,clan_outfit]
4433	[proc,clan_outfit_draw_colour_icons]
4434	[clientscript,clan_outfit_redraw]
4435	[proc,clan_outfit_draw_icon]
4436	[proc,clan_outfit_draw_colour]
4437	[proc,clan_outfit_draw_arrow]
4438	[clientscript,clan_outfit_arrow_click]
4439	[clientscript,clan_outfit_colour_click]
4440	[proc,clan_outfit_icon_get]
4441	[proc,clan_outfit_icon_get_all]
4443	[proc,clan_outfit_icon_set]
4444	[clientscript,clan_icon_click]
4445	[clientscript,clan_create_side_init]
4446	[clientscript,clan_create_member_invite]
4448	[clientscript,clan_create_side_list_players]
4449	[proc,clan_create_side_list_players]
4450	[proc,clan_create_player_component]
4452	[proc,clan_stonebutton_target_init]
4455	[clientscript,clan_create_targetmode]
4456	[proc,clan_permission_get]
4457	[clientscript,clan_permission_init]
4458	[proc,clan_permissions_setsize]
4460	[proc,clan_permissions_categorytitle]
4461	[clientscript,clan_permissions_indexop]
4462	[clientscript,clan_permissions_titlereset]
4463	[proc,clan_permissions_buttoninit]
4464	[clientscript,clan_permissions_buttonresynch]
4465	[proc,clan_permissions_buttonresynch]
4466	[clientscript,clan_permissions_buttonhover]
4467	[clientscript,clan_permissions_buttonop]
4468	[clientscript,clan_permissions_buttontimeout]
4469	[clientscript,clan_permission_settitle]
4470	[proc,clan_permission_settitle]
4471	[clientscript,side_channels_init]
4474	[proc,side_channels_tabinit]
4475	[proc,side_channels_settab]
4476	[clientscript,side_channels_drawtab]
4477	[proc,side_channels_drawtab]
4478	[clientscript,side_channels_optab]
4479	[proc,meslayer_mode18]
4480	[clientscript,meslayer_mode19]
4482	[clientscript,chat_button_hover]
4495	[proc,tob_partydetails_back_button]
4508	[proc,hex_string_to_int]
4511	[proc,ancient_autocast_spellpos]
4512	[proc,standard_autocast_spellpos]
4561	[proc,welcome_screen_toggle]
4564	[clientscript,settings_player_attack_options]
4565	[clientscript,settings_npc_attack_options]
4566	[clientscript,settings_fps_limit]
4567	[proc,settings_side_dropdown_create]
4568	[clientscript,settings_side_dropdown_open]
4569	[clientscript,settings_side_dropdown_op]
4570	[clientscript,settings_side_dropdown_close]
4571	[proc,settings_side_dropdown_close]
4572	[clientscript,settings_side_dropdown_timer]
4573	[clientscript,settings_side_dropdown_transmit]
4574	[proc,settings_side_dropdown_update]
4575	[clientscript,settings_side_dropdown_hover]
4577	[proc,settings_toggle_roof]
4580	[proc,settings_attack_style]
4581	[proc,settings_mouseover_tooltips]
4582	[proc,settings_mouseover_text]
4583	[proc,settings_antidrag]
4584	[proc,settings_regen_indicator]
4585	[proc,settings_helper_cox]
4586	[clientscript,settings_chat_timestamps]
4590	[proc,settings_buff_bar]
4591	[proc,settings_buff_bar_tooltip]
4592	[proc,settings_buff_leagues]
4593	[proc,settings_buff_home_teleport]
4595	[proc,settings_buff_teleblock]
4618	[clientscript,antidrag_toggle]
4619	[proc,antidrag_toggle]
4649	[clientscript,chattimestamp]
4651	[proc,mescoord]
4652	[clientscript,client_type_mes]
4653	[clientscript,helper_cox_init]
4654	[clientscript,helper_cox_op]
4655	[clientscript,helper_cox_transmit]
4656	[clientscript,helper_cox_content]
4657	[proc,helper_cox_content]
4658	[clientscript,helper_cox_data]
4659	[clientscript,helper_cox_timer]
4660	[proc,helper_cox_data]
4661	[proc,helper_cox_timer_convert]
4662	[clientscript,raids_overlay_content_transmit]
4663	[proc,raids_overlay_content]
4664	[clientscript,buff_bar_buff_timer]
4665	[clientscript,buff_bar_buff_transmit]
4667	[proc,buff_bar_time_string]
4668	[proc,buff_bar_max_buffs]
4669	[proc,buff_bar_buff_amount]
4670	[proc,buff_bar_position_init]
4671	[clientscript,buff_bar_position_review]
4672	[proc,buff_bar_position_review]
4673	[proc,enhanced_buffs]
4679	[proc,buff_bar_get_transmit]
4682	[proc,buff_bar_get_number]
4683	[proc,buff_bar_buff_show]
4684	[proc,buff_bar_buff_disabled]
4688	[proc,helper_init]
4689	[clientscript,helper_setup]
4690	[clientscript,helper_background]
4691	[proc,helper_background]
4695	[proc,helper_generic_setup]
4696	[clientscript,helper_generic_op]
4697	[proc,helper_generic_reset_op]
4698	[proc,helper_generic_reset]
4699	[proc,helper_generic_update_text]
4702	[proc,helper_generic_set_height]
4704	[proc,helper_generic_set_size]
4705	[proc,helper_generic_add_title]
4706	[proc,helper_generic_add_text]
4707	[proc,helper_generic_add_data]
4708	[proc,helper_generic_draw_title]
4709	[proc,helper_generic_draw_text]
4710	[proc,helper_generic_draw_data]
4711	[proc,helper_generic_set_type]
4712	[proc,helper_generic_get_type]
4713	[proc,helper_generic_set_string]
4714	[proc,helper_generic_get_string]
4715	[clientscript,helper_new_toplevel]
4727	[proc,mouseover_text]
4728	[proc,mouseover_tooltip]
4744	[clientscript,highlight_npc_on]
4745	[clientscript,highlight_npc_off]
4746	[clientscript,highlight_npctype_on]
4747	[clientscript,highlight_npctype_off]
4748	[clientscript,highlight_loc_on]
4749	[clientscript,highlight_loc_off]
4750	[clientscript,highlight_loctype_on]
4751	[clientscript,highlight_loctype_off]
4752	[clientscript,highlight_obj_on]
4753	[clientscript,highlight_obj_off]
4754	[clientscript,highlight_objtype_on]
4755	[clientscript,highlight_objtype_off]
4756	[clientscript,highlight_player_on]
4757	[clientscript,highlight_player_off]
4758	[clientscript,highlight_tile_on]
4759	[clientscript,highlight_tile_off]
4792	[proc,worldmap_tooltip_override]
4849	[proc,dynamic_stone_button_filled]
4850	178449
4851	178448
4863	179217
4864	179473
4865	179729
4866	179985
4867	180241
4868	180497
4869	180753
4870	181009
4871	181265
4872	181521
4873	181777
4874	182033
4875	182289
4876	182545
4877	182801
4878	183057
4879	183313
4880	183569
4881	183825
4882	184081
4883	184337
4884	184593
4885	184849
4886	185105
4887	185361
4888	185617
4889	185873
4890	186129
4891	186385
4892	186641
4893	186897
4894	187153
4895	187409
4896	187665
4897	187921
4898	188177
4899	188433
4900	188689
4901	188945
4902	189201
4903	189457
4904	189713
4905	189969
4906	190225
4907	190481
4908	190737
4909	190993
4910	191249
4911	191505
4912	191761
4913	192017
4914	192273
4915	192529
4916	192785
4917	193041
4918	193297
4919	193553
4920	193809
4921	194065
4922	194321
4923	194577
4924	194833
4925	195089
4926	195345
4927	195601
4928	195857
4929	196113
4930	196369
4931	196625
4932	196881
4933	197137
4934	197393
4935	197649
4936	197905
4937	198161
4938	198417
4939	198673
4940	198929
4941	199185
4942	199441
4943	199697
4944	199953
4945	200209
4946	200465
4947	200721
4948	200977
4949	201233
4950	201489
4951	201745
4952	202001
4953	202257
4954	202513
4955	202769
4956	203025
4957	203281
4958	203537
4959	203793
4960	204049
4961	204305
4962	204561
4963	204817
4964	205073
4965	205329
4966	205585
4967	205841
4968	206097
4969	206353
4970	206609
4971	206865
4972	207121
4973	207377
4974	207633
4975	207889
4976	208145
4977	208401
4978	208657
4979	208913
4980	209169
4981	209425
4982	209681
4983	209937
4984	210193
4985	210449
4986	210705
4987	210961
4988	211217
4989	211473
4990	211729
4991	211985
4992	212241
4993	212497
4994	212753
4995	213009
4996	213265
4997	213521
4998	213777
4999	214033
5000	214289
5001	214545
5002	214801
5003	215057
5004	215313
5005	215569
5006	215825
5007	216081
5008	216337
5009	216593
5010	216849
5011	217105
5012	217361
5013	217617
5014	179216
5015	179472
5016	179728
5017	179984
5018	180240
5019	180496
5020	180752
5021	181008
5022	181264
5023	181520
5024	181776
5025	182032
5026	182288
5027	182544
5028	182800
5029	183056
5030	183312
5031	183568
5032	183824
5033	184080
5034	184336
5035	184592
5036	184848
5037	185104
5038	185360
5039	185616
5040	185872
5041	186128
5042	186384
5043	186640
5044	186896
5045	187152
5046	187408
5047	187664
5048	187920
5049	188176
5050	188432
5051	188688
5052	188944
5053	189200
5054	189456
5055	189712
5056	189968
5057	190224
5058	190480
5059	190736
5060	190992
5061	191248
5062	191504
5063	191760
5064	192016
5065	192272
5066	192528
5067	192784
5068	193040
5069	193296
5070	193552
5071	193808
5072	194064
5073	194320
5074	194576
5075	194832
5076	195088
5077	195344
5078	195600
5079	195856
5080	196112
5081	196368
5082	196624
5083	196880
5084	197136
5085	197392
5086	197648
5087	197904
5088	198160
5089	198416
5090	198672
5091	198928
5092	199184
5093	199440
5094	199696
5095	199952
5096	200208
5097	200464
5098	200720
5099	200976
5100	201232
5101	201488
5102	201744
5103	202000
5104	202256
5105	202512
5106	202768
5107	203024
5108	203280
5109	203536
5110	203792
5111	204048
5112	204304
5113	204560
5114	204816
5115	205072
5116	205328
5117	205584
5118	205840
5119	206096
5120	206352
5121	206608
5122	206864
5123	207120
5124	207376
5125	207632
5126	207888
5127	208144
5128	208400
5129	208656
5130	208912
5131	209168
5132	209424
5133	209680
5134	209936
5135	210192
5136	210448
5137	210704
5138	210960
5139	211216
5140	211472
5141	211728
5142	211984
5143	212240
5144	212496
5145	212752
5146	213008
5147	213264
5148	213520
5149	213776
5150	214032
5151	214288
5152	214544
5153	214800
5154	215056
5155	215312
5156	215568
5157	215824
5158	216080
5159	216336
5160	216592
5161	216848
5162	217104
5163	217360
5164	217616
5165	[clientscript,helper_agility_highlight_shortcuts]
5166	[proc,helper_agility_highlight_shortcuts]
5167	[clientscript,helper_agility_highlight_course]
5168	[proc,helper_agility_highlight_starts]
5169	[proc,helper_agility_highlight_course]
5170	[clientscript,helper_agility_init]
5171	[proc,helper_agility_reset_data]
5173	[clientscript,helper_agility_start_lap]
5174	[clientscript,helper_agility_end_lap]
5175	[clientscript,helper_agility_add_xp]
5176	[clientscript,helper_agility_level_up]
5177	[clientscript,helper_agility_update_title]
5178	[clientscript,helper_agility_update_laps_completed]
5181	[clientscript,helper_agility_update_next_level]
5182	[proc,helper_agility_draw_content]
5184	[proc,settings_helper_agility]
5185	[proc,settings_helper_agility_highlight_obstacles]
5186	[proc,settings_helper_agility_highlight_shortcuts]
5187	[proc,settings_buff_ammo]
5190	[proc,settings_minimap_lock]
5191	[clientscript,settings_minimap_lock]
5197	-464
5203	-463
5209	-465
5221	[clientscript,summary_sidepanel_timer_draw]
