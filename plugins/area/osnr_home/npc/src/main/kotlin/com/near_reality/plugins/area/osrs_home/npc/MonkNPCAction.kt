package com.near_reality.plugins.area.osrs_home.npc

import com.near_reality.scripts.npc.actions.NPCActionScript
import com.zenyte.game.world.entity.npc.NpcId.*

class MonkNPCAction : NPCActionScript() {

    init {
        npcs(<PERSON><PERSON><PERSON>, MONK_1159, MON<PERSON>_1171, MON<PERSON>_2579, MONK_4068)

        "Talk-To" {
            player.dialogueManager.start(<PERSON><PERSON><PERSON><PERSON>(player, npc))
        }
    }

}