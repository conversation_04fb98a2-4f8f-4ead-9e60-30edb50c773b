package com.near_reality.plugins.area.osrs_home.npc

import com.near_reality.scripts.npc.actions.NPCActionScript
import com.zenyte.game.GameInterface
import com.zenyte.game.world.entity.player.dialogue.dialogue
import com.zenyte.game.world.entity.player.dialogue.options

class SigilMasterNPCAction : NPCActionScript() {

    init {
        npcs(6093)
        "Talk-to" {
            player.dialogue(npc) {
                options("Sigil Master Options") {
                    dialogueOption("Sigil Manager", true) {
                        GameInterface.SIGILS.open(player)
                    }
                    dialogueOption("What are sigils?", false) {
                        npc("Sigils are ancient fragments of forgotten power.")
                        npc("When activated, a sigil can provide immense power to the player that can make you rise above any encounter.")
                        npc("There are 3 tiers of sigils, with tier 3's being the most rare and most powerful.")
                        npc("Sigils are dropped while bossing and slaying. The harder the boss, the easier rate to get sigils. Sigils do not drop from Raids.")
                        npc("Another note, Sigils can be toggled at any time, and they have a 6 hour timer while activated. So use them wisely, as they crumble to dust when the time is up!")
                        return@dialogueOption
                    }

                }
            }
        }

        "Sigil Manager" {
            GameInterface.SIGILS.open(player)
        }
    }

}