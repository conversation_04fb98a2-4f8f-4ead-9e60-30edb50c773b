package com.zenyte.game.content.boss.nightmare;

import com.zenyte.game.util.ZoneBorders;
import com.zenyte.game.world.entity.Location;

public enum NightmareFlowerAttackData {
	FIRST(new Location[] {
			new Location(3872, 9941, 3),
			new Location(3872, 9942, 3),
			new Location(3872, 9943, 3),
			new Location(3872, 9944, 3),
			new Location(3872, 9945, 3),
			new Location(3872, 9946, 3),
			new Location(3872, 9947, 3),
			new Location(3872, 9948, 3),
			new Location(3872, 9949, 3),
			new Location(3872, 9950, 3),
			new Location(3872, 9951, 3),
			new Location(3871, 9951, 3),
			new Location(3870, 9951, 3),
			new Location(3869, 9951, 3),
			new Location(3868, 9951, 3),
			new Location(3867, 9951, 3),
			new Location(3866, 9951, 3),
			new Location(3865, 9951, 3),
			new Location(3864, 9951, 3),
			new Location(3863, 9951, 3),
	}, new Location[]{
			new Location(3872, 9961, 3),
			new Location(3872, 9960, 3),
			new Location(3872, 9959, 3),
			new Location(3872, 9958, 3),
			new Location(3872, 9957, 3),
			new Location(3872, 9956, 3),
			new Location(3872, 9955, 3),
			new Location(3872, 9954, 3),
			new Location(3872, 9953, 3),
			new Location(3872, 9952, 3),
			new Location(3881, 9951, 3),
			new Location(3880, 9951, 3),
			new Location(3879, 9951, 3),
			new Location(3878, 9951, 3),
			new Location(3877, 9951, 3),
			new Location(3876, 9951, 3),
			new Location(3875, 9951, 3),
			new Location(3874, 9951, 3),
			new Location(3873, 9951, 3),
	}, new ZoneBorders(3862, 9940, 3872, 9951, 3)),
	SECOND(new Location[] {
			new Location(3872, 9951, 3),
			new Location(3871, 9951, 3),
			new Location(3870, 9951, 3),
			new Location(3869, 9951, 3),
			new Location(3868, 9951, 3),
			new Location(3867, 9951, 3),
			new Location(3866, 9951, 3),
			new Location(3865, 9951, 3),
			new Location(3864, 9951, 3),
			new Location(3863, 9951, 3),
			new Location(3872, 9961, 3),
			new Location(3872, 9960, 3),
			new Location(3872, 9959, 3),
			new Location(3872, 9958, 3),
			new Location(3872, 9957, 3),
			new Location(3872, 9956, 3),
			new Location(3872, 9955, 3),
			new Location(3872, 9954, 3),
			new Location(3872, 9953, 3),
			new Location(3872, 9952, 3),
	}, new Location[] {
			new Location(3872, 9941, 3),
			new Location(3872, 9942, 3),
			new Location(3872, 9943, 3),
			new Location(3872, 9944, 3),
			new Location(3872, 9945, 3),
			new Location(3872, 9946, 3),
			new Location(3872, 9947, 3),
			new Location(3872, 9948, 3),
			new Location(3872, 9949, 3),
			new Location(3872, 9950, 3),
			new Location(3881, 9951, 3),
			new Location(3880, 9951, 3),
			new Location(3879, 9951, 3),
			new Location(3878, 9951, 3),
			new Location(3877, 9951, 3),
			new Location(3876, 9951, 3),
			new Location(3875, 9951, 3),
			new Location(3874, 9951, 3),
			new Location(3873, 9951, 3),
	}, new ZoneBorders(3863, 9951, 3872, 9961, 3)),
	THIRD(new Location[] {
			new Location(3872, 9961, 3),
			new Location(3872, 9960, 3),
			new Location(3872, 9959, 3),
			new Location(3872, 9958, 3),
			new Location(3872, 9957, 3),
			new Location(3872, 9956, 3),
			new Location(3872, 9955, 3),
			new Location(3872, 9954, 3),
			new Location(3872, 9953, 3),
			new Location(3872, 9952, 3),
			new Location(3872, 9951, 3),
			new Location(3881, 9951, 3),
			new Location(3880, 9951, 3),
			new Location(3879, 9951, 3),
			new Location(3878, 9951, 3),
			new Location(3877, 9951, 3),
			new Location(3876, 9951, 3),
			new Location(3875, 9951, 3),
			new Location(3874, 9951, 3),
			new Location(3873, 9951, 3),
	}, new Location[] {
			new Location(3872, 9941, 3),
			new Location(3872, 9942, 3),
			new Location(3872, 9943, 3),
			new Location(3872, 9944, 3),
			new Location(3872, 9945, 3),
			new Location(3872, 9946, 3),
			new Location(3872, 9947, 3),
			new Location(3872, 9948, 3),
			new Location(3872, 9949, 3),
			new Location(3872, 9950, 3),
			new Location(3871, 9951, 3),
			new Location(3870, 9951, 3),
			new Location(3869, 9951, 3),
			new Location(3868, 9951, 3),
			new Location(3867, 9951, 3),
			new Location(3866, 9951, 3),
			new Location(3865, 9951, 3),
			new Location(3864, 9951, 3),
			new Location(3863, 9951, 3),
	}, new ZoneBorders(3872, 9951, 3882, 9961, 3)),
	FOURTH(new Location[] {
			new Location(3872, 9951, 3),
			new Location(3881, 9951, 3),
			new Location(3880, 9951, 3),
			new Location(3879, 9951, 3),
			new Location(3878, 9951, 3),
			new Location(3877, 9951, 3),
			new Location(3876, 9951, 3),
			new Location(3875, 9951, 3),
			new Location(3874, 9951, 3),
			new Location(3873, 9951, 3),
			new Location(3872, 9941, 3),
			new Location(3872, 9942, 3),
			new Location(3872, 9943, 3),
			new Location(3872, 9944, 3),
			new Location(3872, 9945, 3),
			new Location(3872, 9946, 3),
			new Location(3872, 9947, 3),
			new Location(3872, 9948, 3),
			new Location(3872, 9949, 3),
			new Location(3872, 9950, 3),
	}, new Location[] {
			new Location(3872, 9961, 3),
			new Location(3872, 9960, 3),
			new Location(3872, 9959, 3),
			new Location(3872, 9958, 3),
			new Location(3872, 9957, 3),
			new Location(3872, 9956, 3),
			new Location(3872, 9955, 3),
			new Location(3872, 9954, 3),
			new Location(3872, 9953, 3),
			new Location(3872, 9952, 3),
			new Location(3871, 9951, 3),
			new Location(3870, 9951, 3),
			new Location(3869, 9951, 3),
			new Location(3868, 9951, 3),
			new Location(3867, 9951, 3),
			new Location(3866, 9951, 3),
			new Location(3865, 9951, 3),
			new Location(3864, 9951, 3),
			new Location(3863, 9951, 3),
	}, new ZoneBorders(3872, 9941, 3881, 9951, 3));

	private final Location[] blossoms, berries;
	private final ZoneBorders safeArea;
	public static final NightmareFlowerAttackData[] values = values();

	NightmareFlowerAttackData(Location[] blossoms, Location[] berries, ZoneBorders safeArea) {
		this.blossoms = blossoms;
		this.berries = berries;
		this.safeArea = safeArea;
	}

	public Location[] getBlossoms() {
		return blossoms;
	}

	public Location[] getBerries() {
		return berries;
	}

	public ZoneBorders getSafeArea() {
		return safeArea;
	}

}