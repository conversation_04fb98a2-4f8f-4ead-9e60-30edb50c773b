package com.zenyte.game.content.pyramidplunder;

import com.zenyte.game.world.entity.ImmutableLocation;

/**
 * <AUTHOR>
 * @since 4/5/2020
 */
public class PyramidPlunderConstants {
    public static final ImmutableLocation OUTSIDE_PYRAMID = new ImmutableLocation(3289, 2801, 0);
    public static final int SARCOPHAGUS = 26626;
    public static final int GRAND_GOLDEN_CHEST = 26616;
    public static final int ANONYMOUS_DOOR_NORTH = 26622;
    public static final int ANONYMOUS_DOOR_EAST = 26623;
    public static final int ANONYMOUS_DOOR_SOUTH = 26624;
    public static final int ANONYMOUS_DOOR_WEST = 26625;
}
