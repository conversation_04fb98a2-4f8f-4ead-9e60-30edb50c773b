package com.zenyte.game.content.skills.agility.shortcut;

import com.zenyte.game.content.achievementdiary.AreaPredicate;
import com.zenyte.game.content.achievementdiary.diaries.ArdougneDiary;
import com.zenyte.game.content.achievementdiary.diaries.FaladorDiary;
import com.zenyte.game.content.skills.agility.Shortcut;
import com.zenyte.game.task.WorldTask;
import com.zenyte.game.task.WorldTasksManager;
import com.zenyte.game.util.Direction;
import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.masks.Animation;
import com.zenyte.game.world.entity.masks.Graphics;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.cutscene.actions.FadeScreenAction;
import com.zenyte.game.world.object.WorldObject;

import java.util.HashMap;
import java.util.Map;

public class GrappleWall implements Shortcut {

	private static final Map<Integer, Location> GRAPPLES = new HashMap<Integer, Location>();

	private static final Animation CROSSBOW = new Animation(4455);
	private static final Graphics GRAPPLE = new Graphics(760, 0, 100);
	private static final int MITH_GRAPPLE = 9419;

	private static final Location FALLY_NORTH_GRAPPLE = new Location(3033, 3390, 0);
	private static final Location FALLY_SOUTH_GRAPPLE = new Location(3032, 3388, 0);
	private static final Location FALLY_SOUTH_GRAPPLE_OBJECT = new Location(3032, 3389, 0);
	private static final Location FALLY_WALKWAY_NORTH = new Location(3033, 3389, 1);
	private static final Location FALLY_WALKWAY_SOUTH = new Location(3032, 3389, 1);

	private static final Location YANILLE_NORTH_GRAPPLE = new Location(2556, 3074, 0);
	private static final Location YANILLE_SOUTH_GRAPPLE = new Location(2556, 3073, 0);
	private static final Location YANILLE_WALKWAY_NORTH = new Location(2556, 3074, 1);
	private static final Location YANILLE_WALKWAY_SOUTH = new Location(2556, 3073, 1);

	static {
		GRAPPLES.put(FALLY_NORTH_GRAPPLE.getPositionHash(), FALLY_WALKWAY_NORTH);
		GRAPPLES.put(FALLY_SOUTH_GRAPPLE_OBJECT.getPositionHash(), FALLY_WALKWAY_SOUTH);
		GRAPPLES.put(YANILLE_NORTH_GRAPPLE.getPositionHash(), YANILLE_WALKWAY_NORTH);
		GRAPPLES.put(YANILLE_SOUTH_GRAPPLE.getPositionHash(), YANILLE_WALKWAY_SOUTH);

		GRAPPLES.put(FALLY_WALKWAY_NORTH.getPositionHash(), FALLY_NORTH_GRAPPLE);
		GRAPPLES.put(FALLY_WALKWAY_SOUTH.getPositionHash(), FALLY_SOUTH_GRAPPLE);
		GRAPPLES.put(YANILLE_WALKWAY_NORTH.getPositionHash(), new Location(2556, 3075, 0)); // North walkway goes to north standing position
		GRAPPLES.put(YANILLE_WALKWAY_SOUTH.getPositionHash(), new Location(2556, 3072, 0)); // South walkway goes to south standing position
	}

	@Override
    public boolean preconditions(final Player player, final WorldObject object) {
        if (GRAPPLES.get(object.getPositionHash()) == null) {
            return false;
        }
        if(player.getWeapon() == null || !player.getWeapon().getName().toLowerCase().contains("crossbow")) {
            player.sendMessage("You need a crossbow equipped to do that.");
            return false;
        }
        if(player.getAmmo() == null || player.getAmmo().getId() != MITH_GRAPPLE) {
            player.sendMessage("You need a mithril grapple tipped bolt with a rope to do that.");
            return false;
        }
        return true;
    }

	@Override
	public void startSuccess(final Player player, final WorldObject object) {
		final boolean yanille = object.getRegionId() == 10032;
		final boolean falador = object.getRegionId() == 12084;
		if (yanille) {
			if (object.getPositionHash() == YANILLE_NORTH_GRAPPLE.getPositionHash() ||
				object.getPositionHash() == YANILLE_WALKWAY_NORTH.getPositionHash())
				player.setFaceLocation(new Location(player.getX(), player.getY() - 1, player.getPlane()));
			else if (object.getPositionHash() == YANILLE_SOUTH_GRAPPLE.getPositionHash() ||
					   object.getPositionHash() == YANILLE_WALKWAY_SOUTH.getPositionHash())
				player.setFaceLocation(new Location(player.getX(), player.getY() + 1, player.getPlane()));
			else if (player.getY() == 3073)
				player.faceDirection(Direction.SOUTH);
			else if (player.getY() == 3074)
				player.faceDirection(Direction.NORTH);

		} else if (falador) {
			if (player.getY() == 3390)
				player.faceDirection(Direction.SOUTH);
			else
				player.faceObject(object);
		}

		WorldTasksManager.schedule(new WorldTask() {

			private int ticks;

			@Override
			public void run() {
				if(ticks == 0) {
					player.setAnimation(CROSSBOW);
					player.setGraphics(GRAPPLE);
				} else if(ticks == 8)
					new FadeScreenAction(player, 4).run();
				else if(ticks == 11)
					player.setAnimation(Animation.STOP);
				else if(ticks == 12) {
					player.setLocation(GRAPPLES.get(object.getPositionHash()));
					stop();
					if (yanille)
						player.getAchievementDiaries().update(ArdougneDiary.GRAPPLE_YANILLE_SOUTH_WALL, 1);
					else if (falador)
						player.getAchievementDiaries().update(FaladorDiary.GRAPPLE_FALADOR_WALL, 1);
				}
				ticks++;
			}
		}, 0, 0);
	}

	@Override
	public Location getRouteEvent(final Player player, final WorldObject object) {
		if (object.getRegionId() == 12084) {
			if (object.getPositionHash() == FALLY_NORTH_GRAPPLE.getPositionHash() ||
				object.getPositionHash() == FALLY_WALKWAY_NORTH.getPositionHash()) {
				return FALLY_NORTH_GRAPPLE;
			} else if (object.getPositionHash() == FALLY_SOUTH_GRAPPLE_OBJECT.getPositionHash() ||
					   object.getPositionHash() == FALLY_WALKWAY_SOUTH.getPositionHash()) {
				return FALLY_SOUTH_GRAPPLE;
			}
		}
		return object;
	}

	@Override
	public int getLevel(final WorldObject object) {
		return object.getRegionId() == 10032 ? 39 : 11;
	}

	@Override
	public int distance(final WorldObject object) {
		return 0;
	}

	@Override
	public int[] getObjectIds() {
		return new int[] { 17047, 17049, 17050 };
	}

	@Override
	public int getDuration(final boolean success, final WorldObject object) {
		return 13;
	}

	@Override
	public double getSuccessXp(final WorldObject object) {
		return 0;
	}
	
}
