package com.zenyte.plugins.itemonplayer;

import com.near_reality.game.item.CustomItemId;
import com.zenyte.game.item.Item;
import com.zenyte.game.model.item.ItemOnPlayerPlugin;
import com.zenyte.game.world.entity.player.LogLevel;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.dialogue.Dialogue;

/**
 * <AUTHOR> | 15/06/2019 10:20
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class DonatorBondItemOnPlayerPlugin implements ItemOnPlayerPlugin {
    @Override
    public void handleItemOnPlayerAction(final Player giver, final Item item, final int slot, final Player target) {
        giver.getDialogueManager().start(new Dialogue(giver) {
            @Override
            public void buildDialogue() {
                item(item, "Offer " + target.getName() + " the " + item.getName() + "? You won\'t be able to reclaim it.");
                options(TITLE, new DialogueOption("Offer them the " + item.getName() + ".", () -> {
                    if (player.getInventory().getItem(slot) != item) {
                        return;
                    }
                    target.getDialogueManager().start(new Dialogue(target) {
                        @Override
                        public void buildDialogue() {
                            item(item, giver.getName() + " offers you a " + item.getName() + "! Would you like to accept it?");
                            options(TITLE, new DialogueOption("Accept the " + item.getName() + ".", () -> {
                                if (isUnavailable(giver) || isUnavailable(target)) {
                                    giver.sendMessage("Unable to process request.");
                                    target.sendMessage(giver.getUsername()+" is unavailable.");
                                    return;
                                }
                                if (!target.getInventory().hasFreeSlots()) {
                                    target.sendMessage("You need some free inventory space to accept the " + item.getName() + ".");
                                    giver.sendMessage(target.getUsername()+" does not have enough inventory space to accept the " + item.getName() + ".");
                                    return;
                                }
                                if (giver.getInventory().getItem(slot) != item) {
                                    target.sendMessage(giver.getUsername()+" has cancelled the offer.");
                                    giver.sendMessage("You have cancelled the offer.");
                                    return;
                                }
                                final int result = giver.getInventory().deleteItem(item).getSucceededAmount();
                                if (result > 0) {
                                    target.getInventory().addOrDrop(item);
                                    target.sendMessage("You have accepted the " + item.getName() + " from " + giver.getName() + ".");
                                    giver.sendMessage("You have given the " + item.getName() + " to " + target.getName() + ".");
                                    giver.log(LogLevel.ECO,"[" + giver.getUsername() + "] has given [" + item.getName() + "] to [" + target.getUsername() + "].");
                                    target.log(LogLevel.ECO,"[" + giver.getUsername() + "] has received a [" + item.getName() + "] from [" + target.getUsername() + "].");
                                }
                            }), new DialogueOption("Cancel."));
                        }
                    });
                }), new DialogueOption("Cancel."));
            }
        });
    }

    @Override
    public int[] getItems() {
        return new int[] {CustomItemId.DONATOR_BOND_10, CustomItemId.DONATOR_BOND_25, CustomItemId.DONATOR_BOND_50, CustomItemId.DONATOR_BOND_100};
    }
}
