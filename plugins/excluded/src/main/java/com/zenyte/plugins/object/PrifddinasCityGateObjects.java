package com.zenyte.plugins.object;

import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.entity.player.cutscene.FadeScreen;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.ObjectId;
import com.zenyte.game.world.object.WorldObject;

import java.util.Set;

/**
 * <AUTHOR> (Discord: imslickk)
 * 7/31/2025
 */
public class PrifddinasCityGateObjects implements ObjectAction {

    private static final Location INSIDE_SOUTH_GATE = new Location(3264, 6024, 0);
    private static final Location OUTSIDE_SOUTH_GATE = new Location(2240, 3269, 0);
    private static final Location INSIDE_NORTH_GATE = new Location(3263, 6135, 0);
    private static final Location OUTSIDE_NORTH_GATE = new Location(3264, 6138, 0);
    private static final Location OUTSIDE_EAST_GATE = new Location(2298, 3327, 0);
    private static final Location INSIDE_EAST_GATE = new Location(3319, 6079, 0);
    private static final Location INSIDE_WEST_GATE = new Location(3208, 6080, 0);
    private static final Location OUTSIDE_WEST_GATE = new Location(2181, 3327, 0);
    @Override
    public void handleObjectAction(final Player player, final WorldObject object, final String name, final int optionId, final String option) {
        final int id = object.getId();
        final int x = player.getX();
        final int y = player.getY();

        final Set<Integer> INNER_GATE_IDS = Set.of(ObjectId.CITY_GATE_36518, ObjectId.CITY_GATE_36519);
        final Set<Integer> OUTER_GATE_IDS = Set.of(ObjectId.CITY_GATE_36522, ObjectId.CITY_GATE_36523);

        Location location;

        if (INNER_GATE_IDS.contains(id)) {
            if (y == 3269) location = INSIDE_SOUTH_GATE;
            else if (x == 2298) location = INSIDE_EAST_GATE;
            else if (x == 2181) location = INSIDE_WEST_GATE;
            else {
                location = null;
            }
        } else if (OUTER_GATE_IDS.contains(id)) {
            if (y == 6024) location = OUTSIDE_SOUTH_GATE;
            else if (x == 3319) location = OUTSIDE_EAST_GATE;
            else if (y == 6138) location = INSIDE_NORTH_GATE;
            else if (y == 6135) location = OUTSIDE_NORTH_GATE;
            else if (x == 3208) location = OUTSIDE_WEST_GATE;
            else {
                location = null;
            }
        } else {
            location = null;
        }

        if (location == null) {
            player.sendMessage("Gate doesn't exist. Please report this to an administrator.");
            return;
        }

        new FadeScreen(player, () -> {
            player.getInterfaceHandler().closeInterfaces();
            player.setLocation(location);
        }).fade(2);
    }

    @Override
    public Object[] getObjects() {
        return new Object[] { ObjectId.CITY_GATE_36522, ObjectId.CITY_GATE_36523, ObjectId.CITY_GATE_36518, ObjectId.CITY_GATE_36519 };
    }
}
