package com.zenyte.plugins.object;

import com.zenyte.game.world.entity.Location;
import com.zenyte.game.world.entity.player.Player;
import com.zenyte.game.world.object.ObjectAction;
import com.zenyte.game.world.object.ObjectId;
import com.zenyte.game.world.object.WorldObject;

/**
 * <AUTHOR> (Discord: imslickk)
 */
public class PrifddinasSlayerCaveEntrance implements ObjectAction {

    private static final Location INSIDE_LOCATION = new Location(3225, 12445, 0);

    private static final Location OUTSIDE_LOCATION = new Location(3226, 6046, 0);

    @Override
    public void handleObjectAction(Player player, WorldObject object, String name, int optionId, String option) {
        player.setLocation(object.getId() == ObjectId.CAVE_36690 ? INSIDE_LOCATION : OUTSIDE_LOCATION);
    }

    @Override
    public Object[] getObjects() {
        return new Object[] { ObjectId.CAVE_36690, ObjectId.STEPS_36691 };
    }
}
