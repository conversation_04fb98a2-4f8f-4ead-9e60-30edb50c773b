package com.zenyte.plugins.renewednpc;

import com.zenyte.game.world.entity.npc.NpcId;
import com.zenyte.game.world.entity.npc.actions.NPCPlugin;
import com.zenyte.plugins.dialogue.magebank.ChamberGuardianD;

/**
 * <AUTHOR> | 26/11/2018 19:32
 * @see <a href="https://www.rune-server.ee/members/kris/">Rune-Server profile</a>
 */
public class <PERSON><PERSON><PERSON><PERSON> extends NPCPlugin {

    @Override
    public void handle() {
        bind("Talk-to", (player, npc) -> player.getDialogueManager().start(new <PERSON><PERSON><PERSON>ianD(player, npc)));
    }

    @Override
    public int[] getNPCs() {
        return new int[] { NpcId.CHAMBER_GUARDIAN };
    }
}
