package com.near_reality.tools.backups.player

import com.near_reality.tools.backups.Backup
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.login.LoginManager
import java.nio.file.Files
import java.time.ZoneId
import kotlin.io.path.exists
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes

/**
 * Represents a type of backup for players.
 *
 * <AUTHOR> (original)
 * <AUTHOR> (Discord: imslickk) (major overhaul)
 */
sealed class PlayerBackup(
    period: Duration,
    fileExtension: String,
    folderNameSuffix: String,
) : Backup<Player>(period, fileExtension, folderNameSuffix, useCaching = true) {

    override fun toName(instance: Player): String =
        instance.username

    override fun getOriginalFileTimestamp(instance: Player): Long? {
        val fileName = "${instance.username}.json"
        val filePath = LoginManager.PLAYER_SAVE_DIRECTORY.resolve(fileName)
        return if (filePath.exists()) {
            try {
                val fileTime = Files.getLastModifiedTime(filePath)
                val estZone = ZoneId.of("America/New_York")
                val systemZone = ZoneId.systemDefault()
                val fileInstant = fileTime.toInstant()
                val estZonedDateTime = fileInstant.atZone(systemZone).withZoneSameInstant(estZone)
                estZonedDateTime.toInstant().toEpochMilli()
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }

    object Full : PlayerBackup(15.minutes, "json", "_characters") {
        override suspend fun serialise(instance: Player): ByteArray =
            LoginManager.gson.get().toJson(instance).toByteArray()
    }
}
