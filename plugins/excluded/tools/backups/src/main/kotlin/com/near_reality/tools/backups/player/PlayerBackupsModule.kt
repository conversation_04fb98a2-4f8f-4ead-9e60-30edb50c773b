@file:Suppress("unused")

package com.near_reality.tools.backups.player

import com.google.common.eventbus.Subscribe
import com.zenyte.game.GameConstants
import com.zenyte.game.task.WorldTask
import com.zenyte.game.task.WorldTasksManager
import com.zenyte.game.world.entity.player.login.LoginManager
import com.zenyte.plugins.events.ServerLaunchEvent
import com.zenyte.utils.TimeUnit
import kotlinx.coroutines.*
import org.slf4j.LoggerFactory
import java.nio.file.Paths
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import kotlin.time.ExperimentalTime

/**
 * Handles automatic backups of all players (online and offline).
 * Includes automatic cleanup of backups older than 7 days.
 *
 * <AUTHOR> (original)
 * <AUTHOR> (Discord: imslickk) (major overhaul)
 */
@DelicateCoroutinesApi
@ExperimentalTime
object PlayerBackupsModule {

    private val logger = LoggerFactory.getLogger(PlayerBackupsModule::class.java)
    private val scope = CoroutineScope(newFixedThreadPoolContext(2, "PlayerBackups"))
    private val backups = listOf(PlayerBackup.Full)
    private val backupsDir = Paths.get("data", "backups")

    /**
     * Used for formatting of the file name of the created zip archive.
     */
    private val dateFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy")

    /**
     * Registers backup tasks on server launch.
     */
    @Subscribe
    @JvmStatic
    fun onServerLaunch(event: ServerLaunchEvent) {
        if (GameConstants.WORLD_PROFILE.isDevelopment()) {
            logger.info("Ignoring backup system startup on development world.")
            return
        }
        logger.info("Starting player backup system...")
        for (backup in backups) {
            val backupTask = PlayerBackupTask(backup)
            val intervalTicks = TimeUnit.MINUTES.toTicks(backup.period.inWholeMinutes).toInt()
            val initialDelay = TimeUnit.MINUTES.toTicks(1).toInt()
            WorldTasksManager.schedule(backupTask, initialDelay, intervalTicks)
            logger.info("Scheduled {} backup task with interval of {} minutes",
                backup::class.simpleName, backup.period.inWholeMinutes)
        }
        val cleanupTask = BackupCleanupTask()
        val dailyTicks = TimeUnit.HOURS.toTicks(24).toInt() // Run every 24 hours
        val cleanupDelay = TimeUnit.MINUTES.toTicks(5).toInt() // Start 5 minutes after server launch
        WorldTasksManager.schedule(cleanupTask, cleanupDelay, dailyTicks)
        logger.info("Scheduled backup cleanup task to run every 24 hours")
    }

    /**
     * World task that performs player backups for all players (online and offline).
     */
    private class PlayerBackupTask(private val backup: PlayerBackup) : WorldTask {
        override fun run() {
            try {
                val estZone = ZoneId.of("America/New_York") // EST/EDT timezone
                val currentTime = LocalDateTime.now(estZone)
                if (backup.isTimeForNextBackup(currentTime)) {
                    logger.info("Starting {} backup for all players at {}", backup::class.simpleName, currentTime)
                    scope.async {
                        try {
                            val allPlayers = loadAllPlayers()
                            logger.info("Loaded {} players for backup", allPlayers.size)
                            if (allPlayers.isNotEmpty()) {
                                backup.makeBackupOf(scope, backupsDir, allPlayers, currentTime)
                            } else {
                                logger.info("No players found to backup")
                            }
                        } catch (e: Exception) {
                            logger.error("Failed to load players for backup", e)
                        }
                    }
                }
            } catch (e: Exception) {
                logger.error("Failed to execute backup task for {}", backup::class.simpleName, e)
            }
        }
    }

    /**
     * World task that cleans up old backup files (older than 7 days).
     */
    private class BackupCleanupTask : WorldTask {
        override fun run() {
            try {
                logger.info("Starting backup cleanup task...")
                val backupDir = backupsDir.toFile()
                if (!backupDir.exists()) {
                    logger.debug("Backup directory does not exist, skipping cleanup")
                    return
                }
                val cutoffTime = LocalDateTime.now(ZoneId.of("America/New_York")).minus(7, ChronoUnit.DAYS)
                val dateFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy_HH-mm-ss")
                var deletedCount = 0
                var totalSize = 0L
                backupDir.listFiles()?.forEach { file ->
                    if (file.isFile && file.name.endsWith(".zip")) {
                        try {
                            val fileName = file.nameWithoutExtension
                            val timestampPart = if (fileName.contains("_characters")) {
                                fileName.substringBefore("_characters")
                            } else {
                                fileName.substringBeforeLast("_")
                            }
                            val fileTime = LocalDateTime.parse(timestampPart, dateFormatter)
                            if (fileTime.isBefore(cutoffTime)) {
                                totalSize += file.length()
                                if (file.delete()) {
                                    deletedCount++
                                    logger.debug("Deleted old backup: {}", file.name)
                                } else {
                                    logger.warn("Failed to delete backup file: {}", file.name)
                                }
                            }
                        } catch (e: Exception) {
                            logger.warn("Failed to parse timestamp from backup file: {}", file.name, e)
                        }
                    }
                }

                if (deletedCount > 0) {
                    logger.info("Cleanup completed: deleted {} old backup files ({} MB freed)",
                        deletedCount, totalSize / (1024 * 1024))
                } else {
                    logger.debug("Cleanup completed: no old backup files found")
                }
            } catch (e: Exception) {
                logger.error("Failed to execute backup cleanup task", e)
            }
        }
    }

    /**
     * Loads all players from the character files directory.
     */
    private suspend fun loadAllPlayers(): List<com.zenyte.game.world.entity.player.Player> {
        val charactersDir = LoginManager.PLAYER_SAVE_DIRECTORY.toFile()
        if (!charactersDir.exists()) {
            logger.warn("Characters directory does not exist: {}", charactersDir.absolutePath)
            return emptyList()
        }

        return charactersDir.listFiles { file ->
            file.isFile && file.name.endsWith(".json")
        }?.map { file ->
            scope.async {
                try {
                    LoginManager.deserializePlayerFromFile(file.nameWithoutExtension)
                } catch (e: Exception) {
                    logger.warn("Failed to load player from file: {}", file.nameWithoutExtension, e)
                    null
                }
            }
        }?.awaitAll()?.filterNotNull() ?: emptyList()
    }
}
