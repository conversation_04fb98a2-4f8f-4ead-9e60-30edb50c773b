package com.near_reality.plugins.item

import com.zenyte.game.item.Item
import com.zenyte.game.model.item.pluginextensions.ItemPlugin
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.dialogue.Dialogue
import com.zenyte.game.world.entity.player.dialogue.dialogue

class BrokenZombieAxeItemAction: ItemPlugin() {

	override fun handle() {
		bind("Inspect") { p: Player, item: Item, _: Int ->
			p.dialogue {
				item(item, "I should repair this on an Anvil.")
			}
		}
	}

	override fun getItems(): IntArray {
		return intArrayOf(28813)
	}

}