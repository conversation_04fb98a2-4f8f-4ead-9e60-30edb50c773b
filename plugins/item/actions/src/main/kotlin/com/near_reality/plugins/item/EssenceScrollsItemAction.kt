package com.near_reality.plugins.item

import com.near_reality.scripts.item.actions.ItemActionScript
import com.zenyte.game.item.Item
import com.zenyte.game.item.ItemId
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.dialogue.Dialogue
import com.zenyte.game.model.shop.ShopCurrency
import com.near_reality.game.content.shop.ShopCurrencyHandler
import com.zenyte.game.world.entity.player.dialogue.dialogue

class EssenceScrollItemAction : ItemActionScript() {
    init {
        items(ItemId.REMNANT_POINT_VOUCHER_5)

        "Claim" {
            with(player.inventory) {
                ifDeleteItem(Item(item.id, 1)) {
                    addUtilityEssence(player, 100)
                    player.dialogueManager.start(object : Dialogue(player) {
                        override fun buildDialogue() {
                            item(item, "You claim 100 Utility Essence from the scroll.")
                        }
                    })
                }
            }
        }

        "Claim All" {
            val stackAmount = player.inventory.getAmountOf(item.id)
            if (stackAmount > 0) {
                val total = stackAmount * 100
                player.dialogue {
                    options("Do you want to claim $stackAmount scrolls for $total Utility Essence?", Dialogue.DialogueOption("Yes, Claim all.") {
                        player.inventory.ifDeleteItem(Item(item.id, stackAmount)) {
                            addUtilityEssence(player, total)
                        }
                        player.dialogue {
                            item(item, "You claim $total Utility Essence from all the scrolls.")
                        }
                    }, Dialogue.DialogueOption("No, I changed my mind."))
                }
            }
        }


        items(ItemId.REMNANT_POINT_VOUCHER_1)

        "Info" {
            player.dialogueManager.start(object : Dialogue(player) {
                override fun buildDialogue() {
                    item(item,
                        "You can visit the Combat Essence Lava Pool at home " +
                                "in front of the Wilderness ditch, and sacrifice this item " +
                                "for 1 Combat Essence per ticket."
                    )
                }
            })
        }
    }

    private fun addUtilityEssence(player: Player, amount: Int) {
        ShopCurrencyHandler.add(ShopCurrency.UTILITY_ESSENCE, player, amount)
        player.sendMessage("You receive $amount Utility Essence.")
    }
}