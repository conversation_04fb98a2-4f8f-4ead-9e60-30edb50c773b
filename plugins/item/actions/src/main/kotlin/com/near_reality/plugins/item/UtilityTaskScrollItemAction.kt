package com.near_reality.plugins.item

import com.zenyte.game.item.Item
import com.zenyte.game.model.item.pluginextensions.ItemPlugin
import com.zenyte.game.util.Colour
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.dialogue.Dialogue
import com.zenyte.game.world.entity.player.dialogue.dialogue

class UtilityTaskScrollItemAction: ItemPlugin() {

	override fun handle() {
		bind("Activate") { player: Player, item: Item, _: Int ->
			player.dialogue {
				item(item, "Reading this scroll can assign you a new Utility Task, if you have Utility Task slots available.")
				options("Assign a new Utility Task?",
					Dialogue.DialogueOption("Yes") {
							if(player.taskManager.hasAvailableSlots())  {
								player.taskManager.assignRandomChallenge()
								player.inventory.deleteItem(item.id, 1)
							}
					   	},
					Dialogue.DialogueOption("No"));
			}
		}
	}

	override fun getItems(): IntArray {
		return intArrayOf(608)
	}

}