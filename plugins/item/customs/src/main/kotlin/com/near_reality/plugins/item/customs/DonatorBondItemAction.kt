package com.near_reality.plugins.item.customs
/*
import com.near_reality.game.content.middleman.middleManController
import com.near_reality.scripts.item.actions.ItemActionScript
import com.zenyte.game.item.ItemId.DONATOR_BOND_10
import com.zenyte.game.item.ItemId.DONATOR_BOND_100
import com.zenyte.game.item.ItemId.DONATOR_BOND_25
import com.zenyte.game.item.ItemId.DONATOR_BOND_50

class DonatorBondItemAction : ItemActionScript() {
    init {
        items(DONATOR_BOND_10, DONATOR_BOND_25, DONATOR_BOND_50, DONATOR_BOND_100)

        "Redeem" {
            player.dialogueManager.start(DonatorBondRedeemDialogue(player, item))
        }
        "Trade" {
            player.middleManController.openTradeRequestInterface(item.id, 1, "")
        }

    }
}
*/