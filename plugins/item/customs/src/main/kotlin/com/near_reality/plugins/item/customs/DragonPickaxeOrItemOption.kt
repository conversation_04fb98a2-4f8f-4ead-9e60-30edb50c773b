package com.zenyte.plugins.item.customs

import com.zenyte.game.item.Item
import com.zenyte.game.item.ItemId
import com.zenyte.game.world.entity.player.Player
import com.zenyte.game.world.entity.player.container.impl.Inventory
import com.zenyte.game.world.entity.player.container.RequestResult
import com.zenyte.game.world.entity.player.dialogue.dialogue
import com.zenyte.game.world.entity.player.dialogue.options
import com.zenyte.game.model.item.pluginextensions.ItemPlugin
import com.zenyte.game.world.entity.masks.Animation
import com.zenyte.game.world.entity.masks.Graphics




class DragonPickaxeOrItemOption : ItemPlugin() {

	private val ANIM = Animation(898)
	private val GFX = Graphics(173)

	override fun handle() {
		bind("Revert") { player: Player, item: Item, slot: Int ->
			val inv: Inventory = player.inventory

			player.dialogue {
				item(Item(ItemId.ZALCANO_SHARD),
					"Would you like to remove the shard from the pickaxe?")

				options("Are you sure?") {
					"Yes, remove it." {
						if (inv.containsItem(item)) {
							player.animation = ANIM
							player.graphics = GFX

							if (inv.deleteItem(slot, item).result == RequestResult.SUCCESS) {
								inv.addItem(Item(ItemId.DRAGON_PICKAXE))
								inv.addItem(Item(ItemId.ZALCANO_SHARD))
								player.sendMessage("You carefully remove the shard from the pickaxe.")
							}
						} else {
							player.sendMessage("You do not have the pickaxe in your inventory.")
						}
					}
					"No" {}
				}
			}
		}
	}

	override fun getItems(): IntArray {
		return intArrayOf(
			ItemId.DRAGON_PICKAXE_OR
		)
	}
}
