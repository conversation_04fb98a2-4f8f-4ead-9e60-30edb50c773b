package com.near_reality.plugins.shops

import com.near_reality.scripts.shops.ShopScript
import com.zenyte.game.item.ItemId.*
import com.zenyte.game.model.shop.ShopCurrency
import com.zenyte.game.model.shop.ShopPolicy.STOCK_ONLY

class AlecksHunterEmporiumShop : ShopScript() {
    init {
        "Aleck's Hunter Emporium"(98, ShopCurrency.COINS, STOCK_ONLY) {
            BRONZE_AXE(10, 8, 24)
            KNIFE(10, 2, 7)
            ROPE(100, 7, 23)
            BUTTERFLY_NET(5, 14, 24)
            NOOSE_WAND(50, 2, 4)
            BIRD_SNARE(50, 3, 6)
            BOX_TRAP(25, 22, 38)
            TEASING_STICK(5, 35, 60)
            BIRD_SNARE_PACK(5, 353, 606)
            BOX_TRAP_PACK(5, 2240, 3840)
        }
    }
}