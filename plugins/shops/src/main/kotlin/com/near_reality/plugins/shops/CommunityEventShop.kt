package com.near_reality.plugins.shops

import com.near_reality.scripts.shops.ShopScript
import com.zenyte.game.item.ItemId.*
import com.zenyte.game.model.shop.ShopCurrency.COMMUNITY_EVENT_POINTS
import com.zenyte.game.model.shop.ShopPolicy.NO_SELLING

class CommunityEventShop : ShopScript() {
    init {
        "Community Event Shop"(370, COMMUNITY_EVENT_POINTS, NO_SELLING) {
            PVM_MYSTERY_BOX(1_000_000, sellPrice = (-1), buyPrice = 2_000)
            SIR_ELDRICS_BOOST_SCROLL(1_000_000, sellPrice = (-1), buyPrice = 2_000)
            WORLD_BOOST_TOKEN(1_000_000, sellPrice = (-1), buyPrice = 8_000)
            CANNONBALL_PACK(1_000_000, sellPrice = (-1), buyPrice = 125)
            GRANITE_CANNONBALL_PACK(1_000_000, sellPrice = (-1), buyPrice = 200)
            CRYSTAL_KEY(1_000_000, sellPrice = (-1), buyPrice = 200)
            ENHANCED_CRYSTAL_KEY(1_000_000, sellPrice = (-1), buyPrice = 350)
            HERB_BOX(1_000_000, sellPrice = (-1), buyPrice = 55)
            XERICS_WISDOM(1_000_000, sellPrice = (-1), buyPrice = 1_800)
            TOB_BOOSTER(1_000_000, sellPrice = (-1), buyPrice = 1_500)
            PET_BOOSTER(1_000_000, sellPrice = (-1), buyPrice = 1_000)
            SLAYER_BOOSTER(1_000_000, sellPrice = (-1), buyPrice = 1_000)
            GAUNTLET_BOOSTER(1_000_000, sellPrice = (-1), buyPrice = 1_500)
            LARRANS_KEY_BOOSTER(1_000_000, sellPrice = (-1), buyPrice = 850)
            SHERLOCKS_NOTES(1_000_000, sellPrice = (-1), buyPrice = 250)
            NEX_BOOSTER(1_000_000, sellPrice = (-1), buyPrice = 1_800)
        }
    }
}