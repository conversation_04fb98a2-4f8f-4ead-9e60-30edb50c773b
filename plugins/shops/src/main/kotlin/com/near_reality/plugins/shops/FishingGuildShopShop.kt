package com.near_reality.plugins.shops

import com.near_reality.scripts.shops.ShopScript
import com.zenyte.game.model.shop.*
import com.zenyte.game.model.shop.ShopPolicy
import com.zenyte.game.model.shop.ShopPolicy.*
import com.zenyte.game.model.shop.ShopCurrency
import com.zenyte.game.model.shop.ShopCurrency.*
import com.zenyte.game.item.ItemId
import com.zenyte.game.item.ItemId.*
import com.near_reality.game.content.universalshop.*
import com.near_reality.game.content.universalshop.UnivShopItem
import com.near_reality.game.content.universalshop.UnivShopItem.*

class FishingGuildShopShop : ShopScript() {
    init {
        "Fishing Guild Shop"(58, ShopCurrency.COINS, STOCK_ONLY) {
            FISHING_BAIT(2000, 2, 3)
            BAIT_PACK(150, 210, 300)
            FEATHER(1500, 2, 2)
            FEATHER_PACK(150, 210, 200)
            SANDWORMS(1500, 76, 90)
            SANDWORMS_PACK(150, 7650, 9000)
            BUCKET(1000, 1, 2)
            SMALL_FISHING_NET(10, 3, 5)
            BIG_FISHING_NET(10, 14, 20)
            FISHING_ROD(10, 4, 5)
            HARPOON(10, 3, 5)
            LOBSTER_POT(10, 14, 20)
            KARAMBWAN_VESSEL(10, 14, 20)
            RAW_COD(0, 40, 40)
            RAW_MACKEREL(0, 60, 60)
            RAW_BASS(0, 160, 160)
            RAW_TUNA(0, 160, 160)
            RAW_LOBSTER(0, 280, 280)
            RAW_SWORDFISH(0, 320, 320)
            COD(0, 40, 40)
            MACKEREL(0, 60, 60)
            BASS(0, 160, 160)
            TUNA(0, 160, 160)
            LOBSTER(0, 280, 280)
            SWORDFISH(0, 320, 320)
        }
    }
}