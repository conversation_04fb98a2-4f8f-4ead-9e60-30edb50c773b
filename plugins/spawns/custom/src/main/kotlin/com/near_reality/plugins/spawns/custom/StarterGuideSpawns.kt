package com.near_reality.plugins.spawns.custom

import com.zenyte.game.world.entity.Location
import com.zenyte.game.world.region.area.RegisterArea

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class StarterGuideSpawns : NPCSpawnsScript() {
    init {
        val loc: Location = RegisterArea.EXILES_GUIDE_LOCATION
        
        EXILES_GUIDE(loc.x, loc.y, loc.plane, EAST)
    }
}