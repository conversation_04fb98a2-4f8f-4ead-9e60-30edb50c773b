package com.near_reality.plugins.spawns.nex

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class NexSpawns : NPCSpawnsScript() {
    init {
        SPIRITUAL_RANGER_11291(2881, 5208, 0, SOUTH, 4)
        SPIRITUAL_RANGER_11291(2883, 5223, 0, SOUTH, 4)
        SPIRITUAL_RANGER_11291(2889, 5220, 0, SOUTH, 4)
        SPIRITUAL_RANGER_11291(2891, 5203, 0, SOUTH, 4)
        SPIRITUAL_RANGER_11291(2856, 5204, 0, SOUTH, 4)
        SPIRITUAL_RANGER_11291(2862, 5207, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SPIRITUAL_RANGER_11291(2863, 5199, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SPIRITUAL_RANGER_11291(2865, 5215, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SPIRITUAL_RANGER_11291(2873, 5218, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        
        SP<PERSON><PERSON>U<PERSON>_WARRIOR_11290(2881, 5218, 0, SOUTH, 4)
        SPIRITUAL_WARRIOR_11290(2883, 5204, 0, SOUTH, 4)
        SPIRITUAL_WARRIOR_11290(2890, 5208, 0, SOUTH, 4)
        SPIRITUAL_WARRIOR_11290(2894, 5200, 0, SOUTH, 4)
        SPIRITUAL_WARRIOR_11290(2852, 5208, 0, SOUTH, 4)
        SPIRITUAL_WARRIOR_11290(2860, 5203, 0, SOUTH, 4)
        SPIRITUAL_WARRIOR_11290(2867, 5219, 0, SOUTH, 4)
        SPIRITUAL_WARRIOR_11290(2869, 5212, 0, SOUTH, 4)
        SPIRITUAL_WARRIOR_11290(2873, 5204, 0, SOUTH, 4)
        
        SPIRITUAL_MAGE_11292(2880, 5213, 0, SOUTH, 4)
        SPIRITUAL_MAGE_11292(2887, 5202, 0, SOUTH, 4)
        SPIRITUAL_MAGE_11292(2864, 5210, 0, SOUTH, 4)
        SPIRITUAL_MAGE_11292(2869, 5202, 0, SOUTH, 4)
        SPIRITUAL_MAGE_11292(2876, 5223, 0, SOUTH, 4)
        
        BLOOD_REAVER(2885, 5197, 0, SOUTH, 4)
        BLOOD_REAVER(2885, 5208, 0, SOUTH, 4)
        BLOOD_REAVER(2887, 5215, 0, SOUTH, 4)
        BLOOD_REAVER(2895, 5207, 0, SOUTH, 4)
        BLOOD_REAVER(2854, 5206, 0, SOUTH, 4)
        BLOOD_REAVER(2858, 5208, 0, SOUTH, 4)
        BLOOD_REAVER(2859, 5200, 0, SOUTH, 4)
        BLOOD_REAVER(2864, 5203, 0, SOUTH, 4)
        BLOOD_REAVER(2870, 5222, 0, SOUTH, 4)
        BLOOD_REAVER(2873, 5208, 0, SOUTH, 4)
        BLOOD_REAVER(2875, 5214, 0, SOUTH, 4)
        BLOOD_REAVER(2878, 5201, 0, SOUTH, 4)
        
        ASHUELOT_REIS_11289(2904, 5203, 0, SOUTH, 4)
        KNIGHT_16023(2905, 5200, 0, NORTH, 0)
    }
}