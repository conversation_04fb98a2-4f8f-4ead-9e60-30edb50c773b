package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11161Spawns : NPCSpawnsScript() {
    init {
        BLACK_DRAGON_258(2774, 9812, 1, <PERSON>OUT<PERSON>, 5)
        BLACK_DRAGON_259(2775, 9803, 1, SOUTH, 5)
        BLACK_DRAGON_257(2775, 9834, 1, SOUTH, 5)
        BLACK_DRAGON_259(2777, 9826, 1, <PERSON>OUTH, 5)
        BLACK_DRAGON_258(2783, 9838, 1, <PERSON>OUTH, 5)
        BLACK_DRAGON_257(2785, 9808, 1, S<PERSON><PERSON><PERSON>, 5)
        BLACK_DRAGON_258(2785, 9823, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        BLACK_DRAGON_259(2796, 9841, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        BLACK_DRAGON_257(2798, 9809, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        BLACK_DRAGON_257(2798, 9828, 1, <PERSON>O<PERSON>H, 5)
        <PERSON>LACK_DRAGON_258(2799, 9818, 1, SOUTH, 5)
        BLACK_DRAGON_259(2807, 9814, 1, SOUTH, 5)
    }
}