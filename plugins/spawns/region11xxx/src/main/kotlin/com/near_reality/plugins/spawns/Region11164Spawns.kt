package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11164Spawns : NPCSpawnsScript() {
    init {
        PYREFIEND_435(2757, 10008, 0, SOUTH, 5)
        PYREFIEND_434(2758, 10001, 0, SOUTH, 4)
        PYREFIEND(2758, 10004, 0, SOUTH, 4)
        PYREFIEND_435(2758, 10012, 0, SOUTH, 5)
        PYREFIEND_436(2760, 10007, 0, SOUTH, 2)
        PYREFIEND(2760, 10009, 0, SOUTH, 4)
        PYREFIEND(2761, 9998, 0, SOUTH, 4)
        PYREFIEND_436(2761, 10003, 0, SOUTH, 2)
        PYREFIEND_434(2763, 9999, 0, SOUT<PERSON>, 4)
        PYREFIEND_435(2763, 10001, 0, <PERSON>OUTH, 5)
        PYREFIEND_434(2763, 10007, 0, SOUTH, 4)
        PYREFIEND(2765, 10002, 0, SOUTH, 4)
        PYREFIEND_436(2765, 10005, 0, SOUTH, 2)
        CAVE_CRAWLER_408(2781, 10000, 0, SOUTH, 4)
        CAVE_CRAWLER_409(2782, 9995, 0, SOUTH, 5)
        CAVE_CRAWLER(2784, 9998, 0, SOUTH, 6)
        CAVE_CRAWLER_408(2785, 9992, 0, SOUTH, 4)
        COCKATRICE_419(2785, 10035, 0, SOUTH, 4)
        COCKATRICE_419(2786, 10038, 0, SOUTH, 4)
        CAVE_CRAWLER_407(2787, 9996, 0, SOUTH, 4)
        COCKATRICE_419(2789, 10032, 0, SOUTH, 4)
        CAVE_CRAWLER_408(2790, 9994, 0, SOUTH, 4)
        COCKATRICE_419(2791, 10036, 0, SOUTH, 4)
        CAVE_CRAWLER_409(2793, 9998, 0, SOUTH, 5)
        ROCKSLUG(2793, 10016, 0, SOUTH, 6)
        CAVE_CRAWLER(2795, 9994, 0, SOUTH, 6)
        ROCKSLUG(2795, 10017, 0, SOUTH, 6)
        COCKATRICE_419(2795, 10033, 0, SOUTH, 4)
        COCKATRICE_419(2797, 10036, 0, SOUTH, 4)
        ROCKSLUG(2798, 10018, 0, SOUTH, 6)
        COCKATRICE_419(2799, 10031, 0, SOUTH, 4)
        ROCKSLUG(2800, 10020, 0, SOUTH, 6)
        ROCKSLUG(2801, 10015, 0, SOUTH, 6)
        ROCKSLUG(2803, 10017, 0, SOUTH, 6)
        ROCKSLUG(2804, 10019, 0, SOUTH, 6)
    }
}