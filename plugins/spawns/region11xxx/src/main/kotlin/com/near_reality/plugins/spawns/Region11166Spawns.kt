package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11166Spawns : NPCSpawnsScript() {
    init {
        MOUNTAIN_TROLL(2769, 10149, 0, SOUT<PERSON>, 8)
        MOUNTAIN_TROLL_940(2771, 10143, 0, SOUTH, 5)
        MOUNTAIN_TROLL(2777, 10142, 0, SOUTH, 8)
        MOUNTAIN_TROLL_937(2781, 10143, 0, SOUTH, 6)
        MOUNTAIN_TROLL_938(2783, 10140, 0, SOUT<PERSON>, 2)
        MOUNTAIN_TROLL_939(2784, 10134, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        MOUNTAIN_TROLL_941(2788, 10131, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
    }
}