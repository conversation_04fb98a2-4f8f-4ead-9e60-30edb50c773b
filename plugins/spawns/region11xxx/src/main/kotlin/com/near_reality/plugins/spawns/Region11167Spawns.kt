package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11167Spawns : NPCSpawnsScript() {
    init {
        ICE_TROLL_652(2770, 10219, 0, SOUTH, 5)
        ICE_TROLL_650(2772, 10221, 0, SOUTH, 5)
        ICE_TROLL_651(2773, 10220, 0, SOUTH, 5)
        ICE_TROLL(2775, 10224, 0, SOUTH, 5)
        ICE_TROLL_649(2777, 10224, 0, SOUTH, 5)
        ICE_TROLL(2783, 10199, 0, SOUTH, 5)
        ICE_TROLL_649(2784, 10231, 0, SOUTH, 5)
        ICE_TROLL(2784, 10233, 0, <PERSON>OUTH, 5)
        ICE_TROLL_650(2785, 10201, 0, SOUTH, 5)
        ICE_TROLL_654(2786, 10199, 0, <PERSON>OUTH, 5)
        ICE_TROLL_650(2787, 10232, 0, SOUTH, 5)
        ICE_TROLL_651(2788, 10198, 0, SOUTH, 5)
        ICE_TROLL_653(2788, 10221, 0, SOUTH, 5)
        ICE_TROLL_652(2788, 10230, 0, SOUTH, 5)
        ICE_TROLL_651(2790, 10220, 0, SOUTH, 5)
        ICE_TROLL_654(2790, 10222, 0, SOUTH, 5)
        ICE_TROLL_649(2792, 10222, 0, SOUTH, 5)
        ICE_TROLL_652(2798, 10216, 0, SOUTH, 5)
        ICE_TROLL_654(2799, 10214, 0, SOUTH, 5)
        ICE_TROLL_651(2800, 10218, 0, SOUTH, 5)
        ICE_TROLL_653(2801, 10216, 0, SOUTH, 5)
    }
}