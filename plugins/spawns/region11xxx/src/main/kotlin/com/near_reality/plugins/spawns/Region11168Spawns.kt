package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11168Spawns : NPCSpawnsScript() {
    init {
        ARMOURED_ZOMBIE_12722(2787, 10283, 0, SOUTH, 5)
        ARMOURED_ZOMBIE_12722(2785, 10283, 0, SOUTH, 5)
        ARMOURED_ZOMBIE_12722(2783, 10283, 0, SOUTH, 5)
        ARMOURED_ZOMBIE_12722(2783, 10285, 0, SOUTH, 5)
        ARMOURED_ZOMBIE_12722(2783, 10281, 0, SOUTH, 5)
        ARMOURED_ZOMBIE_12722(2781, 10283, 0, SOUT<PERSON>, 5)
        ARMOURED_ZOMBIE_12722(2781, 10285, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ARMOURED_ZOMBIE_12722(2781, 10281, 0, <PERSON>OUTH, 5)
        ARMOURED_ZOMBIE_12722(2779, 10283, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ARMOURED_ZOMBIE_12722(2779, 10285, 0, SOUTH, 5)
        ARMOURED_ZOMBIE_12722(2779, 10281, 0, SOUTH, 5)
        ARMOURED_ZOMBIE_12722(2777, 10283, 0, SOUTH, 5)
        ARMOURED_ZOMBIE_12722(2771, 10279, 0, SOUTH, 5)
        ARMOURED_ZOMBIE_12722(2778, 10275, 0, SOUTH, 5)
    }
}