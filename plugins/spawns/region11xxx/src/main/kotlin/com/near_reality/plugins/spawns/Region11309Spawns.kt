package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11309Spawns : NPCSpawnsScript() {
    init {
        JUNGLE_FORESTER_3955(2817, 2940, 0, SOUTH, 2)
        OOMLIE_BIRD(2819, 2900, 0, SOUTH, 5)
        OOMLIE_BIRD(2821, 2910, 0, SOUTH, 5)
        JUNGLE_SAVAGE(2821, 2927, 0, SOUTH, 22)
        JUNGLE_WOLF(2824, 2896, 0, <PERSON><PERSON>UTH, 6)
        JUNGLE_SAVAGE(2824, 2907, 0, <PERSON>OUTH, 22)
        JUNGLE_FORESTER(2824, 2940, 0, <PERSON>OUT<PERSON>, 3)
        JUNGLE_WOLF(2827, 2924, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        OOMLIE_BIRD(2827, 2927, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        OOMLIE_BIRD(2834, 2911, 0, <PERSON>OUTH, 5)
        OOMLIE_BIRD(2837, 2897, 0, SOUTH, 5)
        JUNGLE_SAVAGE(2837, 2918, 0, SOUTH, 22)
        JUNGLE_SAVAGE(2840, 2899, 0, SOUTH, 22)
        JUNGLE_WOLF(2842, 2899, 0, SOUTH, 6)
        JUNGLE_SAVAGE(2845, 2928, 0, SOUTH, 22)
        OOMLIE_BIRD(2859, 2904, 0, SOUTH, 5)
        JUNGLE_SAVAGE(2859, 2923, 0, SOUTH, 22)
        JUNGLE_WOLF(2861, 2907, 0, SOUTH, 6)
        JUNGLE_WOLF(2862, 2899, 0, SOUTH, 6)
        JUNGLE_SAVAGE(2862, 2904, 0, SOUTH, 22)
        OOMLIE_BIRD(2863, 2916, 0, SOUTH, 5)
        JUNGLE_FORESTER(2863, 2941, 0, SOUTH, 3)
        JUNGLE_FORESTER_3955(2872, 2941, 0, SOUTH, 2)
    }
}