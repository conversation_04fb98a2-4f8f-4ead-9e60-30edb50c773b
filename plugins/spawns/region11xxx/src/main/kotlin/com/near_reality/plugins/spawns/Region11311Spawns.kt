package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11311Spawns : NPCSpawnsScript() {
    init {
        SNAKE_2845(2825, 3030, 0, SOUTH, 4)
        SNAKE_2845(2827, 3013, 0, <PERSON>OUT<PERSON>, 4)
        MONKEY_2848(2829, 3029, 0, SOUTH, 10)
        SNAKE_2845(2832, 3017, 0, SOUTH, 4)
        SNAKE_2845(2832, 3019, 0, <PERSON>OUTH, 4)
        MONKEY_2848(2832, 3029, 0, SOUTH, 10)
        MONKEY_2848(2833, 3027, 0, SOUTH, 10)
        MONKEY_2848(2834, 3035, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        6130(2835, 3053, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        SNAKE_2845(2836, 3043, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MONKEY_2848(2837, 3029, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        MONKEY_2848(2837, 3034, 0, <PERSON><PERSON><PERSON>H, 10)
        <PERSON><PERSON><PERSON>_2845(2838, 3039, 0, SOUTH, 4)
        S<PERSON>KE_2845(2838, 3047, 0, SOUTH, 4)
        TRI<PERSON><PERSON><PERSON>(2842, 3017, 0, SO<PERSON>H, 5)
        6239(2844, 3042, 0, SOUTH, 5)
        J<PERSON>G<PERSON>_<PERSON><PERSON>ER(2844, 3061, 0, SOUTH, 4)
        S<PERSON>KE_2845(2847, 3042, 0, SOUTH, 4)
        JUNGLE_SPIDER(2850, 3063, 0, SOUTH, 4)
        JUNGLE_SPIDER(2853, 3054, 0, SOUTH, 4)
        TRIBESMAN(2866, 3061, 0, SOUTH, 5)
        MONKEY_2848(2868, 3017, 0, SOUTH, 10)
        MONKEY_2848(2868, 3021, 0, SOUTH, 10)
        TRIBESMAN(2868, 3054, 0, SOUTH, 5)
        MONKEY_2848(2869, 3015, 0, SOUTH, 10)
        MONKEY_2848(2871, 3018, 0, SOUTH, 10)
        MONKEY_2848(2871, 3023, 0, SOUTH, 10)
        MONKEY_2848(2873, 3021, 0, SOUTH, 10)
    }
}