package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11312Spawns : NPCSpawnsScript() {
    init {
        6577(2816, 3083, 0, SOUTH, 5)
        GULL(2818, 3133, 0, SOUTH, 7)
        SNAKE_2845(2820, 3082, 0, SOUTH, 4)
        SNAKE_2845(2824, 3077, 0, SOUTH, 4)
        GULL(2824, 3133, 0, <PERSON>OUTH, 7)
        SNAKE_2845(2826, 3081, 0, SOUTH, 4)
        GULL(2829, 3131, 0, SOUTH, 7)
        GULL(2833, 3134, 0, SOUTH, 7)
        SNAKE_2845(2835, 3074, 0, <PERSON>O<PERSON><PERSON>, 4)
        HARPIE_BUG_SWARM(2854, 3106, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        JUNGLE_SPIDER(2855, 3085, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        JUNGLE_SPIDER(2855, 3089, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        JUNGLE_SPIDER(2857, 3085, 0, SOUTH, 4)
        JUNGLE_SPIDER(2858, 3083, 0, SOUTH, 4)
        JUNGLE_SPIDER(2858, 3092, 0, SOUTH, 4)
        HARPIE_BUG_SWARM(2859, 3106, 0, SOUTH, 6)
        JUNGLE_SPIDER(2861, 3082, 0, SOUTH, 4)
        JUNGLE_SPIDER(2861, 3088, 0, SOUTH, 4)
        JUNGLE_SPIDER(2862, 3081, 0, SOUTH, 4)
        HARPIE_BUG_SWARM(2863, 3109, 0, SOUTH, 6)
        HARPIE_BUG_SWARM(2863, 3115, 0, SOUTH, 6)
        JUNGLE_SPIDER(2864, 3084, 0, SOUTH, 4)
        JUNGLE_SPIDER(2864, 3088, 0, SOUTH, 4)
        JUNGLE_SPIDER(2866, 3081, 0, SOUTH, 4)
        JUNGLE_SPIDER(2867, 3086, 0, SOUTH, 4)
        HARPIE_BUG_SWARM(2867, 3105, 0, SOUTH, 6)
        JUNGLE_SPIDER(2868, 3084, 0, SOUTH, 4)
        HARPIE_BUG_SWARM(2870, 3112, 0, SOUTH, 6)
        HARPIE_BUG_SWARM(2872, 3117, 0, SOUTH, 6)
        HARPIE_BUG_SWARM(2873, 3108, 0, SOUTH, 6)
        HARPIE_BUG_SWARM(2875, 3123, 0, SOUTH, 6)
        HARPIE_BUG_SWARM(2878, 3114, 0, SOUTH, 6)
    }
}