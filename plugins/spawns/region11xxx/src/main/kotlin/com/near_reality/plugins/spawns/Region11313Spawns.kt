package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11313Spawns : NPCSpawnsScript() {
    init {
        SNAKE_2845(2819, 3194, 0, SOUTH, 4)
        MONKEY_2848(2820, 3193, 0, SOUTH, 10)
        IMP_5007(2832, 3170, 0, SOUTH, 100)
        IMP_5007(2832, 3177, 0, SOUTH, 100)
        IMP_5007(2837, 3184, 0, SOUTH, 100)
        IMP_5007(2841, 3163, 0, SOUTH, 100)
        SCORPION_3024(2843, 3156, 0, SOUTH, 12)
        IMP_5007(2849, 3186, 0, SOUTH, 100)
        IMP_5007(2850, 3165, 0, SOUTH, 100)
        SCORPION_3024(2853, 3159, 0, <PERSON>OUTH, 12)
        IMP_5007(2857, 3179, 0, SOUTH, 100)
        IMP_5007(2859, 3177, 0, SO<PERSON>H, 100)
        SCOR<PERSON>ON_3024(2861, 3165, 0, SOUTH, 12)
        SCORPION_3024(2862, 3180, 0, SOUTH, 12)
        SCORPION_3024(2863, 3169, 0, SOUTH, 12)
        SCORPION_3024(2865, 3156, 0, SOUTH, 12)
        SCORPION_3024(2870, 3159, 0, SOUTH, 12)
        MONKEY_2848(2873, 3148, 0, SOUTH, 10)
        SNAKE_2845(2875, 3164, 0, SOUTH, 4)
        MONKEY_2848(2877, 3154, 0, SOUTH, 10)
        SNAKE_2845(2879, 3151, 0, SOUTH, 4)
        MONKEY_2848(2879, 3161, 0, SOUTH, 10)
    }
}