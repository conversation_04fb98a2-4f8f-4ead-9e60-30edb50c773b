package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11314Spawns : NPCSpawnsScript() {
    init {
        SKELETON_77(2820, 3259, 0, <PERSON>O<PERSON><PERSON>, 7)
        SKELETON_77(2824, 3237, 0, <PERSON>OUTH, 7)
        MOSS_GIANT_2092(2827, 3246, 0, SOUTH, 3)
        MOSS_GIANT(2831, 3243, 0, SOUTH, 4)
        SKELETON_78(2832, 3234, 0, <PERSON>OUTH, 8)
        MOSS_GIANT_2091(2834, 3242, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SKELETON_79(2840, 3238, 0, SOUT<PERSON>, 8)
        SKELETON_80(2846, 3249, 0, <PERSON>O<PERSON><PERSON>, 8)
        SKELETON_81(2850, 3247, 0, <PERSON>O<PERSON><PERSON>, 6)
        SKELETON_77(2850, 3256, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETON_79(2852, 3260, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKELETON_78(2854, 3249, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        5865(2849, 3235, 1, SOUTH, 5)
    }
}