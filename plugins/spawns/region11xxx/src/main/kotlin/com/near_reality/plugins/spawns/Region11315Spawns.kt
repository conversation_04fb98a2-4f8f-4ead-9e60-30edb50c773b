package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11315Spawns : NPCSpawnsScript() {
    init {
        HOBGOBLIN_3049(2818, 3291, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGOBLIN_3049(2822, 3282, 0, SOUTH, 13)
        HOBGOBLIN_3049(2822, 3288, 0, SOUTH, 13)
        HOBGOBLIN_3049(2824, 3298, 0, <PERSON>OUTH, 13)
        LESSER_DEMON_2018(2832, 3278, 0, SOUTH, 8)
        HOBGOBLIN_3049(2834, 3305, 0, SOUTH, 13)
        LESSER_DEMON_2007(2837, 3280, 0, <PERSON>OUT<PERSON>, 4)
        KING_SCORPION(2842, 3298, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        KING_SCORPION(2844, 3291, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        KING_SCORPION(2851, 3298, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        KING_SCORPION(2855, 3303, 0, SOUTH, 8)
        KING_SCORPION(2859, 3283, 0, SOUTH, 8)
        KING_SCORPION(2861, 3295, 0, SOUTH, 8)
    }
}