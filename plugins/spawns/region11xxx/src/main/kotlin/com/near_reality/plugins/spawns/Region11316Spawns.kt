package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11316Spawns : NPCSpawnsScript() {
    init {
        MAZION(2818, 3342, 0, SOUTH, 4)
        CAVE_MONK(2822, 3374, 0, WEST, 0)
        FRITZ_THE_GLASSBLOWER(2831, 3350, 0, SOUTH, 2)
        UNICORN(2838, 3370, 0, SOUTH, 15)
        ROD_FISHING_SPOT_1516(2840, 3356, 0, SOUTH, 0)
        ROD_FISHING_SPOT_1516(2842, 3359, 0, SOUTH, 0)
        MONK_4068(2843, 3346, 0, <PERSON>O<PERSON><PERSON>, 3)
        ROD_FISHING_SPOT_1516(2845, 3356, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        CHICKEN(2846, 3374, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        ENTRANA_FIREBIRD(2847, 3386, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ROD_FISHING_SPOT_1516(2849, 3361, 0, SOUTH, 0)
        6114(2850, 3348, 0, SOUTH, 2)
        MONK_4068(2850, 3354, 0, SOUTH, 3)
        CHICKEN(2850, 3368, 0, SOUTH, 2)
        CHICKEN_1174(2850, 3371, 0, SOUTH, 4)
        UNICORN(2850, 3378, 0, SOUTH, 15)
        MONK_4068(2851, 3345, 0, SOUTH, 3)
        HIGH_PRIEST(2851, 3349, 0, SOUTH, 5)
        CHICKEN(2852, 3370, 0, SOUTH, 2)
        CHICKEN_1174(2853, 3368, 0, SOUTH, 4)
        CHICKEN_1174(2853, 3373, 0, SOUTH, 4)
        UNICORN(2860, 3375, 0, SOUTH, 15)
        UNICORN(2863, 3378, 0, SOUTH, 15)
        FISHING_SPOT_1517(2875, 3342, 0, SOUTH, 0)
        FISHING_SPOT_1517(2876, 3331, 0, SOUTH, 0)
        FISHING_SPOT_1517(2877, 3331, 0, SOUTH, 0)
        FISHING_SPOT_1517(2877, 3342, 0, SOUTH, 0)
        MONK_OF_ENTRANA_1168(2830, 3335, 0, SOUTH, 3)
        MONK_OF_ENTRANA_1169(2835, 3336, 0, SOUTH, 4)
        MONK_OF_ENTRANA_1170(2839, 3335, 0, SOUTH, 2)
        MONK_4068(2851, 3342, 1, SOUTH, 3)
        MONK_4068(2854, 3350, 1, SOUTH, 3)
    }
}