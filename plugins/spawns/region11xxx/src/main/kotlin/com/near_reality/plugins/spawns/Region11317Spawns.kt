package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11317Spawns : NPCSpawnsScript() {
    init {
        MAN_3108(2816, 3442, 0, SOUT<PERSON>, 11)
        WOMAN_3112(2817, 3443, 0, SOUTH, 2)
        4986(2819, 3451, 0, SOUTH, 4)
        HICKTON(2822, 3442, 0, SOUTH, 1)
        HARRY(2834, 3445, 0, SOUTH, 2)
        FISHING_SPOT_1518(2836, 3431, 0, SOUTH, 0)
        FISHING_SPOT_1518(2838, 3431, 0, SOUTH, 0)
        WHITE_WOLF(2842, 3450, 0, <PERSON>O<PERSON><PERSON>, 3)
        FISHING_SPOT_1519(2844, 3429, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        FISHING_SPOT_1520(2846, 3429, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        FISHING_SPOT_1519(2853, 3423, 0, <PERSON>OUTH, 0)
        FISHING_SPOT_1520(2855, 3423, 0, SOUTH, 0)
        FISHING_SPOT_1520(2859, 3426, 0, SOUTH, 0)
        ELLENA(2861, 3432, 0, SOUTH, 4)
        TOOL_LEPRECHAUN(2863, 3432, 0, SOUTH, 0)
        WHITE_WOLF(2864, 3453, 0, SOUTH, 3)
        WHITE_WOLF(2870, 3438, 0, SOUTH, 3)
        WHITE_WOLF(2872, 3445, 0, SOUTH, 3)
    }
}