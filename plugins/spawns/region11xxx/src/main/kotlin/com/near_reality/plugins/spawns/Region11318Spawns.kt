package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11318Spawns : NPCSpawnsScript() {
    init {
        ICE_GIANT_2086(2817, 3514, 0, SOUTH, 3)
        VANESSA(2820, 3463, 0, SOUTH, 2)
        VESTRI(2820, 3487, 0, SOUTH, 5)
        ICE_GIANT_2087(2824, 3510, 0, SOUTH, 3)
        WHITE_WOLF_108(2831, 3515, 0, SOUTH, 3)
        WHITE_WOLF_108(2833, 3513, 0, SOUTH, 3)
        WOLF_110(2836, 3495, 0, SO<PERSON><PERSON>, 7)
        WOLF_110(2837, 3499, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        WOLF_110(2839, 3502, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        WOLF_110(2839, 3506, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        BIG_WOLF(2840, 3497, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        WOLF_110(2842, 3504, 0, SOUTH, 7)
        WOLF_110(2844, 3507, 0, SOUTH, 7)
        WHITE_WOLF(2846, 3477, 0, SOUTH, 3)
        WHITE_WOLF(2847, 3474, 0, SOUTH, 3)
        6090(2847, 3499, 0, SOUTH, 0)
        ICE_WARRIOR(2847, 3514, 0, SOUTH, 6)
        WHITE_WOLF_108(2848, 3487, 0, SOUTH, 3)
        WHITE_WOLF_108(2850, 3484, 0, SOUTH, 3)
        ICE_WARRIOR(2850, 3512, 0, SOUTH, 6)
        WHITE_WOLF_108(2854, 3509, 0, SOUTH, 3)
        WHITE_WOLF_108(2856, 3508, 0, SOUTH, 3)
        WHITE_WOLF(2860, 3492, 0, SOUTH, 3)
        6274(2861, 3510, 0, SOUTH_EAST, 0)
        WHITE_WOLF(2866, 3498, 0, SOUTH, 3)
        AUSTRI(2877, 3483, 0, SOUTH, 5)
    }
}