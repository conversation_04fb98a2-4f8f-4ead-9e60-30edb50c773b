package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11319Spawns : NPCSpawnsScript() {
    init {
        BILLY(2816, 3559, 0, SOUTH, 3)
        ROOSTER(2816, 3560, 0, SOUTH, 3)
        CHICKEN(2816, 3561, 0, SOUTH, 2)
        CHICKEN_1174(2818, 3559, 0, SOUTH, 4)
        CHICKEN(2819, 3561, 0, SOUTH, 2)
        CHICKEN_1174(2819, 3562, 0, SOUTH, 4)
        TENZING(2820, 3556, 0, SOUTH, 2)
        JADE(2841, 3543, 0, EAST, 0)
        LIDIO(2842, 3551, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        LILLY(2845, 3553, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        AJJAT(2851, 3553, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        SHANOMI(2854, 3541, 0, SOUTH, 2)
        HARRALLAK_MENAROUS(2866, 3546, 0, SOUTH, 4)
        GHOMMAL(2877, 3546, 0, SOUTH, 2)
        GAMFRED(2840, 3541, 1, SOUTH, 2)
        ANTON(2855, 3536, 1, SOUTH, 2)
        SLOANE(2855, 3552, 1, SOUTH, 2)
        REF_6074(2867, 3544, 1, SOUTH, 2)
        REF(2867, 3556, 1, SOUTH, 2)
        JIMMY(2872, 3539, 1, SOUTH, 2)
        CYCLOPS_2468(2842, 3550, 2, SOUTH, 3)
        KAMFREENA(2846, 3539, 2, SOUTH, 2)
        CYCLOPS_2463(2848, 3551, 2, SOUTH, 3)
        CYCLOPS_2467(2851, 3535, 2, SOUTH, 3)
        CYCLOPS_2466(2851, 3540, 2, SOUTH, 3)
        CYCLOPS_2464(2854, 3542, 2, SOUTH, 3)
        CYCLOPS_2465(2855, 3535, 2, SOUTH, 3)
        CYCLOPS_2465(2858, 3550, 2, SOUTH, 3)
        CYCLOPS_2466(2862, 3550, 2, SOUTH, 3)
        CYCLOPS_2463(2866, 3544, 2, SOUTH, 3)
        CYCLOPS_2467(2868, 3552, 2, SOUTH, 3)
        CYCLOPS_2468(2870, 3541, 2, SOUTH, 3)
        CYCLOPS_2464(2872, 3552, 2, SOUTH, 3)
    }
}