package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11320Spawns : NPCSpawnsScript() {
    init {
        MOUNTAIN_GOAT_4117(2832, 3597, 0, SOUTH, 4)
        SOLDIER_4092(2840, 3589, 0, WEST, 0)
        THROWER_TROLL_935(2851, 3598, 0, NORTH, 0)
        MOUNTAIN_TROLL(2852, 3591, 0, SOUTH, 8)
        THROWER_TROLL(2854, 3600, 0, NORTH, 0)
        ROCK(2855, 3591, 0, SOUTH, 5)
        MOUNTAIN_GOAT_4117(2856, 3610, 0, SOUTH, 4)
        STICK(2857, 3591, 0, <PERSON>O<PERSON><PERSON>, 3)
        MOUNTAIN_TROLL_938(2858, 3586, 0, SOUTH, 2)
        THROWER_TROLL_932(2859, 3600, 0, NORTH, 0)
        DUNG(2862, 3588, 0, WEST, 0)
        MOUNTAIN_TROLL(2863, 3595, 0, SOUTH, 8)
        THROWER_TROLL_933(2863, 3600, 0, NORTH, 0)
        ASH(2864, 3588, 0, EAST, 0)
        MOUNTAIN_TROLL_937(2864, 3595, 0, SOUTH, 6)
        MOUNTAIN_TROLL_938(2865, 3595, 0, SOUTH, 2)
        MOUNTAIN_TROLL_939(2866, 3595, 0, SOUTH, 9)
        MOUNTAIN_TROLL_940(2867, 3595, 0, SOUTH, 5)
        THROWER_TROLL_934(2867, 3600, 0, NORTH, 0)
        MOUNTAIN_TROLL_941(2868, 3595, 0, SOUTH, 11)
        MOUNTAIN_TROLL_942(2869, 3595, 0, SOUTH, 9)
        THROWER_TROLL_935(2870, 3600, 0, NORTH, 0)
        MOUNTAIN_TROLL(2871, 3584, 0, SOUTH, 8)
        MOUNTAIN_TROLL_940(2871, 3587, 0, SOUTH, 5)
        MOUNTAIN_TROLL_937(2872, 3584, 0, SOUTH, 6)
        PEE_HAT(2872, 3598, 0, SOUTH, 0)
        MOUNTAIN_TROLL_942(2873, 3590, 0, SOUTH, 9)
        KRAKA(2875, 3598, 0, SOUTH, 0)
        MOUNTAIN_TROLL_941(2876, 3586, 0, SOUTH, 11)
        MOUNTAIN_TROLL_939(2876, 3589, 0, SOUTH, 9)
    }
}