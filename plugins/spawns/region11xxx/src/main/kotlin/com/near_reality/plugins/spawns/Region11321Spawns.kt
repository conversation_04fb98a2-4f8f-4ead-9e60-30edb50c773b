package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11321Spawns : NPCSpawnsScript() {
    init {
        TOOL_LEPRECHAUN_757(2826, 3685, 0, SOUTH, 0)
        740(2830, 3697, 0, SOUTH, 5)
        8412(2830, 3698, 0, SOUTH, 5)
        8420(2831, 3697, 0, SOUTH, 0)
        761(2841, 3691, 0, <PERSON>OUT<PERSON>, 5)
        MOUNTAIN_TROLL_937(2843, 3675, 0, SOUT<PERSON>, 6)
        MOUNTAIN_TROLL_942(2843, 3682, 0, SOUTH, 9)
        MOUNTAIN_TROLL_941(2845, 3684, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        MOUNTAIN_TROLL_939(2847, 3676, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        MOUNTAIN_TROLL_940(2850, 3673, 0, SOUTH, 5)
        MOUNTAIN_TROLL_937(2850, 3682, 0, SOUTH, 6)
        MOUNTAIN_TROLL_938(2852, 3677, 0, SOUTH, 2)
        MUSHROOM(2854, 3689, 0, EAST, 0)
        MOUNTAIN_TROLL(2856, 3683, 0, SOUTH, 8)
    }
}