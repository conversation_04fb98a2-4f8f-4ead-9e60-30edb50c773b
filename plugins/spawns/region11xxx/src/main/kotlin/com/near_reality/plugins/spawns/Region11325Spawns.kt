package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11325Spawns : NPCSpawnsScript() {
    init {
        ICEBERG(2817, 3935, 0, SOUTH, 0)
        ICEBERG_5818(2817, 3937, 0, SOUTH, 0)
        ICEBERG(2817, 3964, 0, SOUTH, 0)
        ICEBERG(2818, 3955, 0, SOUTH, 0)
        ICEBERG(2818, 3964, 0, SOUTH, 0)
        ICEBERG_5818(2819, 3965, 0, SOUTH, 0)
        ICEBERG(2820, 3950, 0, SOUTH, 0)
        ICEBERG_5818(2821, 3952, 0, SOUTH, 0)
        ICEBERG(2822, 3937, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        ICEBERG(2823, 3930, 0, SOUTH, 0)
        ICEBERG(2823, 3955, 0, <PERSON>O<PERSON><PERSON>, 0)
        ICEBERG_5818(2824, 3939, 0, SOUTH, 0)
        ICEBERG_5818(2825, 3929, 0, SOUTH, 0)
        ICEBERG(2827, 3926, 0, SOUTH, 0)
        ICEBERG(2828, 3930, 0, SOUTH, 0)
        ICEBERG(2828, 3947, 0, SOUTH, 0)
        ICEBERG(2828, 3960, 0, SOUTH, 0)
        ICEBERG_5818(2828, 3962, 0, SOUTH, 0)
        ICEBERG(2829, 3960, 0, SOUTH, 0)
        ICEBERG_5818(2830, 3947, 0, SOUTH, 0)
        ICEBERG(2831, 3965, 0, SOUTH, 0)
        ICEBERG(2834, 3948, 0, SOUTH, 0)
        ICEBERG(2836, 3955, 0, SOUTH, 0)
        ICEBERG(2836, 3964, 0, SOUTH, 0)
        ICEBERG_5818(2837, 3955, 0, SOUTH, 0)
        ICEBERG_5818(2837, 3963, 0, SOUTH, 0)
        SQUIRREL_8422(2838, 3912, 0, SOUTH, 8)
        MOUNTAIN_GOAT_4145(2838, 3923, 0, SOUTH, 2)
        ICEBERG(2838, 3962, 0, SOUTH, 0)
        MOUNTAIN_GOAT_4145(2840, 3912, 0, SOUTH, 2)
        ICEBERG(2840, 3956, 0, SOUTH, 0)
        ICEBERG(2841, 3965, 0, SOUTH, 0)
        ICEBERG(2842, 3958, 0, SOUTH, 0)
        MOUNTAIN_GOAT_4145(2843, 3915, 0, SOUTH, 2)
        MOUNTAIN_GOAT_4145(2844, 3938, 0, SOUTH, 2)
        SQUIRREL_8422(2845, 3926, 0, SOUTH, 8)
        ICEBERG(2845, 3966, 0, SOUTH, 0)
        8443(2846, 3934, 0, SOUTH, 1)
        MOUNTAIN_GOAT_4145(2847, 3948, 0, SOUTH, 2)
        MOUNTAIN_GOAT_4145(2848, 3915, 0, SOUTH, 2)
        ICE_WOLF_713(2848, 3921, 0, SOUTH, 6)
        ICE_WOLF_713(2848, 3926, 0, SOUTH, 6)
        8406(2848, 3930, 0, SOUTH, 5)
        MOUNTAIN_GOAT_4145(2848, 3952, 0, SOUTH, 2)
        ICE_WOLF_713(2850, 3923, 0, SOUTH, 6)
        YELLOW_SNOW(2851, 3933, 0, SOUTH, 0)
        ICE_WOLF_713(2852, 3921, 0, SOUTH, 6)
        ICE_WOLF_713(2852, 3926, 0, SOUTH, 6)
        SQUIRREL_8422(2852, 3951, 0, SOUTH, 8)
        SQUIRREL_8422(2857, 3916, 0, SOUTH, 8)
        PEBBLE(2858, 3931, 0, SOUTH, 1)
        8466(2858, 3936, 0, SOUTH, 2)
        8458(2859, 3934, 0, SOUTH, 4)
        MOUNTAIN_GOAT_4145(2861, 3914, 0, SOUTH, 2)
        8469(2864, 3945, 0, NORTH, 0)
        SNOWY_KNIGHT(2864, 3957, 0, SOUTH, 6)
        8468(2865, 3946, 0, SOUTH, 5)
        ICEBERG_5818(2865, 3967, 0, SOUTH, 0)
        SNOWY_KNIGHT(2867, 3954, 0, SOUTH, 6)
        8441(2868, 3934, 0, SOUTH, 5)
        MOUNTAIN_GOAT_4145(2869, 3916, 0, SOUTH, 2)
        8462(2869, 3927, 0, EAST, 0)
        8465(2870, 3945, 0, SOUTH, 5)
        SNOWY_KNIGHT(2870, 3955, 0, SOUTH, 6)
        8477(2871, 3931, 0, SOUTH, 5)
        8427(2871, 3934, 0, SOUTH, 5)
        8457(2871, 3943, 0, SOUTH, 5)
        MOUNTAIN_GOAT_4145(2871, 3957, 0, SOUTH, 2)
        8433(2872, 3934, 0, NORTH, 0)
        8415(2872, 3936, 0, SOUTH, 5)
        8461(2872, 3944, 0, SOUTH, 5)
        ICEBERG(2872, 3967, 0, SOUTH, 0)
        8408(2873, 3936, 0, SOUTH, 5)
        8417(2874, 3934, 0, WEST, 0)
        SNOWY_KNIGHT(2874, 3952, 0, SOUTH, 6)
        8436(2875, 3935, 0, SOUTH, 5)
        GOAT_POO(2876, 3927, 0, SOUTH, 2)
        8416(2877, 3947, 0, SOUTH, 5)
        8432(2878, 3948, 0, SOUTH, 5)
        MOUNTAIN_GOAT_4145(2878, 3951, 0, SOUTH, 2)
        SQUIRREL_8422(2878, 3954, 0, SOUTH, 8)
        8437(2879, 3946, 0, SOUTH, 5)
    }
}