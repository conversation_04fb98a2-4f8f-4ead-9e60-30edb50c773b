package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11326Spawns : NPCSpawnsScript() {
    init {
        ICEBERG_5818(2820, 3985, 0, SOUTH, 0)
        ICEBERG(2824, 4002, 0, SOUTH, 0)
        ICEBERG(2826, 3978, 0, SOUTH, 0)
        ICEBERG_5818(2826, 4004, 0, SOUTH, 0)
        ICEBERG(2826, 4009, 0, SOUTH, 0)
        ICEBERG(2829, 3973, 0, SOUTH, 0)
        ICEBERG_5818(2829, 3975, 0, SOUTH, 0)
        ICEBERG(2831, 3990, 0, SOUTH, 0)
        ICEBERG(2832, 3978, 0, <PERSON>OUTH, 0)
        ICEBERG_5818(2833, 3992, 0, SOUTH, 0)
        ICEBERG(2836, 3994, 0, SOUTH, 0)
        ICEBERG(2837, 3970, 0, SOUTH, 0)
        ICEBERG_5818(2839, 3971, 0, SOUTH, 0)
        ICEBERG(2840, 3974, 0, SOUTH, 0)
        ICEBERG(2842, 3985, 0, SOUTH, 0)
        ICEBERG(2844, 4005, 0, SOUTH, 0)
        ICEBERG(2845, 3971, 0, SOUTH, 0)
        ICEBERG_5818(2845, 3984, 0, SOUTH, 0)
        ICEBERG_5818(2845, 4006, 0, SOUTH, 0)
        ICEBERG(2848, 3975, 0, SOUTH, 0)
        ICEBERG(2848, 3982, 0, SOUTH, 0)
        8414(2851, 3968, 0, SOUTH, 5)
        ICEBERG(2851, 3971, 0, SOUTH, 0)
        ICEBERG_5818(2852, 3980, 0, SOUTH, 0)
        ICEBERG(2852, 3992, 0, SOUTH, 0)
        ICEBERG(2856, 3973, 0, SOUTH, 0)
        ICEBERG_5818(2858, 3993, 0, SOUTH, 0)
        ICEBERG(2859, 3996, 0, SOUTH, 0)
        ICEBERG(2860, 3977, 0, SOUTH, 0)
        ICEBERG(2860, 3983, 0, SOUTH, 0)
        ICEBERG(2860, 4012, 0, SOUTH, 0)
        ICEBERG_5818(2861, 3979, 0, SOUTH, 0)
        ICEBERG(2863, 3971, 0, SOUTH, 0)
        ICEBERG(2869, 3968, 0, SOUTH, 0)
        ICEBERG(2869, 3975, 0, SOUTH, 0)
        ICEBERG(2869, 3990, 0, SOUTH, 0)
        ICEBERG_5818(2869, 3993, 0, SOUTH, 0)
        ICEBERG(2872, 4005, 0, SOUTH, 0)
        ICEBERG(2878, 3976, 0, SOUTH, 0)
        ICEBERG_5818(2878, 3979, 0, SOUTH, 0)
    }
}