package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11339Spawns : NPCSpawnsScript() {
    init {
        RABBIT_3420(2823, 4827, 0, SOUTH, 4)
        RABBIT_3420(2847, 4814, 0, SOUTH, 4)
        RABBIT_3420(2847, 4849, 0, SOUTH, 4)
        RABBIT_3420(2858, 4831, 0, SOUTH, 4)
    }
}