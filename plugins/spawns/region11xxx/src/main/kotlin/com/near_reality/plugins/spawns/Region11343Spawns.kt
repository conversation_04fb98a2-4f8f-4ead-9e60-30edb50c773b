package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11343Spawns : NPCSpawnsScript() {
    init {
        GUARD_1002(2826, 5100, 0, SOUTH, 5)
        BUTTERFLY(2828, 5091, 0, SOUTH, 7)
        BUTTERFLY_235(2831, 5070, 0, SOUTH, 7)
        BUTTERFLY(2832, 5108, 0, SOUTH, 7)
        CERIL_CARNILLEAN_4577(2833, 5099, 0, SOUTH, 5)
        SPICE_SELLER_4579(2834, 5101, 0, SOUTH, 5)
        BUTTERFLY(2835, 5079, 0, SO<PERSON><PERSON>, 7)
        ALI_MORRISANE_4585(2835, 5089, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        COUNCILLOR_HALGRIVE(2836, 5087, 0, <PERSON><PERSON>UTH, 5)
        GUARD_1003(2836, 5105, 0, SOUTH, 5)
        GUARD_1000(2840, 5067, 0, SOUTH, 5)
        GUARD_1001(2840, 5076, 0, SOUTH, 5)
        GEM_MERCHANT_4581(2842, 5091, 0, SOUTH, 5)
        BUTTERFLY_235(2843, 5106, 0, SOUTH, 7)
        BUTTERFLY_235(2844, 5080, 0, SOUTH, 7)
        ZENESHA(2844, 5092, 0, SOUTH, 5)
        BUTTERFLY(2846, 5067, 0, SOUTH, 7)
        GUARD_999(2846, 5087, 0, SOUTH, 5)
        SILVER_MERCHANT(2846, 5097, 0, SOUTH, 5)
        SILK_MERCHANT_4583(2847, 5098, 0, SOUTH, 5)
        FUR_TRADER_4580(2848, 5096, 0, SOUTH, 5)
        GUARD_998(2849, 5087, 0, SOUTH, 5)
        BUTTERFLY(2850, 5111, 0, SOUTH, 7)
        BUTTERFLY(2853, 5086, 0, SOUTH, 7)
        GUARD_1005(2855, 5076, 0, SOUTH, 5)
        GUARD_1004(2856, 5067, 0, SOUTH, 5)
        6228(2857, 5091, 0, SOUTH, 5)
        GUARD_1007(2859, 5105, 0, SOUTH, 5)
        BUTTERFLY_235(2860, 5079, 0, SOUTH, 7)
        6229(2863, 5086, 0, SOUTH, 5)
        6227(2863, 5101, 0, SOUTH, 5)
        BUTTERFLY(2864, 5071, 0, SOUTH, 7)
        BUTTERFLY_235(2866, 5104, 0, SOUTH, 7)
        BUTTERFLY_235(2868, 5096, 0, SOUTH, 7)
        GUARD_1006(2869, 5100, 0, SOUTH, 5)
        6224(2832, 5098, 1, SOUTH, 5)
        GUARD_1008(2847, 5102, 1, SOUTH, 5)
        GUARD_1009(2848, 5096, 1, SOUTH, 5)
        6226(2858, 5087, 1, SOUTH, 5)
        6225(2861, 5093, 1, SOUTH, 5)
    }
}