package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11346Spawns : NPCSpawnsScript() {
    init {
        //SPIRITUAL_MAGE_3168(2830, 5284, 2, SOUTH, 2)
        KREEARRA(2832, 5302, 2, SOUTH, 2)
        //AVIANSIE_3182(2833, 5289, 2, SOUTH, 5)
        AVIANSIE_3174(2834, 5283, 2, <PERSON><PERSON><PERSON><PERSON>, 6)
        AVIANSIE_3179(2835, 5271, 2, <PERSON><PERSON><PERSON><PERSON>, 9)
        WEREWOLF_3136(2835, 5286, 2, <PERSON><PERSON><PERSON>H, 7)
        SPIRITUAL_MAGE_3168(2837, 5265, 2, <PERSON><PERSON><PERSON><PERSON>, 2)
        SPIRITUAL_WARRIOR_3166(2837, 5288, 2, <PERSON><PERSON><PERSON><PERSON>, 5)
        AVIANSIE_3176(2839, 5275, 2, <PERSON><PERSON><PERSON><PERSON>, 7)
        AVIANSIE_3174(2840, 5268, 2, <PERSON><PERSON><PERSON><PERSON>, 6)
        A<PERSON><PERSON><PERSON>E_3171(2841, 5263, 2, SOUTH, 6)
        SPIRITUAL_WARRIOR_3166(2841, 5281, 2, SOUTH, 5)
        AVIANSIE_3176(2842, 5286, 2, SOUTH, 7)
        AVIANSIE_3171(2842, 5291, 2, SOUTH, 6)
        SPIRITUAL_RANGER_3160(2843, 5266, 2, SOUTH, 7)
        SPIRITUAL_RANGER_3167(2845, 5260, 2, SOUTH, 6)
        AVIANSIE_3176(2845, 5269, 2, SOUTH, 7)
        BLOODVELD_3138(2845, 5283, 2, SOUTH, 7)
        AVIANSIE_3181(2846, 5279, 2, SOUTH, 9)
        AVIANSIE_3182(2848, 5259, 2, SOUTH, 5)
        SPIRITUAL_RANGER_3167(2848, 5274, 2, SOUTH, 6)
        WEREWOLF_3135(2848, 5277, 2, SOUTH, 7)
        SPIRITUAL_RANGER_3160(2848, 5282, 2, SOUTH, 7)
        AVIANSIE_3178(2849, 5270, 2, SOUTH, 9)
        AVIANSIE_3180(2849, 5292, 2, SOUTH, 8)
        AVIANSIE_3175(2851, 5274, 2, SOUTH, 9)
        AVIANSIE_3177(2851, 5310, 2, SOUTH, 10)
        AVIANSIE_3170(2853, 5264, 2, SOUTH, 10)
        SPIRITUAL_MAGE_3168(2854, 5273, 2, SOUTH, 2)
        AVIANSIE_3180(2854, 5299, 2, SOUTH, 8)
        ICEFIEND(2854, 5304, 2, SOUTH, 6)
        AVIANSIE_3183(2855, 5283, 2, SOUTH, 7)
        AVIANSIE_3173(2856, 5269, 2, SOUTH, 8)
        SPIRITUAL_WARRIOR_3166(2858, 5263, 2, SOUTH, 5)
        AVIANSIE_3172(2858, 5304, 2, SOUTH, 9)
        WEREWOLF_3136(2859, 5283, 2, SOUTH, 7)
        AVIANSIE_3177(2861, 5297, 2, SOUTH, 10)
        AVIANSIE_3172(2861, 5310, 2, SOUTH, 9)
        GORAK_3141(2862, 5260, 2, SOUTH, 6)
        FERAL_VAMPYRE(2862, 5292, 2, SOUTH, 8)
        AVIANSIE_3175(2863, 5268, 2, SOUTH, 9)
        PYREFIEND_3139(2863, 5307, 2, SOUTH, 4)
        PYREFIEND_3139(2864, 5280, 2, SOUTH, 4)
        PYREFIEND_3139(2864, 5281, 2, SOUTH, 4)
        GORAK_3141(2864, 5293, 2, SOUTH, 6)
        AVIANSIE_3170(2866, 5262, 2, SOUTH, 10)
        AVIANSIE_3178(2866, 5271, 2, SOUTH, 9)
        SPIRITUAL_MAGE_3168(2867, 5266, 2, SOUTH, 2)
        AVIANSIE(2867, 5305, 2, SOUTH, 8)
        AVIANSIE_3172(2868, 5288, 2, SOUTH, 9)
        AVIANSIE_3173(2870, 5265, 2, SOUTH, 8)
        AVIANSIE_3183(2871, 5310, 2, SOUTH, 7)
        AVIANSIE_3181(2873, 5260, 2, SOUTH, 9)
        AVIANSIE_3180(2873, 5286, 2, SOUTH, 8)
        BLOODVELD_3138(2873, 5299, 2, SOUTH, 7)
        AVIANSIE_3172(2874, 5292, 2, SOUTH, 9)
        ICEFIEND(2875, 5296, 2, SOUTH, 6)
        AVIANSIE_3183(2876, 5298, 2, SOUTH, 7)
        AVIANSIE_3180(2877, 5310, 2, SOUTH, 8)
        AVIANSIE_3177(2878, 5282, 2, SOUTH, 10)
        FERAL_VAMPYRE(2878, 5286, 2, SOUTH, 8)
        AVIANSIE(2878, 5291, 2, SOUTH, 8)
        KNIGHT_16023(2829, 5292, 2, SOUTH, 0)
    }
}