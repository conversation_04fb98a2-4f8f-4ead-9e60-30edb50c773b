package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11412Spawns : NPCSpawnsScript() {
    init {
        JOGRE(2825, 9516, 0, SOUTH, 3)
        JOGRE(2831, 9515, 0, SOUTH, 3)
        JOGRE(2834, 9499, 0, SOUTH, 3)
        JOGRE(2835, 9526, 0, SOUTH, 3)
        JOGRE(2836, 9510, 0, SOUTH, 3)
        JOGRE(2838, 9490, 0, SOUTH, 3)
        JOGRE(2840, 9518, 0, SOUTH, 3)
        JOGRE(2845, 9518, 0, SOUTH, 3)
        JOGRE(2848, 9483, 0, <PERSON>O<PERSON><PERSON>, 3)
        JO<PERSON><PERSON>(2849, 9517, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        JOGRE(2857, 9501, 0, <PERSON>OUT<PERSON>, 3)
        JOGRE(2857, 9523, 0, SOUTH, 3)
        JOGRE(2860, 9517, 0, SOUTH, 3)
        JOGRE(2863, 9493, 0, SOUTH, 3)
        JOGRE(2864, 9526, 0, SOUTH, 3)
        JOGRE(2866, 9512, 0, SOUTH, 3)
        JOGRE(2867, 9529, 0, SOUTH, 3)
        JOGRE(2841, 9498, 0, SOUTH, 3)
        JOGRE(2844, 9501, 0, SOUTH, 3)
        JOGRE(2845, 9495, 0, SOUTH, 3)
        JOGRE(2849, 9495, 0, SOUTH, 3)
        JOGRE(2852, 9493, 0, SOUTH, 3)
        JOGRE(2855, 9491, 0, SOUTH, 3)
    }
}