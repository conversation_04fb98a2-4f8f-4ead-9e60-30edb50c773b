package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11413Spawns : NPCSpawnsScript() {
    init {
        LESSER_DEMON(2831, 9562, 0, SOUTH, 4)
        GIANT_BAT(2834, 9568, 0, SOUTH, 11)
        DEADLY_RED_SPIDER(2834, 9580, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(2834, 9584, 0, SOUTH, 8)
        LESSER_DEMON_2008(2836, 9558, 0, SOUTH, 6)
        DEADLY_RED_SPIDER(2836, 9576, 0, SOUTH, 8)
        LESSER_DEMON_2007(2837, 9565, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        DEADLY_RED_SPIDER(2838, 9582, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        LESSER_DEMON(2840, 9552, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        LESSER_DEMON_2006(2841, 9559, 0, <PERSON><PERSON><PERSON>H, 6)
        DEADLY_RED_SPIDER(2842, 9583, 0, SOUTH, 8)
        LESSER_DEMON_2018(2843, 9557, 0, SOUTH, 8)
        SKELETON(2848, 9581, 0, SOUTH, 7)
        SK<PERSON>ETON_71(2853, 9573, 0, SOUTH, 7)
        GIANT_BAT(2856, 9575, 0, SOUTH, 11)
        SKELETON_73(2859, 9572, 0, SOUTH, 8)
        SKELETON_72(2860, 9577, 0, SOUTH, 8)
        GIANT_BAT(2861, 9568, 0, SOUTH, 11)
        SKELETON(2864, 9568, 0, SOUTH, 7)
    }
}