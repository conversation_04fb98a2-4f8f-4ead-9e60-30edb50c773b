package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11414Spawns : NPCSpawnsScript() {
    init {
        SKELETON_79(2830, 9657, 0, SOUTH, 8)
        SKELETON_77(2832, 9661, 0, SOUTH, 7)
        SKELETON_77(2834, 9650, 0, SOUTH, 7)
        SKELETON_80(2834, 9655, 0, SOUTH, 8)
        SKELETON_78(2834, 9658, 0, SOUTH, 8)
        LESSER_DEMON(2835, 9602, 0, SOUT<PERSON>, 4)
        LESSER_DEMON_2018(2837, 9623, 0, SOUTH, 8)
        LESSER_DEMON_2007(2838, 9610, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SKELETON_81(2838, 9653, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        LESSER_DEMON_2006(2839, 9604, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        SKELETON_83(2840, 9628, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        SKELETON_82(2840, 9638, 0, SOUTH, 7)
        SKELETON_79(2840, 9649, 0, SOUTH, 8)
        SKELETON_83(2841, 9643, 0, SOUTH, 6)
        SKELETON_82(2842, 9632, 0, SOUTH, 7)
        SKELETON_82(2843, 9645, 0, SOUTH, 7)
        SKELETON_82(2844, 9628, 0, SOUTH, 7)
        LESSER_DEMON_2008(2845, 9612, 0, SOUTH, 6)
        SKELETON_83(2845, 9636, 0, SOUTH, 6)
        816(2852, 9637, 0, SOUTH, 5)
        7326(2857, 9645, 0, SOUTH, 2)
        RAT_2854(2860, 9635, 0, SOUTH, 14)
        RAT_2854(2862, 9638, 0, SOUTH, 14)
    }
}