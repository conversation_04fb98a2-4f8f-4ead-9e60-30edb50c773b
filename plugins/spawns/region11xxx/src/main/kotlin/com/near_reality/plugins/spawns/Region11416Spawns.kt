package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11416Spawns : NPCSpawnsScript() {
    init {
        BLACK_DEMON_2051(2826, 9791, 0, SOUTH, 3)
        BLACK_DEMON_2050(2831, 9790, 0, SOUTH, 3)
        BLACK_DEMON_2048(2837, 9787, 0, SOUTH, 7)
        ZOMBIE_64(2842, 9766, 0, SOUTH, 4)
        BLACK_DEMON_2051(2842, 9786, 0, SOUTH, 3)
        ZOMBIE_65(2843, 9761, 0, SOUTH, 4)
        ZOMBIE_67(2846, 9756, 0, <PERSON>OUT<PERSON>, 4)
        ZOMBIE_68(2846, 9760, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        BLA<PERSON><PERSON>_DEMON_2050(2847, 9784, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        ZOMBIE_66(2849, 9763, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        BLACK_DEMON_2048(2849, 9779, 0, SOUTH, 7)
        BLACK_DEMON_2049(2849, 9791, 0, SOUTH, 6)
        BLACK_DEMON_2048(2853, 9786, 0, SOUTH, 7)
        BLACK_DEMON_2052(2854, 9776, 0, SOUTH, 2)
        BLACK_DEMON_2050(2854, 9781, 0, SOUTH, 3)
        BLACK_DEMON_2051(2858, 9772, 0, SOUTH, 3)
        BLACK_DEMON_2049(2860, 9763, 0, SOUTH, 6)
        BLACK_DEMON_2049(2860, 9781, 0, SOUTH, 6)
        GREATER_DEMON_2028(2861, 9747, 0, SOUTH, 4)
        GREATER_DEMON_2026(2862, 9751, 0, SOUTH, 4)
        BLACK_DEMON_2048(2862, 9776, 0, SOUTH, 7)
        BLACK_DEMON_2050(2863, 9769, 0, SOUTH, 3)
        BLACK_DEMON_2052(2866, 9783, 0, SOUTH, 2)
        BLACK_DEMON_2049(2869, 9776, 0, SOUTH, 6)
        BLACK_DEMON_2051(2871, 9783, 0, SOUTH, 3)
        BLACK_DEMON_2050(2875, 9776, 0, SOUTH, 3)
    }
}