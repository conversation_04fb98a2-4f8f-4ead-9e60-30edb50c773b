package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11418Spawns : NPCSpawnsScript() {
    init {
        ICE_WARRIOR(2822, 9901, 0, SOUTH, 6)
        ICE_WARRIOR(2836, 9905, 0, SOUTH, 6)
        ICE_WARRIOR(2838, 9917, 0, SOUTH, 6)
        ICE_WARRIOR(2847, 9919, 0, SOUTH, 6)
        ICE_WARRIOR(2848, 9912, 0, SOUTH, 6)
        DWARF_292(2860, 9874, 0, SOUTH, 9)
        DWARF_292(2860, 9877, 0, SOUTH, 9)
        DWARF_292(2862, 9874, 0, SOUT<PERSON>, 9)
        DWARF_292(2862, 9877, 0, SOUTH, 9)
        KHORVAK_A_DWARVEN_ENGINEER(2864, 9876, 0, SOUTH, 2)
        HOLOY(2865, 9871, 0, NORTH, 0)
        6254(2865, 9876, 0, SOUTH, 2)
        6255(2865, 9877, 0, SOUTH, 2)
        DWARF_292(2867, 9879, 0, SOUTH, 9)
        DWARF_292(2868, 9874, 0, SOUTH, 9)
        CAPTAIN_NINTO(2869, 9877, 0, SOUTH, 2)
        DWARF_292(2869, 9879, 0, SOUTH, 9)
        ICE_SPIDER(2869, 9912, 0, SOUTH, 6)
        DWARF_292(2870, 9874, 0, SOUTH, 9)
        CART_CONDUCTOR_2390(2874, 9872, 0, SOUTH, 2)
    }
}