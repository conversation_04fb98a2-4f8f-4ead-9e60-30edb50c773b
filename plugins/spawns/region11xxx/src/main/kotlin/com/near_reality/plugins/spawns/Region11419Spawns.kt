package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11419Spawns : NPCSpawnsScript() {
    init {
        ICE_SPIDER(2819, 9950, 0, SOUTH, 6)
        ICE_SPIDER(2824, 9934, 0, SOUTH, 6)
        ICE_SPIDER(2827, 9925, 0, SOUTH, 6)
        ICE_SPIDER(2828, 9956, 0, SOUTH, 6)
        ICE_WARRIOR(2834, 9940, 0, SOUTH, 6)
        ICE_WARRIOR(2836, 9953, 0, SOUTH, 6)
        ICE_WARRIOR(2844, 9944, 0, SOUTH, 6)
        ICE_SPIDER(2857, 9969, 0, SOUTH, 6)
        ICE_WARRIOR_2851(2864, 9950, 0, SOUTH, 5)
        ICE_WARRIOR_2851(2865, 9951, 0, <PERSON>OUTH, 5)
        ICE_WARRIOR_2851(2866, 9948, 0, SOUTH, 5)
        ICE_QUEEN(2866, 9956, 0, SOUTH, 5)
        ICE_WARRIOR_2851(2867, 9948, 0, SOUTH, 5)
        ICE_WARRIOR_2851(2867, 9951, 0, SOUTH, 5)
        ICE_WARRIOR_2851(2868, 9950, 0, SOUTH, 5)
        ICE_SPIDER(2872, 9972, 0, SOUTH, 6)
    }
}