package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11421Spawns : NPCSpawnsScript() {
    init {
        GODRIC(2827, 10077, 0, SOUTH, 1)
        EADGAR(2829, 10083, 0, SOUTH, 2)
        TWIG_4133(2832, 10079, 0, EAST, 0)
        BERRY_4134(2832, 10083, 0, EAST, 0)
        GUARD_4151(2850, 10086, 0, SOUTH, 6)
        GUARD_4149(2850, 10089, 0, SOUTH, 3)
        GUARD_4148(2850, 10092, 0, SOUTH, 0)
        GUARD_4153(2855, 10082, 0, SOUTH, 0)
        GUARD_4150(2855, 10089, 0, <PERSON>O<PERSON><PERSON>, 5)
        GUARD_4156(2857, 10075, 0, SOUTH, 5)
        GUARD_4154(2858, 10076, 0, SOUTH, 5)
        GUARD_4152(2861, 10080, 0, SOUTH, 5)
        GUARD_4155(2864, 10085, 0, SOUTH, 5)
        MOUNTAIN_TROLL_939(2825, 10086, 1, SOUTH, 9)
        UG(2827, 10064, 1, NORTH, 0)
        MOUNTAIN_TROLL_4143(2827, 10081, 1, SOUTH, 9)
        ARRG(2828, 10095, 1, NORTH, 0)
        AGA(2828, 10104, 1, SOUTH, 0)
        MOUNTAIN_TROLL(2829, 10079, 1, SOUTH, 8)
        MOUNTAIN_TROLL_940(2829, 10083, 1, SOUTH, 5)
        MOUNTAIN_TROLL_942(2830, 10086, 1, SOUTH, 9)
        MOUNTAIN_TROLL_939(2836, 10077, 1, SOUTH, 9)
        MOUNTAIN_TROLL_937(2836, 10089, 1, SOUTH, 6)
        MOUNTAIN_TROLL_941(2839, 10101, 1, SOUTH, 11)
        COOK_4141(2840, 10054, 1, SOUTH, 0)
        8423(2841, 10061, 1, SOUTH, 0)
        COOK_4140(2842, 10056, 1, SOUTH, 3)
        BURNTMEAT(2844, 10057, 1, WEST, 0)
        COOK_4142(2846, 10055, 1, SOUTH, 1)
        743(2855, 10052, 1, SOUTH, 5)
        MOUNTAIN_GOAT_4145(2856, 10056, 1, SOUTH, 2)
        MOUNTAIN_GOAT_4147(2857, 10055, 1, SOUTH, 2)
        MOUNTAIN_GOAT_4147(2857, 10059, 1, SOUTH, 2)
        MOUNTAIN_GOAT_4145(2858, 10058, 1, SOUTH, 2)
        MOUNTAIN_GOAT_4146(2859, 10055, 1, SOUTH, 2)
        TROLL_GENERAL_4121(2822, 10073, 2, SOUTH, 4)
        TROLL_GENERAL_4122(2829, 10100, 2, SOUTH, 3)
        1452(2830, 10061, 2, SOUTH, 7)
        TROLL_GENERAL(2831, 10086, 2, SOUTH, 2)
        MOUNTAIN_TROLL_942(2836, 10074, 2, SOUTH, 9)
        MOUNTAIN_TROLL_4143(2836, 10082, 2, SOUTH, 9)
        MOUNTAIN_TROLL_941(2836, 10087, 2, SOUTH, 11)
        MOUNTAIN_TROLL_940(2836, 10095, 2, SOUTH, 5)
        MOUNTAIN_TROLL_939(2838, 10098, 2, SOUTH, 9)
        MOUNTAIN_TROLL_941(2851, 10052, 2, SOUTH, 11)
        MOUNTAIN_TROLL_940(2852, 10107, 2, SOUTH, 5)
        MOUNTAIN_TROLL_938(2855, 10054, 2, SOUTH, 2)
    }
}