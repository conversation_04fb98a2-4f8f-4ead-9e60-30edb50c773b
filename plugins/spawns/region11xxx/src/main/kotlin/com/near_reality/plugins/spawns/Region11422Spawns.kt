package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11422Spawns : NPCSpawnsScript() {
    init {
        6262(2824, 10168, 0, NORTH, 0)
        DWARF_292(2826, 10152, 0, SOUTH, 9)
        DWARF_292(2837, 10148, 0, SOUTH, 9)
        DWARVEN_FERRYMAN(2839, 10128, 0, SOUTH, 2)
        2432(2842, 10129, 0, WEST, 0)
        DWARF_292(2854, 10125, 0, SOUTH, 9)
        DWARF_292(2854, 10164, 0, SOUTH, 9)
        DWARVEN_FERRYMAN_4897(2855, 10144, 0, SOUTH, 2)
        DWARF_292(2861, 10167, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        DWARF_292(2866, 10126, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        DWARF_292(2872, 10118, 0, <PERSON><PERSON>UTH, 9)
        6263(2873, 10166, 0, SOUTH, 2)
        DERNI(2870, 10138, 0, SOUTH_WEST, 0)
        DERNU(2870, 10139, 0, WEST, 0)
        MIODVETNIR(2870, 10140, 0, SOUTH_WEST, 0)
    }
}