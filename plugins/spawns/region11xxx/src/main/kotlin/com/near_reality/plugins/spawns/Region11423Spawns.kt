package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11423Spawns : NPCSpawnsScript() {
    init {
        BLACK_GUARD_6047(2822, 10212, 0, SOUTH, 2)
        BLACK_GUARD_BERSERKER_6050(2823, 10220, 0, SOUTH, 2)
        BLACK_GUARD_BERSERKER_6051(2825, 10209, 0, SOUTH, 2)
        COMMANDER_VELDABAN_6045(2826, 10215, 0, SOUTH, 2)
        SARO(2827, 10199, 0, SOUTH, 2)
        BLACK_GUARD_6048(2828, 10210, 0, SOUTH, 2)
        SANTIRI(2828, 10229, 0, <PERSON>O<PERSON><PERSON>, 2)
        DROMUND(2835, 10225, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        DROMUNDS_CAT(2835, 10226, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        BANKER_2368(2836, 10205, 0, <PERSON>RT<PERSON>, 0)
        GAUSS(2838, 10195, 0, SOUTH, 2)
        BANKER_2369(2838, 10205, 0, NORTH, 0)
        BARMAN_2384(2841, 10197, 0, SOUTH, 2)
        BLACK_GUARD_6049(2847, 10190, 0, SOUTH, 13)
        STONEMASON(2849, 10184, 0, SOUTH, 3)
        HAERA(2850, 10223, 0, SOUTH, 2)
        HEGIR(2851, 10225, 0, SOUTH, 2)
        RIND_THE_GARDENER(2854, 10195, 0, SOUTH, 2)
        TOMBAR(2854, 10199, 0, SOUTH, 2)
        MYNDILL(2855, 10224, 0, SOUTH, 11)
        LIBRARIAN(2858, 10225, 0, SOUTH, 2)
        ASSISTANT(2860, 10226, 0, SOUTH, 2)
        CUSTOMER_2373(2864, 10227, 0, SOUTH, 2)
        ODMAR(2865, 10194, 0, SOUTH, 2)
        AUDMANN(2866, 10206, 0, SOUTH, 2)
        PROFESSOR_MANGLETHORP(2868, 10198, 0, SOUTH, 8)
        GUNSLIK(2869, 10190, 0, SOUTH, 2)
        AGMUNDI(2869, 10211, 0, SOUTH, 2)
        WEMUND(2870, 10200, 0, SOUTH, 2)
        DWARVEN_ENGINEER(2871, 10198, 0, WEST, 0)
        VIGR(2873, 10209, 0, SOUTH, 2)
        BLACK_GUARD_BERSERKER_6052(2827, 10212, 1, SOUTH, 2)
        SUPREME_COMMANDER(2828, 10210, 1, SOUTH, 2)
        6547(2835, 10193, 1, SOUTH, 2)
        6546(2836, 10192, 1, SOUTH, 0)
        INN_KEEPER_2382(2839, 10195, 1, SOUTH, 0)
        CUSTOMER(2862, 10225, 1, SOUTH, 2)
        BLUE_OPAL_DIRECTOR_5999(2867, 10203, 1, SOUTH, 2)
        PURPLE_PEWTER_SECRETARY(2868, 10194, 1, SOUTH, 2)
        YELLOW_FORTUNE_DIRECTOR_6000(2868, 10209, 1, SOUTH, 2)
        6536(2869, 10189, 1, SOUTH, 2)
        BLUE_OPAL_SECRETARY(2869, 10205, 1, WEST, 0)
        6539(2870, 10190, 1, SOUTH, 0)
        PURPLE_PEWTER_DIRECTOR_5998(2870, 10196, 1, SOUTH, 2)
        YELLOW_FORTUNE_SECRETARY(2870, 10209, 1, SOUTH, 2)
        6538(2872, 10189, 1, SOUTH, 3)
        6542(2872, 10190, 1, SOUTH, 4)
        TRADER_6028(2875, 10198, 1, SOUTH, 2)
        6541(2877, 10195, 1, SOUTH, 2)
        TRADER(2877, 10199, 1, SOUTH, 2)
        TRADER_6029(2877, 10201, 1, SOUTH, 2)
        TRADER_6030(2877, 10202, 1, SOUTH, 2)
        6540(2878, 10196, 1, SOUTH, 2)
        TRADER_6032(2879, 10202, 1, SOUTH, 0)
    }
}