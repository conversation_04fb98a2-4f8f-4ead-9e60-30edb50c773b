package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11562Spawns : NPCSpawnsScript() {
    init {
        BIRD_5241(2891, 2723, 0, SOUTH, 26)
        2022(2891, 2724, 0, SOUTH, 0)
        SCORPION_5242(2894, 2715, 0, SOUTH, 5)
        2021(2897, 2727, 0, SOUTH, 2)
        SCORPION_5242(2899, 2734, 0, <PERSON>OUTH, 5)
        BIRD_5241(2900, 2739, 0, <PERSON><PERSON>UT<PERSON>, 26)
        SCORPION_5242(2901, 2721, 0, SOUTH, 5)
        BIRD_5241(2902, 2701, 0, <PERSON><PERSON><PERSON><PERSON>, 26)
        SCORPION_5242(2902, 2727, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        BIRD_5241(2906, 2709, 0, <PERSON><PERSON><PERSON><PERSON>, 26)
        SNAKE_5244(2911, 2718, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SNAKE_5244(2913, 2726, 0, <PERSON><PERSON><PERSON>H, 4)
        <PERSON><PERSON><PERSON>_5244(2921, 2713, 0, SOUTH, 4)
        SNAKE_5244(2921, 2727, 0, SOUTH, 4)
        SNAKE_5244(2927, 2719, 0, SOUTH, 4)
        BIRD_5241(2939, 2718, 0, <PERSON>OUTH, 26)
    }
}