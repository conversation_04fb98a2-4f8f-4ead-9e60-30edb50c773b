package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11565Spawns : NPCSpawnsScript() {
    init {
        SNAKE_2845(2880, 2922, 0, SOUTH, 4)
        SNAKE_2845(2881, 2919, 0, SOUTH, 4)
        SNAKE_2845(2884, 2921, 0, SOUTH, 4)
        OOMLIE_BIRD(2885, 2926, 0, SOUTH, 5)
        JUNGLE_SAVAGE(2887, 2914, 0, SOUTH, 22)
        JUNGLE_WOLF(2887, 2929, 0, SOUTH, 6)
        OOMLIE_BIRD(2888, 2896, 0, SOUTH, 5)
        OOMLIE_BIRD(2890, 2907, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        JUNGLE_WOLF(2893, 2893, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        OOMLIE_BIRD(2898, 2926, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        JUNGLE_SAVAGE(2901, 2907, 0, <PERSON><PERSON><PERSON><PERSON>, 22)
        OOMLIE_BIRD(2904, 2892, 0, SOUTH, 5)
        OOMLIE_BIRD(2910, 2906, 0, SOUTH, 5)
        SNAKE_2845(2910, 2919, 0, SOUTH, 4)
        JUNGLE_WOLF(2911, 2894, 0, <PERSON>OUTH, 6)
        SNAKE_2845(2912, 2916, 0, SOUTH, 4)
        SNAKE_2845(2913, 2913, 0, SOUTH, 4)
        JUNGLE_WOLF(2915, 2906, 0, SOUTH, 6)
        JUNGLE_SAVAGE(2916, 2914, 0, SOUTH, 22)
        OOMLIE_BIRD(2918, 2896, 0, SOUTH, 5)
        JUNGLE_WOLF(2918, 2927, 0, SOUTH, 6)
        JUNGLE_WOLF(2919, 2907, 0, SOUTH, 6)
        OOMLIE_BIRD(2921, 2924, 0, SOUTH, 5)
        JUNGLE_SAVAGE(2925, 2923, 0, SOUTH, 22)
        JUNGLE_SAVAGE(2930, 2903, 0, SOUTH, 22)
        OOMLIE_BIRD(2936, 2910, 0, SOUTH, 5)
        OOMLIE_BIRD(2937, 2893, 0, SOUTH, 5)
        JUNGLE_SAVAGE(2939, 2914, 0, SOUTH, 22)
        JUNGLE_WOLF(2941, 2887, 0, SOUTH, 6)
    }
}