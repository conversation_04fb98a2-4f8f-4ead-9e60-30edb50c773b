package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11566Spawns : NPCSpawnsScript() {
    init {
        8695(2883, 2951, 0, SOUTH, 0)
        JUNGLE_SPIDER(2883, 2996, 0, SOUTH, 4)
        JUNGLE_SPIDER(2884, 3005, 0, SOUTH, 4)
        JUNGLE_SPIDER(2887, 3006, 0, SOUTH, 4)
        JUNGLE_SPIDER(2888, 2946, 0, SOUTH, 4)
        JUNGLE_SPIDER(2888, 2998, 0, SOUTH, 4)
        JUNGLE_SPIDER(2890, 3007, 0, SO<PERSON><PERSON>, 4)
        JUNGLE_SPIDER(2891, 2994, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        JUNG<PERSON>_SPIDER(2892, 2946, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        JUNGLE_SPIDER(2892, 2997, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        JUNGLE_SPIDER(2892, 3000, 0, <PERSON><PERSON>UTH, 4)
        JUNG<PERSON>_SPIDER(2893, 2948, 0, SOUTH, 4)
        JUNGLE_SPIDER(2894, 3006, 0, SOUTH, 4)
        JOGRE(2896, 2945, 0, SOUTH, 3)
        JUNGLE_SPIDER(2896, 3000, 0, SOUTH, 4)
        JOGRE(2903, 2945, 0, SOUTH, 3)
        JOGRE(2918, 2963, 0, SOUTH, 3)
        TRIBESMAN(2918, 2987, 0, SOUTH, 5)
        JOGRE(2921, 2959, 0, SOUTH, 3)
        JOGRE(2923, 2985, 0, SOUTH, 3)
        SNAKE_2845(2924, 2979, 0, SOUTH, 4)
        JOGRE(2925, 2956, 0, SOUTH, 3)
        SNAKE_2845(2927, 2977, 0, SOUTH, 4)
        SNAKE_2845(2928, 2973, 0, SOUTH, 4)
        JOGRE(2931, 2987, 0, SOUTH, 3)
        PENWIE(2932, 2972, 0, SOUTH, 10)
        SNAKE_2845(2933, 2981, 0, SOUTH, 4)
        JUNGLE_FORESTER_3955(2935, 2945, 0, SOUTH, 2)
        SNAKE_2845(2935, 2973, 0, SOUTH, 4)
        SNAKE_2845(2936, 2986, 0, SOUTH, 4)
        SNAKE_2845(2937, 2978, 0, SOUTH, 4)
        TRIBESMAN(2937, 2993, 0, SOUTH, 5)
        TRIBESMAN(2938, 2995, 0, SOUTH, 5)
        SNAKE_2845(2939, 2985, 0, SOUTH, 4)
        TRIBESMAN(2940, 2991, 0, SOUTH, 5)
        TRIBESMAN(2942, 2995, 0, SOUTH, 5)
    }
}