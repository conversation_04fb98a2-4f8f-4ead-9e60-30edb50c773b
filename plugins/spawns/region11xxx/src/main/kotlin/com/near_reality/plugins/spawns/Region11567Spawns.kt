package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11567Spawns : NPCSpawnsScript() {
    init {
        MONKEY_2848(2909, 3023, 0, SOUTH, 10)
        MONKEY_2848(2912, 3019, 0, SOUTH, 10)
        JUNGLE_SPIDER(2914, 3046, 0, SOUTH, 4)
        JUNGLE_SPIDER(2915, 3048, 0, SOUTH, 4)
        JUNGLE_SPIDER(2916, 3043, 0, SOUTH, 4)
        JOGRE(2916, 3055, 0, SOUTH, 3)
        JOGRE(2916, 3062, 0, SOUTH, 3)
        6091(2918, 3057, 0, EAST, 0)
        JOGRE(2919, 3052, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        MONKEY_2848(2920, 3027, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        JUNGLE_SPIDER(2920, 3046, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MONKEY_2848(2921, 3024, 0, <PERSON><PERSON><PERSON>H, 10)
        JUNG<PERSON>_SPIDER(2921, 3040, 0, SOUTH, 4)
        MONKEY_2848(2922, 3018, 0, SOUTH, 10)
        MONKEY_2848(2923, 3027, 0, SOUTH, 10)
        JUNGLE_SPIDER(2923, 3046, 0, SOUTH, 4)
        MONKEY_2848(2924, 3018, 0, SOUTH, 10)
        MONKEY_2848(2924, 3022, 0, SOUTH, 10)
        JOGRE(2924, 3060, 0, SOUTH, 3)
        JUNGLE_SPIDER(2927, 3043, 0, SOUTH, 4)
        JOGRE(2927, 3056, 0, SOUTH, 3)
        MONKEY_2848(2928, 3018, 0, SOUTH, 10)
        MONKEY_2848(2928, 3022, 0, SOUTH, 10)
        JOGRE(2928, 3058, 0, SOUTH, 3)
        JUNGLE_SPIDER(2929, 3047, 0, SOUTH, 4)
        JUNGLE_SPIDER(2930, 3044, 0, SOUTH, 4)
        JUNGLE_SPIDER(2931, 3032, 0, SOUTH, 4)
        JUNGLE_SPIDER(2932, 3032, 0, SOUTH, 4)
        JOGRE(2932, 3043, 0, SOUTH, 3)
        JUNGLE_SPIDER(2932, 3054, 0, SOUTH, 4)
        JUNGLE_SPIDER(2932, 3057, 0, SOUTH, 4)
        JUNGLE_SPIDER(2933, 3048, 0, SOUTH, 4)
        JOGRE(2935, 3046, 0, SOUTH, 3)
    }
}