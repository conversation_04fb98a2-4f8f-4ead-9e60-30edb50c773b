package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11568Spawns : NPCSpawnsScript() {
    init {
        HARPIE_BUG_SWARM(2880, 3112, 0, SOUTH, 6)
        HARPIE_BUG_SWARM(2882, 3115, 0, SOUTH, 6)
        HARPIE_BUG_SWARM(2886, 3116, 0, SOUTH, 6)
        HARPIE_BUG_SWARM(2889, 3113, 0, SOUTH, 6)
        MONKEY_2848(2893, 3078, 0, SOUTH, 10)
        MONKEY_2848(2897, 3081, 0, SOUTH, 10)
        FISHING_SPOT_4712(2899, 3119, 0, <PERSON>OUTH, 0)
        MONKEY_2848(2900, 3080, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        MONKEY_2848(2902, 3073, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        MONKEY_2848(2906, 3084, 0, <PERSON><PERSON><PERSON>H, 10)
        THE_SHAI<PERSON><PERSON>N(2906, 3095, 0, SOUTH, 3)
        MONKEY_2848(2908, 3080, 0, SOUTH, 10)
        MONKEY_2848(2909, 3077, 0, SOUTH, 10)
        6235(2912, 3118, 0, SOUTH, 3)
        FISHING_SPOT_4713(2912, 3119, 0, SOUTH, 0)
        PELICAN_288(2914, 3106, 0, SOUTH, 4)
        CORMORANT_287(2931, 3108, 0, SOUTH, 2)
        PELICAN_288(2934, 3105, 0, SOUTH, 4)
        GULL_286(2937, 3104, 0, SOUTH, 3)
    }
}