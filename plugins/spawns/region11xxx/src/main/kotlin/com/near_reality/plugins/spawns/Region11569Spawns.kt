package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11569Spawns : NPCSpawnsScript() {
    init {
        SNAKE_2845(2881, 3157, 0, SOUTH, 4)
        SNAKE_2845(2889, 3146, 0, SOUTH, 4)
        MAN_3110(2889, 3173, 0, SOUTH, 2)
        SHOP_KEEPER_2825(2902, 3146, 0, SOUTH, 2)
        SHOP_ASSISTANT_2826(2902, 3150, 0, SOUTH, 2)
        6543(2904, 3178, 0, SOUTH, 2)
        6545(2906, 3172, 0, SOUTH, 3)
        6544(2907, 3174, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        7325(2920, 3143, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        FISHING_SPOT_1521(2923, 3179, 0, <PERSON>OUT<PERSON>, 0)
        FISHING_SPOT_1522(2923, 3180, 0, SOUTH, 0)
        FISHING_SPOT_1521(2924, 3181, 0, SOUTH, 0)
        ZAMBO(2925, 3143, 0, SOUTH, 2)
        FISHING_SPOT_1522(2925, 3181, 0, SOUTH, 0)
        FISHING_SPOT_1522(2926, 3179, 0, SOUTH, 0)
        FISHING_SPOT_1521(2926, 3180, 0, SOUTH, 0)
        LUTHAS(2939, 3154, 0, SOUTH, 3)
    }
}