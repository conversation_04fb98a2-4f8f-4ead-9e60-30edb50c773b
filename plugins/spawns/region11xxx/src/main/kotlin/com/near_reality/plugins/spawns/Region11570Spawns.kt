package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11570Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(2923, 3251, 0, SOUTH, 14)
        ZOMBIE_RAT_3971(2925, 3256, 0, SOUTH, 2)
        DA_VINCI(2927, 3218, 0, SOUTH, 2)
        RAT_2854(2927, 3251, 0, SOUTH, 14)
        ZOMBIE_RAT_3970(2928, 3244, 0, SOUTH, 3)
        CHANCY(2929, 3222, 0, EAST, 0)
        HOPS(2930, 3218, 0, SOUTH, 0)
        RAT_2854(2930, 3243, 0, SOUTH, 14)
        ZOMBIE_RAT(2930, 3252, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ZOMBIE_RAT_3971(2931, 3249, 0, <PERSON>OUTH, 2)
        ZOMBIE_RAT_3971(2933, 3244, 0, SOUTH, 2)
        RAT_2854(2933, 3250, 0, SOUTH, 14)
        CHEMIST(2934, 3210, 0, SOUTH, 2)
        ZOMBIE_RAT_3970(2934, 3254, 0, SOUTH, 3)
        ZOMBIE_RAT_3971(2935, 3248, 0, SOUTH, 2)
        TOOL_LEPRECHAUN(2939, 3219, 0, SOUTH, 0)
        RAT_2854(2939, 3242, 0, SOUTH, 14)
        TARIA(2940, 3223, 0, SOUTH, 0)
        IMP_5007(2941, 3236, 0, SOUTH, 100)
        1064(2910, 3226, 0, SOUTH, 5)
        CAPTAIN_BARNABY_8763(2916, 3225, 0, SOUTH, 5)
        GHOST_3979(2925, 3255, 1, SOUTH, 5)
        GHOST_3976(2926, 3254, 1, SOUTH, 5)
        GHOST_3977(2927, 3251, 1, SOUTH, 5)
        GHOST_3975(2929, 3249, 1, SOUTH, 5)
        GHOST_3978(2930, 3253, 1, SOUTH, 5)
        RAT_2854(2932, 3242, 1, SOUTH, 14)
        RAT_2854(2934, 3240, 1, SOUTH, 14)
        RAT_2854(2923, 3245, 2, SOUTH, 14)
        SKELETON_3972(2923, 3251, 2, SOUTH, 5)
        RAT_2854(2924, 3240, 2, SOUTH, 14)
        SKELETON_3973(2926, 3255, 2, SOUTH, 5)
        SKELETON_3974(2928, 3253, 2, SOUTH, 5)
        SKELETON_3973(2929, 3251, 2, SOUTH, 5)
        SKELETON_3973(2930, 3254, 2, SOUTH, 5)
        SKELETON_3974(2933, 3256, 2, SOUTH, 5)
    }
}