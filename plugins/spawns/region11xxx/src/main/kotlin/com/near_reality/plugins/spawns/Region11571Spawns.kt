package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11571Spawns : NPCSpawnsScript() {
    init {
        HOBGOBLIN_3286(2905, 3295, 0, SOUTH, 10)
        HOBGOBLIN_3286(2906, 3288, 0, SOUTH, 10)
        HOBGOBLIN_3288(2908, 3291, 0, SOUTH, 5)
        HOBGOBLIN_3288(2909, 3282, 0, SOUTH, 5)
        HOBGOBLIN_3287(2909, 3285, 0, SOUTH, 8)
        HOBGOBLIN_3289(2909, 3294, 0, SOUT<PERSON>, 7)
        HOBGOBLIN_3286(2912, 3279, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        HOB<PERSON><PERSON>BLIN_3287(2915, 3273, 0, <PERSON>OUT<PERSON>, 8)
        MAKEOVER_MAGE_1307(2916, 3323, 0, SOUTH, 5)
        HOBGOBLIN_3288(2917, 3269, 0, SO<PERSON>H, 5)
        COW(2917, 3289, 0, SOUTH, 3)
        FROG_3290(2918, 3319, 0, SOUTH, 2)
        HOBGOBLIN_3286(2920, 3266, 0, SOUTH, 10)
        COW_2793(2920, 3285, 0, SOUTH, 7)
        COW_CALF_2801(2920, 3288, 0, SOUTH, 4)
        1172(2920, 3291, 0, SOUTH, 0)
        SHEEP_2787(2920, 3318, 0, SOUTH, 3)
        SHEEP_2699(2920, 3320, 0, SOUTH, 2)
        COW_2791(2921, 3287, 0, SOUTH, 4)
        SHEEP_2693(2921, 3319, 0, SOUTH, 3)
        SHEEP_2699(2922, 3320, 0, SOUTH, 2)
        1172(2923, 3285, 0, SOUTH, 0)
        SHEEP_2693(2923, 3325, 0, SOUTH, 3)
        COW_2793(2924, 3277, 0, SOUTH, 7)
        COW_2793(2924, 3288, 0, SOUTH, 7)
        RAM_1262(2924, 3318, 0, SOUTH, 5)
        RAM_1262(2924, 3325, 0, SOUTH, 5)
        COW(2925, 3282, 0, SOUTH, 3)
        4731(2925, 3303, 0, SOUTH, 2)
        SHEEP_2787(2925, 3324, 0, SOUTH, 3)
        COW(2926, 3271, 0, SOUTH, 3)
        SHEEP_2693(2926, 3322, 0, SOUTH, 3)
        LIL_LAMB(2927, 3320, 0, SOUTH, 5)
        COW_2793(2930, 3269, 0, SOUTH, 7)
        MASTER_CRAFTER_5811(2931, 3280, 0, EAST, 0)
        MASTER_CRAFTER(2932, 3287, 0, SOUTH, 0)
        COW_2791(2933, 3274, 0, SOUTH, 4)
        COW_CALF_2801(2933, 3276, 0, SOUTH, 4)
        COW(2936, 3274, 0, SOUTH, 3)
        MASTER_CRAFTER_5812(2937, 3290, 0, SOUTH, 0)
        TANNER(2934, 3284, 1, SOUTH, 3)
    }
}