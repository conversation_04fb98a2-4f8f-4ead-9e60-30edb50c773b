package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11572Spawns : NPCSpawnsScript() {
    init {
        DARK_WIZARD_2056(2905, 3335, 0, SOUTH, 4)
        DARK_WIZARD_2058(2906, 3334, 0, SOUTH, 3)
        DARK_WIZARD_2057(2907, 3337, 0, SOUTH, 2)
        DARK_WIZARD_2059(2908, 3333, 0, SOUTH, 4)
        DARK_WIZARD_2056(2909, 3331, 0, SOUTH, 4)
        DARK_WIZARD_2057(2909, 3334, 0, SOUTH, 2)
        DARK_WIZARD_2056(2910, 3337, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        DARK_WIZARD_2056(2906, 3333, 1, <PERSON>O<PERSON><PERSON>, 4)
        DARK_WIZARD_2058(2906, 3336, 1, SOUTH, 3)
        DARK_WIZARD_2059(2909, 3333, 1, SOUTH, 4)
        DARK_WIZARD_2057(2909, 3336, 1, SOUTH, 2)
        ZANDAR_HORFYRE(2905, 3334, 2, SOUTH, 2)
        DARK_WIZARD_2056(2906, 3336, 2, SOUTH, 4)
        DARK_WIZARD_2058(2908, 3332, 2, SOUTH, 3)
        DARK_WIZARD_2057(2909, 3334, 2, SOUTH, 2)
        DARK_WIZARD_2059(2910, 3336, 2, SOUTH, 4)
    }
}