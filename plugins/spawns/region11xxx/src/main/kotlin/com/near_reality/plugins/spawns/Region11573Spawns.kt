package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11573Spawns : NPCSpawnsScript() {
    init {
        DRUID(2883, 3431, 0, SOUTH, 13)
        DRUID(2884, 3434, 0, SOUTH, 13)
        DRUID(2885, 3418, 0, SOUTH, 13)
        GAIUS(2885, 3449, 0, SOUTH, 2)
        DRUID(2888, 3440, 0, SOUTH, 13)
        DRUID(2889, 3448, 0, SOUTH, 13)
        DRUID(2890, 3435, 0, SOUTH, 13)
        SYLAS(2891, 3454, 0, SOUTH, 2)
        DRUID(2893, 3419, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DRUID(2893, 3440, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DRUID(2896, 3442, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DRUID(2897, 3429, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DRUID(2897, 3436, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DRUID(2898, 3419, 0, SOUTH, 13)
        J<PERSON><PERSON>(2899, 3427, 0, SOUTH, 2)
        DRUID(2900, 3449, 0, SOUTH, 13)
        DRUID(2902, 3449, 0, SOUTH, 13)
        DRUID(2906, 3441, 0, <PERSON>OUTH, 13)
        TEGID(2913, 3417, 0, SOUTH, 0)
        DRUID(2913, 3450, 0, SOUTH, 13)
        DRUID(2915, 3446, 0, SOUTH, 13)
        THE_LADY_OF_THE_LAKE(2924, 3405, 0, SOUTH, 2)
        ALAIN(2933, 3438, 0, SOUTH, 3)
        4728(2938, 3422, 0, SOUTH, 2)
        TOOL_LEPRECHAUN(2941, 3432, 0, SOUTH, 0)
        SANFEW(2897, 3426, 1, SOUTH, 5)
        DRUID(2898, 3432, 1, SOUTH, 13)
        DRUID(2906, 3451, 1, SOUTH, 13)
    }
}