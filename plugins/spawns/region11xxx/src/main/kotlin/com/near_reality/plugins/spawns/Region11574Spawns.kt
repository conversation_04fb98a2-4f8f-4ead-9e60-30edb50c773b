package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11574Spawns : NPCSpawnsScript() {
    init {
        ACHIETTIES(2903, 3510, 0, SOUTH, 3)
        NORA_T_HAGG(2904, 3463, 0, SOUTH, 13)
        DRUID(2921, 3485, 0, SOUTH, 13)
        WHITE_KNIGHT_4114(2921, 3514, 0, SOUTH, 3)
        DRUID(2923, 3478, 0, SOUTH, 13)
        KAQEMEEX(2925, 3486, 0, SOUTH, 2)
        DRUID(2927, 3481, 0, SOUTH, 13)
        DRUID(2927, 3489, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        BOY(2928, 3456, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        DRUID(2930, 3484, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        MON<PERSON>_OF_ZAMORAK_8401(2931, 3516, 0, <PERSON>OUT<PERSON>, 4)
        MONK_OF_ZAMORAK_8401(2933, 3514, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MONK_OF_ZAMORAK_8400(2934, 3515, 0, SOUTH, 3)
        MONK_OF_ZAMORAK_8400(2934, 3516, 0, SOUTH, 3)
        MONK_OF_ZAMORA<PERSON>_8401(2936, 3515, 0, SOUTH, 4)
        MONK_OF_<PERSON>AMORAK_8401(2940, 3517, 0, SOUTH, 4)
        HELEMOS(2894, 3510, 1, SOUTH, 7)
        MONK_OF_ZAMORAK_8400(2938, 3516, 1, SOUTH, 3)
    }
}