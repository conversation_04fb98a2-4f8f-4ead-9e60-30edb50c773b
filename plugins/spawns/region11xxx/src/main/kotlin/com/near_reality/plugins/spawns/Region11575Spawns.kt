package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11575Spawns : NPCSpawnsScript() {
    init {
        SOLDIER_4088(2885, 3528, 0, SOUTH, 5)
        SOLDIER_4088(2885, 3533, 0, SOUTH, 5)
        SOLDIER(2891, 3537, 0, NORTH, 0)
        SOLDIER(2891, 3539, 0, NORTH, 0)
        SOLDIER_4089(2892, 3532, 0, EAST, 0)
        SOLDIER(2892, 3537, 0, NORTH, 0)
        SOLDIER(2892, 3539, 0, NORTH, 0)
        SOLDIER_4090(2893, 3533, 0, SOUTH, 0)
        SOLDIER(2893, 3537, 0, NORTH, 0)
        SOLDIER(2893, 3539, 0, <PERSON>RTH, 0)
        SERGEANT(2893, 3541, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        EADBURG(2893, 3558, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        SOLDIER_4091(2894, 3532, 0, WEST, 0)
        SOLDIER(2894, 3537, 0, NORTH, 0)
        SOLDIER(2894, 3539, 0, NORTH, 0)
        SOLDIER(2895, 3537, 0, NORTH, 0)
        SOLDIER(2895, 3539, 0, NORTH, 0)
        DENULTH(2896, 3528, 0, SOUTH, 2)
        GUARD_4099(2896, 3566, 0, SOUTH, 2)
        GUARD_4099(2897, 3556, 0, SOUTH, 2)
        SOLDIER_4087(2900, 3531, 0, EAST, 0)
        SERGEANT_4085(2900, 3532, 0, SOUTH, 2)
        SOLDIER_4087(2900, 3533, 0, EAST, 0)
        SOLDIER_4088(2900, 3539, 0, SOUTH, 5)
        GUARD_4100(2900, 3556, 0, SOUTH, 2)
        SERVANT_4104(2901, 3564, 0, SOUTH, 4)
        TOSTIG(2906, 3537, 0, SOUTH, 2)
        SOLDIER_4088(2907, 3542, 0, SOUTH, 5)
        PENDA(2910, 3539, 0, SOUTH, 3)
        SOLDIER_4088(2911, 3542, 0, SOUTH, 5)
        CEOLBURG(2916, 3523, 0, SOUTH, 3)
        BERNALD(2918, 3534, 0, SOUTH, 3)
        6202(2918, 3558, 0, SOUTH, 2)
        BREOCA(2919, 3550, 0, SOUTH, 3)
        DUNSTAN(2919, 3574, 0, SOUTH, 2)
        6201(2923, 3566, 0, SOUTH, 0)
        4113(2924, 3565, 0, SOUTH, 2)
        HYGD(2926, 3536, 0, SOUTH, 3)
        OCGA(2927, 3559, 0, SOUTH, 3)
        WISTAN(2928, 3546, 0, SOUTH, 3)
        8141(2930, 3557, 0, SOUTH, 0)
        HILD_4112(2930, 3566, 0, SOUTH, 2)
        TURAEL(2931, 3536, 0, SOUTH, 2)
        ARCHER_4096(2892, 3558, 1, SOUTH, 2)
        ARCHER_4097(2893, 3559, 1, SOUTH, 2)
        ARCHER_4098(2893, 3569, 1, SOUTH, 5)
        ARCHER_4096(2897, 3549, 0, SOUTH, 2)
        ARCHER_4097(2900, 3549, 0, SOUTH, 2)
        EOHRIC(2902, 3565, 1, SOUTH, 5)
        ARCHER_4096(2903, 3559, 1, SOUTH, 2)
        ARCHER_4097(2904, 3568, 1, SOUTH, 2)
        ARCHER_4096(2904, 3570, 1, SOUTH, 2)
        HAROLD(2905, 3539, 1, SOUTH, 5)
        ARCHER_4097(2905, 3557, 1, SOUTH, 2)
    }
}