package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11576Spawns : NPCSpawnsScript() {
    init {
        TROLL_SPECTATOR_4128(2902, 3604, 0, SOUTH, 0)
        MOUNTAIN_TROLL_4143(2904, 3642, 0, SOUTH, 9)
        TROLL_SPECTATOR(2906, 3623, 0, SOUTH, 0)
        MOUNTAIN_TROLL_939(2909, 3641, 0, SOUTH, 9)
        DAD(2911, 3612, 0, SOUTH, 5)
        TROLL_SPECTATOR_4127(2916, 3601, 0, SOUTH, 0)
        MOUNTAIN_TROLL(2916, 3631, 0, SOUTH, 8)
        MOUNTAIN_TROLL_937(2917, 3634, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        TROLL_SPECTATOR_4124(2920, 3622, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        TROLL_SPECTATOR_4129(2922, 3606, 0, <PERSON><PERSON><PERSON>H, 0)
        T<PERSON>LL_SPECTATOR_4126(2923, 3612, 0, SOUTH, 0)
        TROLL_SPECTATOR_4125(2923, 3616, 0, SOUTH, 0)
    }
}