package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11577Spawns : NPCSpawnsScript() {
    init {
        MOUNTAIN_GOAT_4146(2885, 3670, 0, SOUTH, 2)
        THROWER_TROLL_4139(2886, 3698, 0, NORTH, 0)
        MOUNTAIN_GOAT_4145(2888, 3675, 0, SOUTH, 2)
        THROWER_TROLL_4138(2889, 3699, 0, NORTH, 0)
        THISTLE(2891, 3676, 0, SOUTH, 0)
        MOUNTAIN_GOAT_4145(2892, 3685, 0, SOUTH, 2)
        MOUNTAIN_GOAT_4146(2893, 3678, 0, <PERSON>OUTH, 2)
        THROWER_TROLL_4137(2893, 3700, 0, NORTH, 0)
        THROWER_TROLL_4136(2898, 3698, 0, NORTH, 0)
        MOUNTAIN_GOAT_4145(2900, 3671, 0, SOUTH, 2)
        THROWER_TROLL_4135(2902, 3696, 0, NORTH, 0)
    }
}