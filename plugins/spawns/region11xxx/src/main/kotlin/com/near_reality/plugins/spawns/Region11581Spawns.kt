package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11581Spawns : NPCSpawnsScript() {
    init {
        ICICLE(2881, 3931, 0, SOUTH, 5)
        SQUIRREL_8422(2882, 3917, 0, SOUTH, 8)
        ROOT(2884, 3939, 0, SOUTH, 3)
        SQUIRREL_8422(2884, 3955, 0, SOUTH, 8)
        ICEBERG(2884, 3966, 0, SOUTH, 0)
        MOUNTAIN_GOAT_4145(2885, 3944, 0, SOUTH, 2)
        ICEBERG_5818(2885, 3966, 0, SOUTH, 0)
        MOUNTAIN_GOAT_4145(2886, 3945, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        DRIFTWOOD(2888, 3922, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        SQUIRREL_8422(2893, 3949, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        ICEBERG_5818(2899, 3964, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        ICEBERG(2901, 3963, 0, SOUTH, 0)
        ICEBERG_5818(2905, 3963, 0, SOUTH, 0)
        ICEBERG(2907, 3965, 0, SOUTH, 0)
        SQUIRREL_8422(2917, 3941, 0, SOUTH, 8)
        ICEBERG(2924, 3964, 0, <PERSON>OUTH, 0)
        ICEBERG_5818(2924, 3965, 0, SOUTH, 0)
        ICEBERG(2926, 3965, 0, SOUTH, 0)
        ICEBERG(2938, 3964, 0, SOUTH, 0)
        ICEBERG_5818(2939, 3963, 0, SOUTH, 0)
        ICEBERG(2940, 3954, 0, SOUTH, 0)
        ICEBERG_5818(2941, 3951, 0, SOUTH, 0)
        ICEBERG(2941, 3961, 0, SOUTH, 0)
        ICEBERG(2942, 3944, 0, SOUTH, 0)
        ICEBERG(2943, 3949, 0, SOUTH, 0)
        ICEBERG(2943, 3953, 0, SOUTH, 0)
    }
}