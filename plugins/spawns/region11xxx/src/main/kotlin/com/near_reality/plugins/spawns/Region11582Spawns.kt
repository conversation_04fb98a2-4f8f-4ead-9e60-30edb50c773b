package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11582Spawns : NPCSpawnsScript() {
    init {
        ICEBERG(2881, 3970, 0, SOUTH, 0)
        ICEBERG_5818(2882, 3971, 0, SOUTH, 0)
        ICEBERG(2884, 3982, 0, SOUTH, 0)
        ICEBERG(2886, 3973, 0, SOUTH, 0)
        ICEBERG_5818(2889, 3976, 0, SOUTH, 0)
        ICEBERG_5818(2891, 3969, 0, SOUTH, 0)
        ICEBERG(2891, 3975, 0, SOUTH, 0)
        ICEBERG_5818(2893, 3984, 0, <PERSON>OUTH, 0)
        ICEBERG(2894, 3988, 0, SOUTH, 0)
        ICEBERG(2899, 3969, 0, <PERSON>OUTH, 0)
        ICEBERG_5818(2900, 3970, 0, SOUTH, 0)
        ICEBERG(2901, 3980, 0, SOUTH, 0)
        ICEBERG(2902, 3972, 0, SOUTH, 0)
        ICEBERG_5818(2903, 3981, 0, SOUTH, 0)
        ICEBERG(2904, 3970, 0, SOUTH, 0)
        ICEBERG_5818(2904, 3973, 0, SOUTH, 0)
        ICEBERG_5818(2906, 3996, 0, SOUTH, 0)
        ICEBERG(2910, 3985, 0, SOUTH, 0)
        ICEBERG_5818(2912, 3984, 0, SOUTH, 0)
        ICEBERG(2913, 3982, 0, SOUTH, 0)
        ICEBERG(2915, 3986, 0, SOUTH, 0)
        ICEBERG(2918, 3969, 0, SOUTH, 0)
        ICEBERG(2920, 3976, 0, SOUTH, 0)
        ICEBERG_5818(2921, 3972, 0, SOUTH, 0)
        ICEBERG_5818(2923, 3980, 0, SOUTH, 0)
        ICEBERG(2925, 3978, 0, SOUTH, 0)
        ICEBERG(2927, 3971, 0, SOUTH, 0)
    }
}