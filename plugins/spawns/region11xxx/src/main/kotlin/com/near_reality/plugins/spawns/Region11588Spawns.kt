package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11588Spawns : NPCSpawnsScript() {
    init {
        SUSPICIOUS_WATER_5948(2897, 4377, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2897, 4382, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5948(2897, 4387, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2899, 4374, 0, SOUTH, 0)
        DAGANNOTH_SUPREME(2906, 4384, 0, SOUTH, 3)
        DAGANNOTH_PRIME(2911, 4387, 0, <PERSON>OUT<PERSON>, 3)
        SUSPICIOUS_WATER_5962(2912, 4401, 0, <PERSON><PERSON><PERSON>H, 0)
        DAGANNOTH_REX(2913, 4381, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SUSPICIOUS_WATER_5948(2918, 4369, 0, SOUTH, 0)
        SUSPICIOUS_WATER(2922, 4399, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2925, 4372, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2926, 4395, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5948(2930, 4376, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2930, 4386, 0, SOUTH, 0)
    }
}