package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11589Spawns : NPCSpawnsScript() {
    init {
        SUSPICIOUS_WATER_5948(2897, 4441, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2897, 4446, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2899, 4438, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2901, 4461, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5948(2906, 4433, 0, SOUTH, 0)
        DAGANNOTH_SUPREME(2906, 4448, 0, SOUT<PERSON>, 3)
        SUSPICIOUS_WATER(2908, 4464, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        DAGANNOTH_PRIME(2911, 4451, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SUSPICIOUS_WATER_5962(2912, 4465, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        DAGANNOTH_REX(2913, 4445, 0, SOUTH, 3)
        SUSPICIOUS_WATER_5948(2918, 4433, 0, SOUTH, 0)
        SUSPICIOUS_WATER(2922, 4463, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2925, 4436, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2930, 4446, 0, SOUTH, 0)
        SUSPICIOUS_WATER_5962(2930, 4450, 0, SOUTH, 0)
        SUSPICIOUS_WATER(2930, 4453, 0, SOUTH, 0)
    }
}