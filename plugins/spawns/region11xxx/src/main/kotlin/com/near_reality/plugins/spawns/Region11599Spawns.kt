package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11599Spawns : NPCSpawnsScript() {
    init {
        RAT_4612(2904, 5082, 0, SOUTH, 5)
        RAT_4611(2905, 5084, 0, SOUTH, 5)
        RAT_4610(2905, 5087, 0, SOUTH, 5)
        GAMER_1017(2905, 5094, 0, SOUTH, 5)
        GAMER_1019(2906, 5077, 0, SOUTH, 5)
        RAT_4611(2906, 5083, 0, SOUTH, 5)
        RAT_4610(2906, 5086, 0, SOUTH, 5)
        RAT_4610(2906, 5089, 0, SOUTH, 5)
        RAT_4612(2907, 5081, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RAT_4612(2907, 5084, 0, SOUTH, 5)
        RAT_4612(2907, 5090, 0, SOUTH, 5)
        GAMER_1018(2908, 5078, 0, SOUTH, 5)
        RAT_4611(2908, 5085, 0, SOUTH, 5)
        RAT_4610(2908, 5087, 0, SOUTH, 5)
        BARMAN(2909, 5076, 0, SOUTH, 5)
        RAT_4610(2909, 5083, 0, SOUTH, 5)
        RAT_4610(2909, 5089, 0, SOUTH, 5)
        GAMER(2909, 5098, 0, SOUTH, 5)
        RAT_4612(2910, 5081, 0, SOUTH, 5)
        RAT_4611(2910, 5085, 0, SOUTH, 5)
        RAT_4611(2910, 5090, 0, SOUTH, 5)
        GERTRUDES_CAT(2910, 5100, 0, SOUTH, 5)
        RAT_4610(2911, 5082, 0, SOUTH, 5)
        RAT_4611(2911, 5083, 0, SOUTH, 5)
        RAT_4611(2911, 5086, 0, SOUTH, 5)
        RAT_4612(2911, 5088, 0, SOUTH, 5)
        RAT_4611(2911, 5103, 0, SOUTH, 5)
        RAT_4612(2912, 5085, 0, SOUTH, 5)
        RAT_4612(2912, 5102, 0, SOUTH, 5)
        RAT_4612(2912, 5105, 0, SOUTH, 5)
        RAT_4610(2913, 5103, 0, SOUTH, 5)
        GAMER_1016(2914, 5099, 0, SOUTH, 5)
        RAT_4610(2914, 5102, 0, SOUTH, 5)
        TREACLE(2914, 5104, 0, SOUTH, 5)
        RAT_4610(2914, 5105, 0, SOUTH, 5)
        GAMER_1014(2915, 5088, 0, SOUTH, 5)
        RAT_4612(2915, 5103, 0, SOUTH, 5)
        MITTENS(2916, 5104, 0, SOUTH, 5)
        RAT_4611(2917, 5102, 0, SOUTH, 5)
        RAT_4611(2917, 5105, 0, SOUTH, 5)
    }
}