package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11603Spawns : NPCSpawnsScript() {
    init {
        FERAL_VAMPYRE(2880, 5318, 2, SOUTH, 8)
        WEREWOLF_3135(2880, 5321, 2, SOUTH, 7)
        FERAL_VAMPYRE(2880, 5332, 2, SOUTH, 8)
        HELLHOUND_3133(2881, 5324, 2, SOUTH, 6)
        GORAK_3141(2881, 5329, 2, <PERSON><PERSON>UTH, 6)
        BLOODVELD_3138(2883, 5349, 2, <PERSON><PERSON>UT<PERSON>, 7)
        SPIRITUAL_MAGE_3161(2883, 5356, 2, <PERSON><PERSON><PERSON><PERSON>, 8)
        BLOODVELD_3138(2884, 5325, 2, <PERSON><PERSON><PERSON><PERSON>, 7)
        WEREWOLF_3136(2884, 5354, 2, <PERSON><PERSON><PERSON><PERSON>, 7)
        FERAL_VAMPYRE(2885, 5312, 2, <PERSON><PERSON><PERSON><PERSON>, 8)
        BLOODVELD_3138(2885, 5315, 2, SO<PERSON>H, 7)
        IMP(2886, 5313, 2, SOUTH, 5)
        WEREWOLF_3135(2886, 5323, 2, SOUTH, 7)
        GORAK_3141(2886, 5351, 2, SOUTH, 6)
        BLOODVELD_3138(2886, 5359, 2, <PERSON>OUTH, 7)
        ICE<PERSON>END(2887, 5312, 2, SOUTH, 6)
        SPIRITUAL_MAGE_3161(2887, 5357, 2, SOUTH, 8)
        GORAK_3141(2888, 5313, 2, SOUTH, 6)
        FERAL_VAMPYRE(2888, 5320, 2, SOUTH, 8)
        SPIRITUAL_MAGE_3161(2888, 5363, 2, SOUTH, 8)
        BLOODVELD_3138(2889, 5322, 2, SOUTH, 7)
        HELLHOUND_3133(2889, 5329, 2, SOUTH, 6)
        SPIRITUAL_WARRIOR_3159(2890, 5356, 2, SOUTH, 7)
        WEREWOLF_3136(2891, 5361, 2, SOUTH, 7)
        WEREWOLF_3135(2892, 5320, 2, SOUTH, 7)
        FERAL_VAMPYRE(2894, 5325, 2, SOUTH, 8)
        BLOODVELD_3138(2894, 5350, 2, SOUTH, 7)
        BLOODVELD_3138(2894, 5362, 2, SOUTH, 7)
        WEREWOLF_3135(2896, 5312, 2, SOUTH, 7)
        WEREWOLF_3136(2897, 5352, 2, SOUTH, 7)
        FERAL_VAMPYRE(2898, 5312, 2, SOUTH, 8)
        IMP(2898, 5313, 2, SOUTH, 5)
        ICEFIEND(2898, 5315, 2, SOUTH, 6)
        WEREWOLF_3136(2900, 5355, 2, SOUTH, 7)
        IMP(2902, 5357, 2, SOUTH, 5)
        SPIRITUAL_MAGE_3161(2902, 5361, 2, SOUTH, 8)
        GORAK_3141(2903, 5348, 2, SOUTH, 6)
        SPIRITUAL_WARRIOR_3159(2903, 5351, 2, SOUTH, 7)
        GORAK_3141(2903, 5363, 2, SOUTH, 6)
        SPIRITUAL_MAGE_3161(2905, 5355, 2, SOUTH, 8)
        GORAK_3141(2906, 5346, 2, SOUTH, 6)
        IMP(2907, 5348, 2, SOUTH, 5)
        WEREWOLF_3136(2910, 5342, 2, SOUTH, 7)
        SPIRITUAL_WARRIOR_3159(2912, 5338, 2, SOUTH, 7)
        WEREWOLF_3136(2912, 5353, 2, SOUTH, 7)
        SPIRITUAL_WARRIOR_3159(2913, 5357, 2, SOUTH, 7)
        WEREWOLF_3136(2914, 5335, 2, SOUTH, 7)
        HELLHOUND_3133(2915, 5345, 2, SOUTH, 6)
        WEREWOLF_3136(2916, 5342, 2, SOUTH, 7)
        WEREWOLF_3136(2916, 5354, 2, SOUTH, 7)
        SPIRITUAL_MAGE_3161(2917, 5339, 2, SOUTH, 8)
        SPIRITUAL_WARRIOR_3159(2918, 5337, 2, SOUTH, 7)
        SPIRITUAL_WARRIOR_3159(2918, 5344, 2, SOUTH, 7)
        SPIRITUAL_WARRIOR_3159(2919, 5342, 2, SOUTH, 7)
        SPIRITUAL_MAGE_3161(2919, 5360, 2, SOUTH, 8)
        SPIRITUAL_MAGE_3161(2920, 5338, 2, SOUTH, 8)
        WEREWOLF_3136(2920, 5347, 2, SOUTH, 7)
        IMP(2920, 5356, 2, SOUTH, 5)
        WEREWOLF_3136(2920, 5358, 2, SOUTH, 7)
        SPIRITUAL_WARRIOR_3159(2922, 5345, 2, SOUTH, 7)
        GORAK_3141(2922, 5353, 2, SOUTH, 6)
        BLOODVELD_3138(2922, 5358, 2, SOUTH, 7)
        SPIRITUAL_MAGE_3161(2924, 5340, 2, SOUTH, 8)
        KRIL_TSUTSAROTH(2925, 5322, 2, SOUTH, 2)
        IMP(2925, 5352, 2, SOUTH, 5)
        SPIRITUAL_WARRIOR_3159(2926, 5341, 2, SOUTH, 7)
        SPIRITUAL_WARRIOR_3159(2927, 5339, 2, SOUTH, 7)
        SPIRITUAL_WARRIOR_3159(2927, 5345, 2, SOUTH, 7)
        WEREWOLF_3136(2927, 5354, 2, SOUTH, 7)
        BLOODVELD_3138(2928, 5356, 2, SOUTH, 7)
        SPIRITUAL_MAGE_3161(2929, 5347, 2, SOUTH, 8)
        //GORAK_3141(2930, 5345, 2, SOUTH, 6)
        //WEREWOLF_3136(2931, 5357, 2, SOUTH, 7)
        //WEREWOLF_3136(2933, 5347, 2, SOUTH, 7)
        //BLOODVELD_3138(2935, 5353, 2, SOUTH, 7)
        //SPIRITUAL_MAGE_3161(2936, 5348, 2, SOUTH, 8)
        //WEREWOLF_3136(2936, 5350, 2, SOUTH, 7)
        //SPIRITUAL_RANGER_3160(2937, 5351, 2, SOUTH, 7)
        KNIGHT_16023(2932, 5354, 2, EAST, 0)
    }
}