package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11605Spawns : NPCSpawnsScript() {
    init {
        WINTER_ELEMENTAL_5797(2891, 5470, 0, SOUTH, 5)
        WINTER_ELEMENTAL_5800(2891, 5483, 0, SOUTH, 5)
        WINTER_ELEMENTAL_5798(2897, 5478, 0, SOUTH, 5)
        AUTUMN_ELEMENTAL(2898, 5460, 0, SOUTH, 5)
        AUTUMN_ELEMENTAL_5804(2899, 5449, 0, SOUTH, 5)
        WINTER_ELEMENTAL(2899, 5466, 0, <PERSON>OUT<PERSON>, 5)
        AUTUMN_ELEMENTAL_5803(2900, 5455, 0, SOUTH, 5)
        WINTER_ELEMENTAL_5799(2900, 5480, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        WINTER_ELEMENTAL_5801(2900, 5485, 0, SOUTH, 5)
        AUTUMN_ELEMENTAL_5805(2903, 5451, 0, SOUTH, 5)
        AUTUMN_ELEMENTAL_5806(2904, 5457, 0, SOUTH, 5)
        SUMMER_ELEMENTAL(2907, 5488, 0, SOUTH, 5)
        SUMMER_ELEMENTAL_1802(2907, 5495, 0, SOUTH, 5)
        DELMONTY(2909, 5472, 0, SOUTH, 5)
        SUMMER_ELEMENTAL_1803(2910, 5493, 0, SOUTH, 5)
        SUMMER_ELEMENTAL_1804(2912, 5485, 0, SOUTH, 5)
        AUTUMN_ELEMENTAL_5807(2917, 5455, 0, SOUTH, 5)
        SUMMER_ELEMENTAL_1806(2921, 5495, 0, SOUTH, 5)
        SPRING_ELEMENTAL(2922, 5471, 0, SOUTH, 5)
        SUMMER_ELEMENTAL_1805(2923, 5490, 0, SOUTH, 5)
        SPRING_ELEMENTAL_2957(2924, 5463, 0, SOUTH, 5)
        SPRING_ELEMENTAL_2962(2925, 5475, 0, SOUTH, 5)
        SPRING_ELEMENTAL_2958(2926, 5461, 0, SOUTH, 5)
        SPRING_ELEMENTAL_2961(2928, 5469, 0, SOUTH, 5)
        SPRING_ELEMENTAL_2963(2931, 5477, 0, SOUTH, 5)
        SPRING_ELEMENTAL_2959(2934, 5460, 0, SOUTH, 5)
        SPRING_ELEMENTAL_2960(2935, 5468, 0, SOUTH, 5)
    }
}