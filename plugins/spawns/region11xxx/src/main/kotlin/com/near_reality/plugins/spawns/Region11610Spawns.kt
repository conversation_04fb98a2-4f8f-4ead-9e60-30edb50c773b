package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11610Spawns : NPCSpawnsScript() {
    init {
        SPIDER_10652(2909, 5812, 0, SOUT<PERSON>, 5)
        SPIDER_10652(2913, 5808, 0, SOUTH, 5)
        FROG_10700(2914, 5772, 0, SOUTH, 5)
        SPIDER_10652(2918, 5815, 0, SOUTH, 5)
        SPIDER_10652(2920, 5809, 0, SOUTH, 5)
        FISHING_SPOT_10653(2921, 5807, 0, SOUTH, 5)
        FISHING_SPOT_10686(2922, 5788, 0, SO<PERSON><PERSON>, 5)
        10704(2922, 5821, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        FROG_10700(2926, 5770, 0, <PERSON>O<PERSON>H, 5)
        FISHING_SPOT_10686(2926, 5784, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        FROG_10700(2927, 5785, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        FISHING_<PERSON>OT_10686(2928, 5777, 0, SOUTH, 5)
        SPIDER_10652(2934, 5797, 0, SOUTH, 5)
        SPIDER_10652(2937, 5811, 0, SOUTH, 5)
        SPIDER_10652(2938, 5794, 0, SOUTH, 5)
        SPIDER_10652(2940, 5795, 0, SOUTH, 5)
    }
}