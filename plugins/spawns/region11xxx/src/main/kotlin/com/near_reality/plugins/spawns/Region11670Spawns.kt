package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11670Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(2921, 9653, 0, SOUTH, 14)
        RAT_2854(2923, 9639, 0, SOUTH, 14)
        MELZAR_THE_MAD(2929, 9649, 0, SOUTH, 5)
        SPIDER_3019(2929, 9659, 0, SOUTH, 8)
        SPIDER_3019(2930, 9648, 0, SOUTH, 8)
        ZOMBIE_3980(2932, 9642, 0, SOUTH, 5)
        ZOMBIE_3981(2933, 9640, 0, SOUTH, 5)
        RAT_2854(2936, 9648, 0, SOUTH, 14)
        LESSER_DEMON_3982(2936, 9650, 0, SOUTH, 5)
        SPIDER_3019(2936, 9654, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
    }
}