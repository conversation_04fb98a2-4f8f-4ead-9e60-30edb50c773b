package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11672Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_4928(2889, 9766, 0, SOUTH, 0)
        FISHING_SPOT_4928(2890, 9766, 0, SOUTH, 0)
        FISHING_SPOT_4928(2891, 9766, 0, SOUTH, 0)
        BABY_BLUE_DRAGON(2895, 9765, 0, SOUTH, 4)
        BABY_BLUE_DRAGON_242(2895, 9769, 0, SOUTH, 4)
        BABY_BLUE_DRAGON_243(2900, 9763, 0, SOUT<PERSON>, 3)
        HILL_GIANT_2101(2902, 9736, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        BLUE_DRAGON_268(2903, 9780, 0, <PERSON>OUT<PERSON>, 4)
        BLUE_DRAGON_269(2903, 9786, 0, SOUTH, 3)
        HILL_GIANT_2099(2905, 9732, 0, SOUTH, 3)
        BLUE_DRAGON_267(2906, 9774, 0, SOUTH, 3)
        HILL_GIANT_2100(2907, 9735, 0, SOUTH, 3)
        BLUE_DRAGON_267(2911, 9787, 0, SOUTH, 3)
        BLUE_DRAGON_266(2912, 9779, 0, SOUTH, 4)
        HILL_GIANT(2913, 9732, 0, SOUTH, 3)
        HILL_GIANT_2103(2913, 9741, 0, SOUTH, 3)
        CHAOS_DWARF(2915, 9760, 0, SOUTH, 14)
        BLUE_DRAGON(2920, 9784, 0, SOUTH, 3)
        CHAOS_DWARF(2922, 9757, 0, SOUTH, 14)
        CHAOS_DWARF(2925, 9769, 0, SOUTH, 14)
        BABY_BLUE_DRAGON_242(2925, 9788, 0, SOUTH, 4)
        CHAOS_DWARF(2928, 9761, 0, SOUTH, 14)
        POISON_SCORPION(2929, 9752, 0, SOUTH, 8)
        CHAOS_DWARF(2932, 9784, 0, SOUTH, 14)
        POISON_SCORPION(2934, 9775, 0, SOUTH, 8)
        POISON_SCORPION(2935, 9758, 0, SOUTH, 8)
        POISON_SCORPION(2935, 9769, 0, SOUTH, 8)
        CHAOS_DWARF(2938, 9788, 0, SOUTH, 14)
        POISON_SCORPION(2941, 9779, 0, SOUTH, 8)
    }
}