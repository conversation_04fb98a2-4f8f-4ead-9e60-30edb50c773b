package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11673Spawns : NPCSpawnsScript() {
    init {
        SKELETON_77(2884, 9836, 0, SOUTH, 7)
        SKELETON_73(2885, 9819, 0, SOUTH, 8)
        SKELETON_71(2885, 9823, 0, SOUTH, 7)
        SKELETON_71(2886, 9812, 0, SOUTH, 7)
        SKELETON_72(2886, 9816, 0, SOUTH, 8)
        SKELETON_73(2886, 9825, 0, SOUTH, 8)
        SKELETON(2887, 9821, 0, SOUTH, 7)
        GHOST_90(2888, 9849, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SPIDER_3019(2891, 9834, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        BABY_BLUE_DRAGON_242(2892, 9799, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SPIDER_3019(2893, 9828, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        GHOST_89(2894, 9849, 0, SOUTH, 4)
        SPIDER_3019(2896, 9833, 0, SOUTH, 8)
        BLUE_DRAGON_267(2897, 9797, 0, SOUTH, 3)
        BLUE_DRAGON(2899, 9802, 0, SOUTH, 3)
        GHOST(2900, 9819, 0, SOUTH, 5)
        GHOST_88(2901, 9848, 0, SOUTH, 5)
        BABY_BLUE_DRAGON_243(2904, 9796, 0, SOUTH, 3)
        BLUE_DRAGON_268(2904, 9802, 0, SOUTH, 4)
        GIANT_BAT(2904, 9832, 0, SOUTH, 11)
        GHOST_87(2905, 9851, 0, SOUTH, 2)
        GHOST_86(2907, 9818, 0, SOUTH, 5)
        GIANT_BAT(2908, 9830, 0, SOUTH, 11)
        BABY_BLUE_DRAGON_242(2909, 9806, 0, SOUTH, 4)
        GIANT_BAT(2909, 9837, 0, SOUTH, 11)
        GHOST_86(2909, 9848, 0, SOUTH, 5)
        BABY_BLUE_DRAGON(2911, 9797, 0, SOUTH, 4)
        BABY_BLUE_DRAGON(2911, 9808, 0, SOUTH, 4)
        GHOST_87(2912, 9820, 0, SOUTH, 2)
        GIANT_BAT(2914, 9828, 0, SOUTH, 11)
        GIANT_BAT(2914, 9840, 0, SOUTH, 11)
        GHOST(2915, 9851, 0, SOUTH, 5)
        BABY_BLUE_DRAGON_243(2917, 9801, 0, SOUTH, 3)
        BABY_BLUE_DRAGON_242(2918, 9792, 0, SOUTH, 4)
        GIANT_BAT(2919, 9831, 0, SOUTH, 11)
        GHOST_88(2920, 9818, 0, SOUTH, 5)
        GHOST_92(2920, 9848, 0, SOUTH, 6)
        GIANT_BAT(2921, 9826, 0, SOUTH, 11)
        GIANT_BAT(2922, 9834, 0, SOUTH, 11)
        BABY_BLUE_DRAGON(2923, 9793, 0, SOUTH, 4)
        GIANT_BAT(2925, 9830, 0, SOUTH, 11)
        LESSER_DEMON_2007(2926, 9802, 0, SOUTH, 4)
        CHAOS_DRUID(2929, 9848, 0, SOUTH, 8)
        LESSER_DEMON_2008(2931, 9798, 0, SOUTH, 6)
        LESSER_DEMON(2931, 9807, 0, SOUTH, 4)
        CHAOS_DRUID(2931, 9846, 0, SOUTH, 8)
        LESSER_DEMON_2006(2932, 9810, 0, SOUTH, 6)
        CHAOS_DRUID(2932, 9852, 0, SOUTH, 8)
        GHOST_91(2933, 9838, 0, SOUTH, 6)
        LESSER_DEMON_2018(2936, 9793, 0, SOUTH, 8)
        GHOST_89(2936, 9829, 0, SOUTH, 4)
        CHAOS_DRUID(2936, 9846, 0, SOUTH, 8)
        CHAOS_DRUID(2936, 9852, 0, SOUTH, 8)
        CHAOS_DRUID(2937, 9849, 0, SOUTH, 8)
        GHOST_90(2938, 9836, 0, SOUTH, 8)
        BLACK_KNIGHT_517(2939, 9812, 0, SOUTH, 6)
        BLACK_KNIGHT_517(2943, 9801, 0, SOUTH, 6)
        BLUE_DRAGON_5878(2889, 9804, 1, SOUTH, 2)
        BLUE_DRAGON_5879(2891, 9810, 1, SOUTH, 4)
        BLUE_DRAGON_5880(2896, 9813, 1, SOUTH, 3)
        EVE(2906, 9815, 1, SOUTH, 2)
        BLUE_DRAGON_5881(2917, 9812, 1, SOUTH, 3)
        BLUE_DRAGON_5882(2922, 9809, 1, SOUTH, 3)
        BLUE_DRAGON_5878(2926, 9812, 1, SOUTH, 2)
        BLUE_DRAGON_5882(2942, 9818, 1, SOUTH, 3)
    }
}