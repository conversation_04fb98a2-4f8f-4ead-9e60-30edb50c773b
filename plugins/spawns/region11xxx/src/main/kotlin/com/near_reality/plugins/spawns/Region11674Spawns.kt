package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11674Spawns : NPCSpawnsScript() {
    init {
        GIANT_BAT(2889, 9907, 0, SOUTH, 11)
        GIANT_BAT(2896, 9911, 0, SOUTH, 11)
        EXPERIMENT_NO2(2900, 9863, 0, SOUTH, 4)
        EXPERIMENT_NO2(2903, 9862, 0, SOUTH, 4)
        EXPERIMENT_NO2(2908, 9865, 0, SOUTH, 4)
        BLUE_DRAGON_266(2908, 9905, 0, SOUTH, 4)
        EXPERIMENT_NO2(2913, 9862, 0, <PERSON>OUTH, 4)
        CAGE_5133(2913, 9867, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        WINKIN(2914, 9867, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        CAGE_5133(2915, 9867, 0, <PERSON>OUTH, 0)
        CAGE_5133(2916, 9867, 0, <PERSON>OUTH, 0)
        EXPERIMENT_NO2(2917, 9865, 0, SOUTH, 4)
        CAGE_5133(2917, 9867, 0, SOUTH, 0)
        EXPERIMENT_NO2(2920, 9863, 0, SOUTH, 4)
        GIANT_BAT(2934, 9887, 0, SOUTH, 11)
        GIANT_BAT(2936, 9896, 0, SOUTH, 11)
        GIANT_BAT(2939, 9892, 0, SOUTH, 11)
    }
}