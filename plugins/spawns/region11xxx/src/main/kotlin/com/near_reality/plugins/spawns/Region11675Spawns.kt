package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11675Spawns : NPCSpawnsScript() {
    init {
        ICE_GIANT(2880, 9927, 0, SOUTH, 3)
        ICE_GIANT(2883, 9932, 0, SOUTH, 3)
        ICE_GIANT(2883, 9965, 0, SOUTH, 3)
        ICE_GIANT(2884, 9959, 0, SOUTH, 3)
        ICE_SPIDER(2885, 9962, 0, SOUTH, 6)
        ICE_SPIDER(2887, 9936, 0, SOUTH, 6)
        ICE_GIANT(2887, 9955, 0, SOUTH, 3)
        ICE_GIANT(2890, 9950, 0, SOUTH, 3)
        ICE_GIANT(2891, 9941, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        CYCLOPS_2137(2906, 9960, 0, <PERSON>OUTH, 3)
        CYCLOPS_2138(2906, 9963, 0, SOUTH, 2)
        RORY(2908, 9972, 0, SOUTH, 0)
        LORELAI(2909, 9972, 0, SOUTH, 0)
        CYCLOPS_2137(2911, 9959, 0, SOUTH, 3)
        CYCLOPS_2140(2913, 9963, 0, SOUTH, 4)
        CYCLOPS_2139(2913, 9971, 0, SOUTH, 4)
        CYCLOPS_2142(2916, 9958, 0, SOUTH, 2)
        CYCLOPS_2140(2918, 9971, 0, SOUTH, 4)
        CYCLOPS_2137(2919, 9961, 0, SOUTH, 3)
        CYCLOPS_2139(2919, 9964, 0, SOUTH, 4)
        CYCLOPS_2141(2919, 9968, 0, SOUTH, 3)
        CYCLOPS_2141(2924, 9958, 0, SOUTH, 3)
        CYCLOPS_2140(2924, 9971, 0, SOUTH, 4)
        CYCLOPS_2140(2927, 9964, 0, SOUTH, 4)
        CYCLOPS_2142(2927, 9967, 0, SOUTH, 2)
        CYCLOPS_2138(2928, 9972, 0, SOUTH, 2)
        CYCLOPS_2137(2929, 9959, 0, SOUTH, 3)
        CYCLOPS_2141(2932, 9966, 0, SOUTH, 3)
        CYCLOPS_2141(2932, 9970, 0, SOUTH, 3)
        CYCLOPS_2142(2933, 9959, 0, SOUTH, 2)
        CYCLOPS_2139(2933, 9963, 0, SOUTH, 4)
        CYCLOPS_2139(2937, 9963, 0, SOUTH, 4)
        CYCLOPS_2137(2937, 9971, 0, SOUTH, 3)
        CYCLOPS_2138(2938, 9958, 0, SOUTH, 2)
    }
}