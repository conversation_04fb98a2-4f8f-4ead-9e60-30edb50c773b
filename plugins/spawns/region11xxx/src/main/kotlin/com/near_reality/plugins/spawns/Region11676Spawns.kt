package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11676Spawns : NPCSpawnsScript() {
    init {
        MOUNTAIN_TROLL_4143(2921, 10027, 0, SOUTH, 9)
        MOUNTAIN_TROLL_939(2921, 10032, 0, SOUTH, 9)
        MOUNTAIN_TROLL_942(2924, 10031, 0, SOUTH, 9)
        MOUNTAIN_TROLL_941(2925, 10034, 0, SOUTH, 11)
    }
}