package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11679Spawns : NPCSpawnsScript() {
    init {
        GULLDAMAR(2885, 10197, 0, SOUTH, 2)
        HIRKO(2885, 10202, 0, SOUTH, 0)
        NOLAR(2885, 10207, 0, SOUTH, 2)
        VERMUNDI(2887, 10189, 0, SOUTH, 2)
        HERVI(2888, 10212, 0, SOUTH, 2)
        DWARVEN_BOATMAN(2888, 10227, 0, SOUTH, 0)
        BLACK_GUARD_6046(2889, 10207, 0, SOUTH, 2)
        2231(2890, 10224, 0, WEST, 0)
        2231(2890, 10226, 0, WEST, 0)
        BLAC<PERSON>_GUARD_6047(2891, 10192, 0, SOUT<PERSON>, 2)
        BLACK_GUARD_6048(2891, 10199, 0, SOUTH, 2)
        RANDIVOR(2892, 10212, 0, SOUTH, 2)
        ULIFED(2899, 10218, 0, SOUTH, 0)
        KARL(2900, 10227, 0, SOUTH, 3)
        3197(2904, 10207, 0, SOUTH, 0)
        ROWDY_DWARF(2906, 10198, 0, SOUTH, 9)
        BLASIDAR_THE_SCULPTOR(2906, 10205, 0, SOUTH, 2)
        KJUT(2906, 10216, 0, SOUTH, 2)
        CART_CONDUCTOR_2389(2908, 10188, 0, SOUTH, 2)
        3198(2912, 10222, 0, SOUTH, 3)
        THE_BEAST(2913, 10189, 0, SOUTH, 0)
        BARMAID(2916, 10195, 0, SOUTH, 2)
        CART_CONDUCTOR(2919, 10180, 0, SOUTH, 2)
        RUNVASTR(2923, 10203, 0, SOUTH, 2)
        SUNE(2924, 10213, 0, SOUTH, 19)
        CART_CONDUCTOR_2386(2925, 10176, 0, SOUTH, 2)
        CART_CONDUCTOR_2388(2925, 10180, 0, SOUTH, 2)
        TATI(2925, 10210, 0, SOUTH, 2)
        REINALD(2926, 10225, 0, SOUTH, 2)
        FACTORY_WORKER(2929, 10191, 0, SOUTH, 2)
        FACTORY_WORKER_2379(2930, 10188, 0, SOUTH, 2)
        SMOKIN_JOE(2930, 10213, 0, SOUTH, 2)
        FACTORY_MANAGER(2931, 10184, 0, SOUTH, 2)
        FACTORY_WORKER_2380(2931, 10187, 0, SOUTH, 2)
        FACTORY_WORKER_2378(2931, 10191, 0, SOUTH, 2)
        BENTAMIR(2933, 10219, 0, SOUTH, 2)
        CART_CONDUCTOR_2387(2936, 10181, 0, SOUTH, 2)
        DWARVEN_MINER_2439(2943, 10182, 0, SOUTH, 5)
        TRADER_6040(2880, 10194, 1, SOUTH, 2)
        TRADER_6039(2880, 10195, 1, SOUTH, 2)
        TRADE_REFEREE(2880, 10199, 1, SOUTH, 2)
        TRADER_6031(2880, 10201, 1, SOUTH, 2)
        TRADER_6037(2882, 10196, 1, SOUTH, 2)
        TRADER_6035(2882, 10199, 1, SOUTH, 2)
        TRADER_6033(2882, 10201, 1, SOUTH, 2)
        TRADER_6038(2884, 10195, 1, SOUTH, 2)
        TRADER_6034(2884, 10203, 1, SOUTH, 2)
        TRADER_6036(2885, 10199, 1, SOUTH, 2)
        GREEN_GEMSTONE_SECRETARY(2889, 10211, 1, SOUTH, 0)
        BROWN_ENGINE_SECRETARY(2890, 10192, 1, SOUTH, 0)
        WHITE_CHISEL_DIRECTOR_6022(2890, 10205, 1, SOUTH, 2)
        WHITE_CHISEL_SECRETARY(2890, 10207, 1, SOUTH, 0)
        SILVER_COG_SECRETARY(2891, 10196, 1, SOUTH, 0)
        GREEN_GEMSTONE_DIRECTOR_6021(2891, 10210, 1, SOUTH, 2)
        BROWN_ENGINE_DIRECTOR_6024(2892, 10190, 1, SOUTH, 2)
        SILVER_COG_DIRECTOR_6023(2893, 10196, 1, SOUTH, 2)
        BLANDEBIR(2915, 10194, 1, SOUTH, 2)
    }
}