package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11821Spawns : NPCSpawnsScript() {
    init {
        MONKEY_2848(2954, 2904, 0, SOUTH, 10)
        MONKEY_2848(2958, 2900, 0, SOUTH, 10)
        MONKEY_2848(2958, 2903, 0, SOUTH, 10)
        MONKEY_2848(2961, 2896, 0, SOUTH, 10)
    }
}