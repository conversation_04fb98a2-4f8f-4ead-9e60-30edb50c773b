package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11822Spawns : NPCSpawnsScript() {
    init {
        MONKEY_2848(2948, 2970, 0, SOUTH, 10)
        MONKEY_2848(2948, 3001, 0, SOUTH, 10)
        MONKEY_2848(2950, 2997, 0, SOUTH, 10)
        MONKEY_2848(2952, 2973, 0, SOUTH, 10)
        MONKEY_2848(2953, 2965, 0, SOUTH, 10)
        6092(2970, 2973, 0, SOUTH, 0)
    }
}