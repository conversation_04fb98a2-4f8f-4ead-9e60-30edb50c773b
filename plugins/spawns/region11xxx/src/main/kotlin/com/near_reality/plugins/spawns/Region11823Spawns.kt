package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11823Spawns : NPCSpawnsScript() {
    init {
        SHIPYARD_WORKER(2944, 3040, 0, SOUTH, 2)
        SHIPYARD_WORKER_5457(2953, 3056, 0, SOUTH, 5)
        SHIPYARD_WORKER_5457(2953, 3061, 0, SOUTH, 5)
        SHIPYARD_WORKER_5729(2955, 3029, 0, SOUTH, 5)
        GLO_CARANOCK(2956, 3025, 0, SOUTH, 2)
        SHIPYARD_WORKER_5457(2957, 3032, 0, SOUTH, 5)
        SHIPYARD_WORKER_5729(2957, 3055, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SHIPYARD_WORKER_5457(2969, 3034, 0, <PERSON>OUTH, 5)
        SHIPYARD_WORKER_5729(2969, 3042, 0, <PERSON>O<PERSON><PERSON>, 5)
        GULL(2974, 3068, 0, SOUTH, 7)
        GULL(2977, 3052, 0, SOUTH, 7)
        GULL(2987, 3034, 0, SOUTH, 7)
        GULL(2990, 3025, 0, SOUTH, 7)
        GULL(2992, 3040, 0, SOUTH, 7)
        SHARK_1830(3003, 3069, 0, SOUTH, 2)
        SHARK_1830(3007, 3063, 0, SOUTH, 2)
        SHIPYARD_WORKER_5729(2976, 3064, 0, SOUTH, 5)
        SHIPYARD_WORKER_5457(2983, 3049, 0, SOUTH, 5)
        SHIPYARD_WORKER_5729(2983, 3061, 0, SOUTH, 5)
        SHIPYARD_WORKER_5729(2987, 3049, 0, SOUTH, 5)
        SHIPYARD_WORKER_5729(2988, 3058, 0, SOUTH, 5)
        SHIPYARD_WORKER_5729(2995, 3061, 0, SOUTH, 5)
        FOREMAN(2999, 3043, 0, SOUTH, 2)
        1330(3001, 3033, 0, SOUTH, 4)
        1332(3001, 3034, 0, SOUTH, 2)
        SHIPYARD_WORKER_5729(3001, 3049, 0, SOUTH, 5)
    }
}