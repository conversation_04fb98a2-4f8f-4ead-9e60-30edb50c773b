package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11825Spawns : NPCSpawnsScript() {
    init {
        MAN_3652(2948, 3146, 0, SOUTH, 2)
        GULL_4735(2969, 3146, 0, SOUTH, 19)
        A_PILE_OF_BROKEN_GLASS(2981, 3190, 0, SOUTH, 0)
        3230(2983, 3196, 0, SOUTH, 5)
        FISHING_SPOT_1523(2986, 3176, 0, <PERSON>OUTH, 0)
        GULL_4734(2994, 3138, 0, SOUTH, 2)
        GIANT_RAT_2859(2995, 3191, 0, SOUT<PERSON>, 4)
        FISHING_SPOT_1523(2996, 3158, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        GIANT_RAT_2860(2999, 3194, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        THURGO(3001, 3144, 0, <PERSON><PERSON>UT<PERSON>, 3)
        CUSTOMS_OFFICER(2953, 3147, 0, SOUTH, 2)
        1333(2954, 3156, 0, SOUTH, 2)
        1331(2954, 3157, 0, SOUTH, 2)
    }
}