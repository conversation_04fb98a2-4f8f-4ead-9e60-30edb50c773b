package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11826Spawns : NPCSpawnsScript() {
    init {
        ROMMIK(2946, 3205, 0, SOUTH, 2)
        SHOP_KEEPER_2823(2947, 3217, 0, SOUTH, 0)
        SHOP_ASSISTANT_2824(2948, 3217, 0, SOUTH, 0)
        PHIALS(2950, 3212, 0, SOUTH, 2)
        RAT_2855(2953, 3204, 0, SOUTH, 3)
        RAT_2855(2955, 3202, 0, SOUTH, 3)
        BRIAN_8694(2957, 3203, 0, SOUTH, 2)
        RAT_2855(2959, 3202, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        RAT_2855(2960, 3205, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        HENJA(2963, 3211, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ANGEL(2964, 3210, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        HETTY(2968, 3206, 0, <PERSON>OUTH, 3)
        GOBLIN_3030(2995, 3209, 0, SOUTH, 13)
        GOBLIN_3031(2996, 3201, 0, SOUTH, 11)
        GOBLIN_3029(2997, 3216, 0, SOUTH, 13)
        GOBLIN_3032(2998, 3205, 0, SOUTH, 14)
        GOBLIN_3033(2999, 3208, 0, SOUTH, 12)
        GOBLIN_3034(2999, 3211, 0, SOUTH, 14)
        GOBLIN_3051(3001, 3202, 0, SOUTH, 12)
        GOBLIN_3036(3002, 3206, 0, SOUTH, 12)
        GOBLIN_3035(3002, 3210, 0, SOUTH, 11)
        GOBLIN_3053(3003, 3205, 0, SOUTH, 6)
        GOBLIN_3052(3004, 3201, 0, SOUTH, 11)
        GOBLIN_3054(3004, 3209, 0, SOUTH, 12)
        HENGEL(2967, 3210, 1, SOUTH, 5)
        ANJA(2967, 3213, 1, SOUTH, 5)
    }
}