package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11827Spawns : NPCSpawnsScript() {
    init {
        MAN_3109(2959, 3307, 0, SOUTH, 3)
        WAYNE(2973, 3312, 0, SOUTH, 3)
        AIR_WIZARD(2989, 3269, 0, SOUTH, 4)
        WATER_WIZARD(2990, 3267, 0, SOUTH, 5)
        MALIGNIUS_MORTIFER(2991, 3269, 0, SOUTH, 4)
        FIRE_WIZARD(2991, 3271, 0, SOUTH, 4)
        EARTH_WIZARD(2993, 3269, 0, SOUT<PERSON>, 6)
        GUARD_3254(3006, 3321, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        GUARD_3254(3006, 3323, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        HIGHWAYMAN_519(3007, 3273, 0, <PERSON>OUTH, 5)
        GUARD_3254(3007, 3323, 0, SOUTH, 3)
    }
}