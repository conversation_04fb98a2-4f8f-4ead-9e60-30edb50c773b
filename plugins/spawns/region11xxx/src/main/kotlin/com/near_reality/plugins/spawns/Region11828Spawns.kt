package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11828Spawns : NPCSpawnsScript() {
    init {
        HERQUIN(2944, 3334, 0, SOUTH, 3)
        GUARD_3270(2944, 3376, 0, SOUTH, 2)
        BANKER_1618(2945, 3366, 0, NORTH, 0)
        BANKER_1618(2946, 3366, 0, NORTH, 0)
        HAIRDRESSER(2946, 3379, 0, SOUTH, 3)
        IMP_5007(2947, 3329, 0, SOUTH, 100)
        BANKER_1618(2947, 3366, 0, NORTH, 0)
        GUARD_3269(2948, 3353, 0, <PERSON>O<PERSON><PERSON>, 2)
        BANKER_1618(2948, 3366, 0, NORT<PERSON>, 0)
        6523(2949, 3366, 0, NORTH, 0)
        GUARD_3272(2950, 3376, 0, SOUTH, 5)
        IMP_5007(2951, 3360, 0, SOUTH, 100)
        FLYNN(2951, 3386, 0, SOUTH, 3)
        TOWN_CRIER_278(2952, 3380, 0, SOUTH, 3)
        EMILY(2954, 3373, 0, SOUTH, 2)
        SHOP_ASSISTANT_2820(2955, 3389, 0, SOUTH, 2)
        WHITE_KNIGHT(2956, 3337, 0, SOUTH, 5)
        10648(2956, 3367, 0, SOUTH, 5)
        GUARD_3271(2956, 3382, 0, SOUTH, 5)
        KAYLEE(2957, 3370, 0, SOUTH, 5)
        SHOP_KEEPER_2819(2957, 3386, 0, SOUTH, 2)
        SWAN(2958, 3358, 0, SOUTH, 5)
        SWAN(2961, 3360, 0, SOUTH, 5)
        WHITE_KNIGHT(2962, 3338, 0, SOUTH, 5)
        GUARD_3269(2962, 3381, 0, SOUTH, 2)
        GUARD_3271(2964, 3376, 0, SOUTH, 5)
        GUARD_3269(2965, 3384, 0, SOUTH, 2)
        GUARD_3271(2965, 3391, 0, SOUTH, 5)
        CHICKEN(2966, 3346, 0, SOUTH, 2)
        SWAN(2967, 3361, 0, SOUTH, 5)
        GUARD_3272(2968, 3379, 0, SOUTH, 5)
        SWAN(2970, 3359, 0, SOUTH, 5)
        WHITE_KNIGHT(2972, 3345, 0, SOUTH, 5)
        WHITE_KNIGHT_1799(2973, 3347, 0, SOUTH, 5)
        SWAN(2975, 3358, 0, SOUTH, 5)
        WHITE_KNIGHT_1799(2976, 3339, 0, SOUTH, 5)
        7458(2976, 3347, 0, SOUTH, 0)
        SQUIRE_4737(2977, 3342, 0, SOUTH, 4)
        SIR_REBRAL(2977, 3346, 0, SOUTH, 0)
        DUCK(2977, 3357, 0, SOUTH, 18)
        WHITE_KNIGHT(2978, 3348, 0, SOUTH, 5)
        DUCK_1839(2978, 3356, 0, SOUTH, 20)
        SWAN(2978, 3358, 0, SOUTH, 5)
        CASSIE(2979, 3383, 0, SOUTH, 3)
        DUCK(2981, 3358, 0, SOUTH, 18)
        DUCK_1839(2982, 3357, 0, SOUTH, 20)
        DUCKLINGS(2982, 3358, 0, SOUTH, 2)
        WHITE_KNIGHT(2983, 3330, 0, SOUTH, 5)
        WHITE_KNIGHT(2983, 3334, 0, SOUTH, 5)
        WHITE_KNIGHT(2983, 3343, 0, SOUTH, 5)
        ESTATE_AGENT(2983, 3369, 0, SOUTH, 2)
        WHITE_KNIGHT(2987, 3341, 0, SOUTH, 5)
        SWAN(2989, 3383, 0, SOUTH, 5)
        DUCK_1839(2989, 3384, 0, SOUTH, 20)
        6800(2990, 3345, 0, SOUTH, 0)
        10428(2990, 3365, 0, SOUTH, 5)
        DUCK_2003(2990, 3379, 0, SOUTH, 3)
        DUCK(2992, 3383, 0, SOUTH, 18)
        DUCK_1839(2992, 3384, 0, SOUTH, 20)
        DRAKE(2993, 3386, 0, SOUTH, 3)
        WHITE_KNIGHT(2996, 3342, 0, SOUTH, 5)
        GARDENER(2996, 3381, 0, SOUTH, 5)
        SIR_TIFFY_CASHIEN(2997, 3373, 0, NORTH, 0)
        HESKEL(3001, 3373, 0, SOUTH, 5)
        7457(2953, 3390, 1, SOUTH, 0)
        DRUNKEN_MAN(2956, 3369, 1, SOUTH, 2)
        TINA(2956, 3373, 1, SOUTH, 2)
        WHITE_KNIGHT(2958, 3341, 1, SOUTH, 5)
        WHITE_KNIGHT(2961, 3351, 1, SOUTH, 5)
        WHITE_KNIGHT_1799(2963, 3336, 1, SOUTH, 5)
        WHITE_KNIGHT(2969, 3329, 1, SOUTH, 5)
        WHITE_KNIGHT(2969, 3338, 1, SOUTH, 5)
        APPRENTICE_WORKMAN(2970, 3369, 1, SOUTH, 5)
        WORKMAN_3278(2973, 3369, 1, SOUTH, 5)
        WHITE_KNIGHT(2974, 3337, 1, SOUTH, 5)
        WHITE_KNIGHT(2978, 3329, 1, SOUTH, 5)
        SIR_RENITEE(2981, 3341, 1, SOUTH, 0)
        WHITE_KNIGHT_1799(2982, 3345, 1, SOUTH, 5)
        WHITE_KNIGHT_1799(2983, 3335, 1, SOUTH, 5)
        AMBASSADOR_SPANFIPPLE(2984, 3342, 1, SOUTH, 8)
        WHITE_KNIGHT_1800(2987, 3345, 1, SOUTH, 4)
        WHITE_KNIGHT_1799(2989, 3338, 1, SOUTH, 5)
        CECILIA(2990, 3384, 0, SOUTH, 3)
        WHITE_KNIGHT_1829(2957, 3341, 2, SOUTH, 3)
        SIR_AMIK_VARZE_4771(2960, 3336, 2, SOUTH, 2)
        WHITE_KNIGHT_1800(2964, 3352, 2, SOUTH, 4)
        WHITE_KNIGHT_1800(2966, 3328, 2, SOUTH, 4)
        WHITE_KNIGHT_1799(2972, 3329, 2, SOUTH, 5)
        WHITE_KNIGHT_1800(2975, 3328, 2, SOUTH, 4)
        WHITE_KNIGHT(2981, 3339, 2, SOUTH, 5)
        SIR_VYVIN(2983, 3335, 2, SOUTH, 2)
        WHITE_KNIGHT_1799(2984, 3350, 2, SOUTH, 5)
        WHITE_KNIGHT_1799(2985, 3343, 2, SOUTH, 5)
        WHITE_KNIGHT_1800(2990, 3346, 2, SOUTH, 4)
        WHITE_KNIGHT_1829(2960, 3338, 3, SOUTH, 3)
        WHITE_KNIGHT_1800(2963, 3340, 3, SOUTH, 4)
        WHITE_KNIGHT_1829(2982, 3352, 3, SOUTH, 3)
    }
}