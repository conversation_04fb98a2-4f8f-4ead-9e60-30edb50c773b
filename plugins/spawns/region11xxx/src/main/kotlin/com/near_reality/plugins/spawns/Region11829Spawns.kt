package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11829Spawns : NPCSpawnsScript() {
    init {
        DORIC(2952, 3451, 0, SOUTH, 2)
        GUARD_3269(2962, 3398, 0, SOUTH, 2)
        GUARD_3269(2964, 3394, 0, SOUTH, 2)
        SQUIRREL(2965, 3445, 0, SOUTH, 8)
        GUARD_3271(2966, 3396, 0, SOUTH, 5)
        GUARD_3269(2967, 3393, 0, SOUTH, 2)
        9392(2967, 3411, 0, SOUTH, 0)
        GUARD_3283(2967, 3452, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SQUIRREL_1417(2970, 3441, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        RACCOON(2973, 3453, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        RACCOON_1420(2977, 3450, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        JEFF(2978, 3443, 0, SOUTH, 3)
        RUSTY(2979, 3436, 0, SOUTH, 5)
        SQUIRREL_1418(2980, 3438, 0, SOUTH, 10)
        RACCOON_1421(2982, 3444, 0, SOUTH, 11)
        <PERSON>UFFS(2986, 3434, 0, SOUTH, 4)
        NARF(2986, 3438, 0, SOUTH, 4)
        //DWARF(2998, 3445, 0, SOUTH, 13)
        //DWARF(2999, 3453, 0, SOUTH, 13)
        //10638(3003, 3435, 0, SOUTH, 5)
    }
}