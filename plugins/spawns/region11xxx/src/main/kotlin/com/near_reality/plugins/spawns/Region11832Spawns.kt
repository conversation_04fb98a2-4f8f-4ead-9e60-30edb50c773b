package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11832Spawns : NPCSpawnsScript() {
    init {
        GREEN_DRAGON_263(2973, 3620, 0, SOUTH, 4)
        GREEN_DRAGON_264(2977, 3611, 0, SOUTH, 3)
        GREEN_DRAGON(2982, 3618, 0, SOUTH, 2)
    }
}