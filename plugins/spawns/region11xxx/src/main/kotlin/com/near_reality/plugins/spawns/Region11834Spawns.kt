package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11834Spawns : NPCSpawnsScript() {
    init {
        ANKOU_6608(2960, 3744, 0, <PERSON>OUT<PERSON>, 9)
        ANKOU_6608(2960, 3751, 0, SOUT<PERSON>, 9)
        ANKOU_6608(2961, 3762, 0, SOUTH, 9)
        GHOST(2962, 3755, 0, SOUTH, 5)
        ANKOU_6608(2965, 3754, 0, <PERSON><PERSON>UT<PERSON>, 9)
        ANKOU_6608(2966, 3761, 0, <PERSON>O<PERSON><PERSON>, 9)
        IAN(2967, 3765, 0, SOUTH, 2)
        ANKOU_6608(2968, 3744, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        GHOST(2968, 3749, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GHOST(2968, 3761, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ANKOU_6608(2970, 3755, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        ANKOU_6608(2971, 3761, 0, <PERSON><PERSON>UTH, 9)
        <PERSON>KOU_6608(2972, 3748, 0, SOUTH, 9)
        ANKOU_6608(2974, 3741, 0, SOUTH, 9)
        MAN_3106(2974, 3742, 0, SOUTH, 5)
        ANKOU_6608(2976, 3759, 0, SOUTH, 9)
        ANKOU_6608(2977, 3748, 0, SOUTH, 9)
        SKELETON_77(2977, 3753, 0, SOUTH, 7)
        SKELETON_77(2980, 3763, 0, SOUTH, 7)
        ANKOU_6608(2981, 3739, 0, SOUTH, 9)
        ANKOU_6608(2981, 3756, 0, SOUTH, 9)
        ANKOU_6608(2981, 3766, 0, SOUTH, 9)
        ANKOU_6608(2983, 3746, 0, SOUTH, 9)
        GHOST(2985, 3749, 0, SOUTH, 5)
        GHOST(2985, 3761, 0, SOUTH, 5)
        ANKOU_6608(2987, 3752, 0, SOUTH, 9)
        ANKOU_6608(2989, 3764, 0, SOUTH, 9)
        ANKOU_6608(2990, 3743, 0, SOUTH, 9)
        GHOST(2990, 3755, 0, SOUTH, 5)
        ANKOU_6608(2991, 3750, 0, SOUTH, 9)
        ANKOU_6608(2992, 3760, 0, SOUTH, 9)
    }
}