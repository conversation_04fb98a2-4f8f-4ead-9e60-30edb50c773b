package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11836Spawns : NPCSpawnsScript() {
    init {
        ICE_WARRIOR(2945, 3866, 0, SOUTH, 6)
        ICE_WARRIOR(2946, 3857, 0, SOUTH, 6)
        ICE_GIANT_2087(2947, 3895, 0, SOUTH, 3)
        ICE_WARRIOR(2948, 3878, 0, SOUTH, 6)
        ICE_WARRIOR(2948, 3886, 0, SOUTH, 6)
        ICE_WARRIOR(2952, 3874, 0, SOUTH, 6)
        ICE_GIANT(2952, 3902, 0, SOUTH, 3)
        ICE_WARRIOR(2953, 3858, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ICE_GIANT(2953, 3889, 0, <PERSON><PERSON><PERSON>H, 3)
        ICE_WARRIOR(2954, 3877, 0, <PERSON><PERSON><PERSON>H, 6)
        ICE_WARRIOR(2954, 3883, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ICE_GIANT_2086(2954, 3894, 0, SOUTH, 3)
        ICE_WARRIOR(2957, 3887, 0, SOUTH, 6)
        ICE_WARRIOR(2958, 3866, 0, SOUTH, 6)
        ICE_GIANT_2087(2958, 3898, 0, SOUTH, 3)
        ICE_WARRIOR(2959, 3876, 0, SOUTH, 6)
        ICE_WARRIOR(2962, 3883, 0, SOUTH, 6)
        ICE_WARRIOR(2963, 3874, 0, SOUTH, 6)
        MINIATURE_CHAOTIC_CLOUDS(2973, 3843, 0, SOUTH, 2)
        MINIATURE_CHAOTIC_CLOUDS(2977, 3848, 0, SOUTH, 2)
        MINIATURE_CHAOTIC_CLOUDS(2979, 3840, 0, SOUTH, 2)
        CHAOS_FANATIC(2979, 3846, 0, SOUTH, 3)
        MINIATURE_CHAOTIC_CLOUDS(2982, 3851, 0, SOUTH, 2)
        MINIATURE_CHAOTIC_CLOUDS(2983, 3842, 0, SOUTH, 2)
        MINIATURE_CHAOTIC_CLOUDS(2983, 3847, 0, SOUTH, 2)
    }
}