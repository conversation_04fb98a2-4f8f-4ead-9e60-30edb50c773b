package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11837Spawns : NPCSpawnsScript() {
    init {
        ICE_GIANT(2947, 3921, 0, SOUTH, 3)
        ICE_WARRIOR(2947, 3934, 0, SOUTH, 6)
        ICE_WARRIOR(2948, 3917, 0, SOUTH, 6)
        ICE_WARRIOR(2949, 3926, 0, SOUTH, 6)
        ICE_GIANT_2086(2950, 3932, 0, SOUTH, 3)
        ICE_WARRIOR(2952, 3913, 0, SOUTH, 6)
        ICE_WARRIOR(2952, 3936, 0, SOUTH, 6)
        ICE_WARRIOR(2954, 3921, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ICE_GIANT_2086(2955, 3945, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        ICE_WARRIOR(2956, 3930, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ICE_SPIDER(2959, 3917, 0, <PERSON><PERSON><PERSON>H, 6)
        ICE_SPIDER(2960, 3925, 0, SOUTH, 6)
        ICE_SPIDER(2961, 3920, 0, SOUTH, 6)
        ICE_SPIDER(2962, 3915, 0, SOUTH, 6)
        ICE_SPIDER(2962, 3933, 0, SOUTH, 6)
        ICE_SPIDER(2963, 3923, 0, SOUTH, 6)
        ICE_SPIDER(2963, 3929, 0, SOUTH, 6)
        ICE_WARRIOR(2964, 3944, 0, SOUTH, 6)
        ICE_SPIDER(2965, 3927, 0, SOUTH, 6)
        ICE_SPIDER(2965, 3931, 0, SOUTH, 6)
        ICE_WARRIOR(2970, 3947, 0, SOUTH, 6)
        ICE_WARRIOR(2971, 3938, 0, SOUTH, 6)
        ICE_WARRIOR(2977, 3953, 0, SOUTH, 6)
        ICE_WARRIOR(2978, 3942, 0, SOUTH, 6)
        ICE_GIANT_2087(2978, 3956, 0, SOUTH, 3)
        SKELETON_81(2982, 3947, 0, SOUTH, 6)
        ICE_WARRIOR(2984, 3933, 0, SOUTH, 6)
        WHITE_WOLF_108(2988, 3921, 0, SOUTH, 3)
        SKELETON_79(2989, 3944, 0, SOUTH, 8)
        WHITE_WOLF_108(2991, 3924, 0, SOUTH, 3)
        WHITE_WOLF_108(2992, 3917, 0, SOUTH, 3)
        SKELETON_77(2992, 3942, 0, SOUTH, 7)
        WHITE_WOLF_108(2994, 3921, 0, SOUTH, 3)
        WHITE_WOLF_108(3002, 3924, 0, SOUTH, 3)
        WHITE_WOLF_108(3004, 3917, 0, SOUTH, 3)
        WHITE_WOLF_108(3005, 3926, 0, SOUTH, 3)
        WHITE_WOLF_108(3006, 3921, 0, SOUTH, 3)
    }
}