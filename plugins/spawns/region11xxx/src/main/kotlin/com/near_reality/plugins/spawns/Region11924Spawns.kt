package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11924Spawns : NPCSpawnsScript() {
    init {
        MUDSKIPPER(2948, 9522, 1, SOUTH, 5)
        MUDSKIPPER(2949, 9525, 1, SOUTH, 5)
        MUDSKIPPER_4821(2949, 9528, 1, SOUTH, 5)
        MUDSKIPPER(2951, 9522, 1, SOUTH, 5)
        MUDSKIPPER_4821(2952, 9525, 1, SOUTH, 5)
        MUDSKIPPER(2952, 9528, 1, SOUTH, 5)
        MUDSKIPPER(2953, 9523, 1, SOUTH, 5)
        MUDSKIPPER(2954, 9530, 1, <PERSON>OUT<PERSON>, 5)
        FISH_4846(2957, 9503, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        FISH_4843(2958, 9490, 1, <PERSON><PERSON><PERSON>H, 5)
        FISH_4836(2958, 9500, 1, SOUTH, 5)
        FISH_4830(2959, 9485, 1, SOUTH, 5)
        FISH_4831(2959, 9495, 1, SOUTH, 5)
        FISH_4837(2960, 9500, 1, SOUTH, 5)
        FISH_4842(2962, 9480, 1, SOUTH, 5)
        FISH_4836(2962, 9503, 1, SOUTH, 5)
        FISH_4838(2963, 9488, 1, SOUTH, 4)
        FISH_4826(2963, 9492, 1, SOUTH, 5)
        FISH_4833(2963, 9498, 1, SOUTH, 5)
        FISH_4828(2964, 9485, 1, SOUTH, 5)
        FISH_4839(2964, 9496, 1, SOUTH, 5)
        FISH_4839(2964, 9502, 1, SOUTH, 5)
        FISH_4844(2967, 9498, 1, SOUTH, 5)
        FISH_4825(2968, 9487, 1, SOUTH, 5)
        MOGRE_GUARD(2968, 9523, 1, SOUTH, 5)
        FISH_4835(2969, 9494, 1, SOUTH, 5)
        MOGRE_GUARD(2969, 9512, 1, SOUTH, 5)
        FISH_4824(2970, 9492, 1, SOUTH, 5)
        FISH_4841(2971, 9486, 1, SOUTH, 5)
        FISH_4837(2972, 9494, 1, SOUTH, 5)
        FISH(2972, 9497, 1, SOUTH, 5)
        FISH_4844(2972, 9500, 1, SOUTH, 5)
        CRAB_4819(2972, 9516, 1, SOUTH, 5)
        CRAB_4822(2972, 9519, 1, SOUTH, 5)
        FISH_4845(2973, 9490, 1, SOUTH, 5)
        FISH_4833(2974, 9492, 1, SOUTH, 5)
        FISH_4838(2975, 9495, 1, SOUTH, 4)
        FISH_4834(2975, 9498, 1, SOUTH, 5)
        NUNG(2975, 9512, 1, SOUTH, 5)
        CRAB_4822(2975, 9518, 1, SOUTH, 5)
        FISH_4829(2976, 9485, 1, SOUTH, 5)
        FISH_4827(2976, 9490, 1, SOUTH, 5)
        CRAB_4822(2976, 9516, 1, SOUTH, 5)
        CRAB_4822(2977, 9520, 1, SOUTH, 5)
        CRAB_4822(2977, 9522, 1, SOUTH, 5)
        FISH_4830(2978, 9481, 1, SOUTH, 5)
        FISH_4824(2978, 9495, 1, SOUTH, 5)
        FISH_4840(2978, 9498, 1, SOUTH, 5)
        CRAB_4819(2978, 9518, 1, SOUTH, 5)
        FISH_4827(2980, 9485, 1, SOUTH, 5)
        MOGRE_GUARD(2980, 9511, 1, SOUTH, 5)
        FISH_4843(2981, 9482, 1, SOUTH, 5)
        FISH_4831(2981, 9493, 1, SOUTH, 5)
        CRAB_4822(2981, 9517, 1, SOUTH, 5)
        FISH_4829(2983, 9487, 1, SOUTH, 5)
        FISH_4841(2983, 9490, 1, SOUTH, 5)
        FISH(2983, 9494, 1, SOUTH, 5)
        CRAB_4822(2983, 9519, 1, SOUTH, 5)
        FISH_4846(2984, 9496, 1, SOUTH, 5)
        FISH_4825(2984, 9499, 1, SOUTH, 5)
        CRAB_4822(2984, 9516, 1, SOUTH, 5)
        FISH_4834(2986, 9487, 1, SOUTH, 5)
        FISH_4832(2986, 9494, 1, SOUTH, 2)
        FISH_4842(2988, 9492, 1, SOUTH, 5)
        FISH_4828(2988, 9497, 1, SOUTH, 5)
        MOGRE_GUARD(2990, 9519, 1, SOUTH, 5)
        FISH_4840(2992, 9485, 1, SOUTH, 5)
        FISH_4845(2993, 9498, 1, SOUTH, 5)
    }
}