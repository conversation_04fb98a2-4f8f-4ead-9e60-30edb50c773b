package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11925Spawns : NPCSpawnsScript() {
    init {
        PIRATE_523(2984, 9575, 0, SOUTH, 4)
        PIRATE_523(2986, 9582, 0, SOUTH, 4)
        PIRATE_523(2990, 9571, 0, SOUTH, 4)
        PIRATE_523(2991, 9583, 0, SOUTH, 4)
        MUGGER(2993, 9545, 0, SOUTH, 2)
        MUGGER(2995, 9548, 0, SOUTH, 2)
        PIRATE_523(2995, 9576, 0, SOUTH, 4)
        MUGGER(2997, 9544, 0, SOUTH, 2)
        PIRATE_523(2997, 9569, 0, <PERSON>O<PERSON><PERSON>, 4)
        PIRATE_523(2998, 9572, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MUGGER(2999, 9550, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        PIRATE_523(2999, 9584, 0, SOUTH, 4)
        PIRATE_523(3000, 9579, 0, SOUTH, 4)
    }
}