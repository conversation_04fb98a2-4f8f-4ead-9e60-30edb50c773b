package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11926Spawns : NPCSpawnsScript() {
    init {
        RAT_1022(2959, 9632, 0, SOUTH, 5)
        PUSSKINS(2960, 9633, 0, SOUTH, 5)
        RAT(2960, 9636, 0, SOUTH, 5)
        RAT_1021(2961, 9631, 0, SOUTH, 5)
        RAT(2961, 9633, 0, SOUTH, 5)
        RAT_1022(2961, 9635, 0, SOUTH, 5)
        RAT_1021(2961, 9636, 0, SOUTH, 5)
        RAT_2854(2961, 9647, 0, SOUTH, 14)
        TURBOGROOMER(2962, 9634, 0, <PERSON>OUTH, 5)
        GAMER_1017(2962, 9640, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RAT_1022(2963, 9632, 0, <PERSON><PERSON>UTH, 5)
        RAT_1021(2963, 9633, 0, SOUTH, 5)
        RAT(2963, 9635, 0, SOUTH, 5)
        GAMER_1019(2967, 9633, 0, SOUTH, 5)
        RAT_2854(2976, 9634, 0, SOUTH, 14)
        FELKRASH(2977, 9639, 0, SOUTH, 5)
        GAMER_1018(2980, 9636, 0, SOUTH, 5)
        RAT_1021(2982, 9631, 0, SOUTH, 5)
        RAT_1022(2983, 9638, 0, SOUTH, 5)
        RAT_1021(2983, 9640, 0, SOUTH, 5)
        RAT_1021(2984, 9632, 0, SOUTH, 5)
        RAT(2984, 9636, 0, SOUTH, 5)
        BARMAN(2984, 9656, 0, SOUTH, 5)
        RAT_1021(2985, 9630, 0, SOUTH, 5)
        RAT_1022(2985, 9632, 0, SOUTH, 5)
        RAT(2985, 9634, 0, SOUTH, 5)
        RAT(2985, 9638, 0, SOUTH, 5)
        GAMER(2985, 9652, 0, SOUTH, 5)
        RAT_1022(2986, 9640, 0, SOUTH, 5)
        RAT_1022(2987, 9633, 0, SOUTH, 5)
        RAT(2987, 9637, 0, SOUTH, 5)
        RAT_1021(2988, 9636, 0, SOUTH, 5)
        RAT_1021(2988, 9640, 0, SOUTH, 5)
        RAT_1022(2988, 9642, 0, SOUTH, 5)
        RAT_1022(2989, 9634, 0, SOUTH, 5)
        RAT(2989, 9637, 0, SOUTH, 5)
        RAT_2854(2989, 9653, 0, SOUTH, 14)
        GAMER_1015(2989, 9654, 0, SOUTH, 5)
        RAT_1022(2990, 9639, 0, SOUTH, 5)
        RAT(2990, 9642, 0, SOUTH, 5)
        RAT_1021(2991, 9632, 0, SOUTH, 5)
        RAT(2991, 9636, 0, SOUTH, 5)
    }
}