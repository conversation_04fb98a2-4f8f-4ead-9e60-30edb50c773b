package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11928Spawns : NPCSpawnsScript() {
    init {
        MAGIC_AXE(2955, 9775, 0, SOUTH, 6)
        MAGIC_AXE(2956, 9790, 0, SOUTH, 6)
        MAGIC_AXE(2957, 9780, 0, SOUTH, 6)
        SQUIRE_10368(2962, 9733, 0, SOUTH, 5)
        MAGIC_AXE(2962, 9791, 0, SOUTH, 6)
        MAGIC_AXE(2966, 9775, 0, SOUTH, 6)
        MAGIC_AXE(2966, 9787, 0, SOUTH, 6)
    }
}