package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11929Spawns : NPCSpawnsScript() {
    init {
        MAGIC_AXE(2955, 9795, 0, SOUTH, 6)
        HAMMERSPIKE_STOUTBEARD(2965, 9811, 0, SOUTH, 3)
        DWARF_GANG_MEMBER(2966, 9813, 0, SOUTH, 2)
        DWARF_GANG_MEMBER_1355(2967, 9813, 0, SOUTH, 2)
        DWARF_GANG_MEMBER_1356(2968, 9813, 0, SOUTH, 3)
        DWARF(2984, 9807, 0, SOUTH, 13)
        BOOT(2985, 9812, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        DWARF(2992, 9844, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DWARF_5904(2993, 9828, 0, <PERSON><PERSON>UT<PERSON>, 5)
        DWARF(2994, 9826, 0, SOUTH, 13)
        DWARF(2995, 9805, 0, SOUTH, 13)
        CART_CONDUCTOR_2391(2995, 9837, 0, SOUTH, 2)
        DWARF(2998, 9841, 0, SOUTH, 13)
        NURMOF(2998, 9844, 0, SOUTH, 6)
        DWARF(2999, 9817, 0, SOUTH, 13)
        DWARF(3000, 9809, 0, SOUTH, 13)
        DWARF(3000, 9826, 0, SOUTH, 13)
        DWARF(3001, 9796, 0, SOUTH, 13)
        DWARF(3001, 9844, 0, SOUTH, 13)
        DWARF(3002, 9829, 0, SOUTH, 13)
        HURA(3003, 9799, 0, SOUTH, 0)
        DWARF(3005, 9799, 0, SOUTH, 13)
        BLUE_DRAGON_5879(2944, 9809, 1, SOUTH, 4)
        BLUE_DRAGON_5881(2946, 9824, 1, SOUTH, 3)
        BLUE_DRAGON_5880(2951, 9814, 1, SOUTH, 3)
        BLUE_DRAGON_5878(2953, 9827, 1, SOUTH, 2)
        BLUE_DRAGON_5879(2955, 9821, 1, SOUTH, 4)
    }
}