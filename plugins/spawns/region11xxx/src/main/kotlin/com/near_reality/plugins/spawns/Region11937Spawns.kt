package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region11937Spawns : NPCSpawnsScript() {
    init {
        SKELETON_78(2994, 10346, 0, SOUT<PERSON>, 8)
        SKELETON_77(2998, 10342, 0, SOUT<PERSON>, 7)
        SKELETON_80(2998, 10350, 0, SOUTH, 8)
        SKELETON_77(3002, 10356, 0, SOUTH, 7)
        SKELETON_79(3003, 10347, 0, SOUTH, 8)
        SKELETON_79(3004, 10362, 0, <PERSON>O<PERSON><PERSON>, 8)
        SKELETON_81(3006, 10352, 0, SOUTH, 6)
        SKELETON_78(3006, 10357, 0, SOUTH, 8)
    }
}