package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12078Spawns : NPCSpawnsScript() {
    init {
        GULL(3010, 2967, 0, SOUTH, 7)
        CRAB_10563(3012, 2985, 0, SOUTH, 5)
        GULL(3012, 3002, 0, SOUTH, 7)
        GULL(3013, 2977, 0, SOUTH, 7)
        CRAB_10563(3013, 2982, 0, SOUTH, 5)
        CRAB_10563(3014, 2984, 0, SOUTH, 5)
        GULL(3019, 2947, 0, SOUTH, 7)
        CRAB_10563(3024, 2980, 0, SOUTH, 5)
        CRAB_10563(3038, 3005, 0, SOUT<PERSON>, 5)
        CRAB_10563(3041, 3004, 0, <PERSON>O<PERSON><PERSON>, 5)
        CRAB_10563(3047, 2959, 0, <PERSON><PERSON>UTH, 5)
        GULL(3049, 2950, 0, SOUTH, 7)
        GULL(3051, 3002, 0, SOUTH, 7)
        GULL(3053, 2995, 0, SOUTH, 7)
        GULL(3055, 2953, 0, SOUTH, 7)
        GULL(3055, 2999, 0, SOUTH, 7)
        GULL(3063, 2966, 0, SOUTH, 7)
        GULL(3068, 2989, 0, SOUTH, 7)
    }
}