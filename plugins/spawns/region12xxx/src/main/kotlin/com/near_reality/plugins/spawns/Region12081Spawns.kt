package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12081Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(3011, 3189, 0, SOUTH, 14)
        CRAB_1553(3013, 3141, 0, SOUTH, 4)
        RAT_2854(3013, 3183, 0, SOUTH, 14)
        RAT_2854(3013, 3190, 0, SOUTH, 14)
        CAPN_HAND(3013, 3192, 0, SOUTH, 2)
        RAT_2854(3013, 3194, 0, SOUTH, 14)
        WORMBRAIN(3014, 3189, 0, SOUTH, 2)
        PIRATE_1447(3014, 3191, 0, SOUTH, 2)
        THIEF(3014, 3195, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        RAT_2854(3015, 3182, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        BLACK_KNIGHT_1545(3018, 3180, 0, SOUTH, 2)
        MUGGER_1461(3018, 3189, 0, SOUTH, 2)
        CRAB_1553(3019, 3154, 0, SOUTH, 4)
        GUARD_1551(3019, 3185, 0, SOUTH, 0)
        CRAB_1553(3020, 3146, 0, SOUTH, 4)
        CRAB_1553(3022, 3157, 0, SOUTH, 4)
        CRAB_1553(3025, 3156, 0, SOUTH, 4)
        CRAB_1553(3028, 3158, 0, SOUTH, 4)
        SEAGULL_1554(3037, 3146, 0, SOUTH, 6)
        SEAGULL_1555(3037, 3153, 0, SOUTH, 5)
        GUARD_1548(3011, 3181, 1, SOUTH, 5)
        GUARD_1550(3012, 3185, 1, SOUTH, 5)
        SECURITY_GUARD(3013, 3192, 1, SOUTH, 5)
        GUARD_1547(3016, 3182, 1, SOUTH, 5)
        GUARD_1546(3016, 3184, 1, SOUTH, 5)
        GUARD_1549(3019, 3180, 1, SOUTH, 5)
        1328(3039, 3192, 0, SOUTH, 2)
        1334(3039, 3193, 0, SOUTH, 2)
        1331(3042, 3192, 0, SOUTH, 2)
        SEAMAN_MORRIS(3050, 3193, 0, SOUTH, 5)
        GUARD_1552(3018, 3179, 2, SOUTH, 5)
    }
}