package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12082Spawns : NPCSpawnsScript() {
    init {
        WOMAN_3111(3011, 3236, 0, SOUTH, 5)
        BETTY_5905(3012, 3259, 0, SOUTH, 2)
        GERRANT_2891(3013, 3225, 0, SOUTH, 4)
        GRUM_2889(3013, 3248, 0, SOUTH, 2)
        WYDIN_2890(3014, 3204, 0, SOUTH, 2)
        THIEF_3252(3014, 3232, 0, SOUTH, 5)
        THIEF_3253(3016, 3232, 0, SOUTH, 5)
        MAN_3106(3017, 3238, 0, <PERSON>OUTH, 5)
        THE_FACE(3019, 3231, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        BELLEMORDE(3019, 3233, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        BRIAN(3028, 3250, 0, <PERSON>O<PERSON><PERSON>, 4)
        BARTENDER_1313(3045, 3257, 0, SOUTH, 2)
        JACK_SEAGULL(3047, 3256, 0, SOUTH, 2)
        LONGBOW_BEN(3047, 3257, 0, SOUTH, 2)
        AHAB(3049, 3256, 0, SOUTH_WEST, 0)
        REDBEARD_FRANK(3051, 3253, 0, SOUTH, 3)
        CABIN_BOY_HERBERT_10933(3054, 3246, 0, SOUTH, 3)
        FRIZZY_SKERNIP(3060, 3256, 0, SOUTH, 5)
        TOOL_LEPRECHAUN(3064, 3255, 0, SOUTH, 0)
        SEAMAN_THRESNOR(3026, 3217, 0, SOUTH, 4)
        SEAGULL(3027, 3204, 0, SOUTH, 5)
        SEAGULL(3028, 3202, 0, SOUTH, 5)
        CAPTAIN_TOBIAS(3028, 3216, 0, SOUTH, 4)
        SEAMAN_LORRIS(3028, 3221, 0, SOUTH, 4)
        SEAGULL(3028, 3232, 0, SOUTH, 5)
        SEAGULL(3028, 3235, 0, SOUTH, 5)
        SEAGULL_1339(3029, 3203, 0, SOUTH, 4)
        SEAGULL_1339(3029, 3236, 0, SOUTH, 4)
        SEAGULL(3030, 3236, 0, SOUTH, 5)
        SEAGULL(3031, 3203, 0, SOUTH, 5)
        SQUIRE_1770(3041, 3202, 0, SOUTH, 2)
        6115(3044, 3204, 0, SOUTH, 2)
        MONK_OF_ENTRANA(3045, 3236, 0, SOUTH, 4)
        1979(3046, 3207, 1, SOUTH, 0)
        MONK_OF_ENTRANA_1166(3046, 3234, 0, SOUTH, 4)
        KLARENSE(3047, 3204, 0, NORTH, 0)
        818(3048, 3207, 1, SOUTH, 2)
        SEAGULL_1339(3049, 3202, 0, SOUTH, 4)
        MONK_OF_ENTRANA_1167(3049, 3235, 0, SOUTH, 3)
        SEAGULL(3050, 3203, 0, SOUTH, 5)
        1063(3054, 3245, 0, SOUTH, 5)
    }
}