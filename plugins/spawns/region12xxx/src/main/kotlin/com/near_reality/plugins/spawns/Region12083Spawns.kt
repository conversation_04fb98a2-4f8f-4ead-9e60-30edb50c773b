package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12083Spawns : NPCSpawnsScript() {
    init {
        HIGHWAYMAN(3009, 3280, 0, SOUTH, 12)
        IMP_5007(3009, 3307, 0, SOUTH, 100)
        IMP_5007(3011, 3314, 0, SOUTH, 100)
        PIG_2807(3014, 3309, 0, SOUTH, 3)
        DUCK(3015, 3284, 0, SOUTH, 18)
        CHICKEN_2806(3015, 3287, 0, SOUTH, 5)
        RAT_2854(3015, 3295, 0, SOUTH, 14)
        PIG_2808(3015, 3309, 0, SOUTH, 2)
        IMP_5007(3015, 3314, 0, SOUT<PERSON>, 100)
        DUCK_1839(3016, 3284, 0, <PERSON><PERSON><PERSON><PERSON>, 20)
        CHICKEN_2804(3016, 3294, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        PIGLET_2809(3017, 3309, 0, S<PERSON><PERSON>H, 3)
        CH<PERSON><PERSON><PERSON>_2804(3018, 3283, 0, SOUTH, 4)
        CHICKEN_2805(3018, 3289, 0, SOUTH, 6)
        DOG(3018, 3291, 0, SOUTH, 4)
        CHICKEN_2805(3018, 3294, 0, SOUTH, 6)
        PIGLET_2810(3018, 3309, 0, SOUTH, 3)
        CHICKEN_2805(3019, 3285, 0, SOUTH, 6)
        RAT_2854(3019, 3296, 0, SOUTH, 14)
        PIGLET_2811(3019, 3309, 0, SOUTH, 3)
        COW(3025, 3307, 0, SOUTH, 3)
        CHICKEN_2804(3027, 3288, 0, SOUTH, 4)
        COW_CALF_2801(3027, 3308, 0, SOUTH, 4)
        CHICKEN_2804(3029, 3286, 0, SOUTH, 4)
        COW(3029, 3305, 0, SOUTH, 3)
        COW_2793(3029, 3311, 0, SOUTH, 7)
        7959(3030, 3273, 0, SOUTH, 0)
        COW_2791(3030, 3300, 0, SOUTH, 4)
        CHICKEN_2805(3031, 3286, 0, SOUTH, 6)
        CHICKEN_2806(3031, 3288, 0, SOUTH, 5)
        COW_CALF_2801(3031, 3311, 0, SOUTH, 4)
        COW(3033, 3305, 0, SOUTH, 3)
        10441(3035, 3296, 0, SOUTH, 0)
        COW(3037, 3306, 0, SOUTH, 3)
        SARAH(3038, 3292, 0, SOUTH, 0)
        1172(3040, 3301, 0, SOUTH, 0)
        COW_2791(3040, 3310, 0, SOUTH, 4)
        COW_CALF_2801(3042, 3311, 0, SOUTH, 4)
        TOOL_LEPRECHAUN(3053, 3305, 0, SOUTH, 0)
        ELSTAN(3053, 3308, 0, SOUTH, 3)
        ROOSTER_2803(3014, 3292, 0, SOUTH, 0)
    }
}