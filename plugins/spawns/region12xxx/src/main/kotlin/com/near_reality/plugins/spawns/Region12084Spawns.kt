package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12084Spawns : NPCSpawnsScript() {
    init {
        TOOL_LEPRECHAUN(3008, 3370, 0, SOUTH, 0)
        BANKER_3094(3010, 3353, 0, NORTH, 0)
        6707(3010, 3379, 0, SOUTH, 8)
        BANKER_3094(3011, 3353, 0, NORTH, 0)
        GARDENER_3276(3011, 3386, 0, SOUTH, 4)
        DWARF_295(3012, 3337, 0, SOUTH, 5)
        BANKER_3094(3012, 3353, 0, NORTH, 0)
        BANKER_3094(3013, 3353, 0, NORTH, 0)
        BANKER_3094(3014, 3353, 0, NORTH, 0)
        6523(3015, 3353, 0, NORTH, 0)
        DWARF_7712(3017, 3340, 0, SOUTH, 2)
        GARDEN_SUPPLIER(3017, 3374, 0, SOUTH, 3)
        GADRIN(3018, 3337, 0, SOUTH, 2)
        DWARF_296(3019, 3333, 0, SOUTH, 3)
        GARDENER(3019, 3370, 0, SOUTH, 5)
        DWARF_7713(3021, 3338, 0, SOUTH, 2)
        10633(3022, 3342, 0, SOUTH, 5)
        6707(3023, 3339, 0, SOUTH, 8)
        DWARF_294(3025, 3347, 0, SOUTH, 3)
        6707(3025, 3360, 0, SOUTH, 8)
        WYSON_THE_GARDENER(3027, 3379, 0, SOUTH, 0)
        MAN_3264(3029, 3352, 0, SOUTH, 3)
        GUARD_3269(3036, 3355, 0, SOUTH, 2)
        LARRY_10418(3038, 3366, 0, SOUTH, 5)
        GUARD_3270(3041, 3355, 0, SOUTH, 2)
        6707(3045, 3339, 0, SOUTH, 8)
        TAU(3047, 3343, 0, SOUTH, 5)
        MAN_3265(3047, 3365, 0, SOUTH, 3)
        PARTY_PETE(3052, 3373, 0, SOUTH, 3)
        6707(3058, 3371, 0, SOUTH, 8)
        DWARF_295(3010, 3341, 1, SOUTH, 5)
        DWARF_296(3017, 3348, 1, SOUTH, 3)
        GUARD_3273(3028, 3329, 1, SOUTH, 4)
        GUARD_3274(3031, 3389, 1, SOUTH, 4)
        NORMAN(3036, 3345, 1, SOUTH, 2)
        GUARD_3274(3038, 3329, 1, SOUTH, 4)
        GUARD_3273(3039, 3388, 1, SOUTH, 4)
        MEGAN(3043, 3373, 1, SOUTH, 2)
        LUCY(3046, 3383, 1, SOUTH, 3)
        GUARD_3274(3049, 3389, 1, SOUTH, 4)
        GUARD_3273(3051, 3329, 1, SOUTH, 4)
        6110(3053, 3378, 1, SOUTH, 2)
        GUARD_3273(3056, 3389, 1, SOUTH, 4)
        GUARD_3274(3059, 3330, 1, SOUTH, 4)
        GUARD_3274(3064, 3384, 1, SOUTH, 4)
    }
}