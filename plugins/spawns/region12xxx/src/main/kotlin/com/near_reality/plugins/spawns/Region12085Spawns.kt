package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12085Spawns : NPCSpawnsScript() {
    init {
        DWARF(3008, 3436, 0, SOUTH, 13)
        ENGINEER(3008, 3442, 0, SOUTH, 0)
        NULODION(3011, 3453, 0, SOUTH, 3)
        BLACK_GUARD(3012, 3444, 0, SOUTH, 6)
        ENGINEERING_ASSISTANT_1414(3013, 3449, 0, SOUTH, 4)
        ENGINEERING_ASSISTANT(3013, 3451, 0, SOUTH, 4)
        BLACK_GUARD_1412(3014, 3447, 0, SOUT<PERSON>, 7)
        DWARF(3015, 3435, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        BLACK_GUARD_1410(3015, 3445, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        DWARF_1403(3016, 3448, 0, <PERSON><PERSON>UT<PERSON>, 6)
        DWARF_1401(3016, 3450, 0, SOUTH, 6)
        DWARF_1404(3017, 3447, 0, SOUTH, 4)
        BLACK_GUARD_1411(3019, 3446, 0, SOUTH, 5)
        DWARF(3021, 3437, 0, SOUTH, 13)
        DWARF_1402(3021, 3447, 0, SOUTH, 3)
        ROLAD(3022, 3452, 0, SOUTH, 2)
        SQUIRREL_1417(3026, 3432, 0, SOUTH, 9)
        DWARF(3029, 3445, 0, SOUTH, 13)
        SQUIRREL_1418(3033, 3436, 0, SOUTH, 10)
        SQUIRREL(3038, 3428, 0, SOUTH, 8)
        RACCOON_1420(3039, 3449, 0, SOUTH, 12)
        RACCOON_1421(3040, 3440, 0, SOUTH, 11)
        SQUIRREL_1417(3044, 3423, 0, SOUTH, 9)
        RACCOON(3044, 3447, 0, SOUTH, 9)
        SQUIRREL_1418(3048, 3418, 0, SOUTH, 10)
        SQUIRREL(3049, 3427, 0, SOUTH, 8)
        SQUIRREL_1417(3054, 3418, 0, SOUTH, 9)
        RACCOON_1420(3062, 3439, 0, SOUTH, 12)
        SQUIRREL(3063, 3422, 0, SOUTH, 8)
        RACCOON(3064, 3444, 0, SOUTH, 9)
        RACCOON_1421(3065, 3435, 0, SOUTH, 11)
    }
}