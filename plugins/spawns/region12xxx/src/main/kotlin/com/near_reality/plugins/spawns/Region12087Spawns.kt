package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12087Spawns : NPCSpawnsScript() {
    init {
        SCORPION_3024(3025, 3568, 0, SOUTH, 12)
        SCORPION_3024(3028, 3577, 0, SOUTH, 12)
        SCORPION_3024(3032, 3570, 0, SOUTH, 12)
        SCORPION_3024(3040, 3581, 0, SOUTH, 12)
        SCORPION_3024(3041, 3572, 0, SOUTH, 12)
        SCORPION_3024(3047, 3567, 0, SOUTH, 12)
        SCORPION_3024(3055, 3574, 0, SOUT<PERSON>, 12)
        SCORPION_3024(3056, 3566, 0, SOUTH, 12)
        SCORPION_3024(3062, 3555, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        SCORPION_3024(3062, 3570, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
    }
}