package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12093Spawns : NPCSpawnsScript() {
    init {
        DARREN(3016, 3947, 0, SOUT<PERSON>, 2)
        6105(3022, 3946, 0, SOUTH, 2)
        PIRATE_523(3039, 3950, 0, SOUTH, 4)
        PIRATE_523(3039, 3955, 0, SOUTH, 4)
        PIRATE_523(3039, 3958, 0, SOUTH, 4)
        PIRATE_523(3040, 3955, 0, SOUTH, 4)
        PIRATE_523(3041, 3950, 0, SOUTH, 4)
        PIRATE_523(3041, 3951, 0, SOUT<PERSON>, 4)
        PIRATE_523(3041, 3953, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        PIRATE_523(3041, 3956, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        PIRATE_523(3043, 3950, 0, <PERSON><PERSON>UT<PERSON>, 4)
        PIRATE_523(3043, 3951, 0, SOUTH, 4)
        PIRATE_523(3043, 3956, 0, SOUTH, 4)
        PIRATE_523(3043, 3958, 0, SOUTH, 4)
    }
}