package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12102Spawns : NPCSpawnsScript() {
    init {
        CENTAUR_1844(3024, 4506, 0, SOUTH, 5)
        CENTAUR(3025, 4503, 0, SOUTH, 5)
        BUTTERFLY_1854(3025, 4513, 0, SOUTH, 5)
        WOOD_DRYAD(3027, 4520, 0, SOUTH, 5)
        BUTTERFLY_1855(3031, 4493, 0, SOUTH, 5)
        RABBIT_1853(3032, 4505, 0, SOUTH, 5)
        RABBIT(3034, 4505, 0, SOUTH, 5)
        STAG(3037, 4503, 0, SOUTH, 5)
        FISHING_SPOT_6731(3042, 4510, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RABBIT_1853(3045, 4490, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        STAG(3050, 4494, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RABBIT(3059, 4503, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        4541(3051, 4486, 0, <PERSON>O<PERSON>H, 5)
    }
}