package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12106Spawns : NPCSpawnsScript() {
    init {
        EYE(3024, 4770, 0, SOUTH, 0)
        EYE(3027, 4786, 0, SOUTH, 0)
        EYE(3051, 4771, 0, SOUTH, 0)
        EYE(3051, 4790, 0, SOUTH, 0)
    }
}