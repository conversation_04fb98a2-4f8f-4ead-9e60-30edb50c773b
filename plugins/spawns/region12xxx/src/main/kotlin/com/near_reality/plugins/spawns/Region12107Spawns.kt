package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12107Spawns : NPCSpawnsScript() {
    init {
        ABYSSAL_GUARDIAN(3014, 4826, 0, SOUTH, 6)
        ABYSS<PERSON>_LEECH(3014, 4838, 0, SOUT<PERSON>, 6)
        ABYSSAL_LEECH(3015, 4828, 0, SOUTH, 6)
        ABYSSAL_LEECH(3015, 4837, 0, SOUTH, 6)
        ABYSSAL_LEECH(3016, 4824, 0, SOUTH, 6)
        ABYSSAL_LEECH(3016, 4830, 0, SOUTH, 6)
        ABYSSAL_WALKER(3016, 4832, 0, SO<PERSON><PERSON>, 7)
        ABYSSAL_LEECH(3016, 4834, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ABY<PERSON><PERSON>_LEECH(3016, 4838, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ABY<PERSON><PERSON>_LEECH(3017, 4813, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ABYSS<PERSON>_LEECH(3017, 4816, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ABY<PERSON><PERSON>_<PERSON>LKER(3017, 4823, 0, SOUTH, 7)
        ABYSSAL_LEECH(3017, 4825, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3017, 4847, 0, SOUTH, 6)
        ABY<PERSON><PERSON>_LEE<PERSON>(3018, 4841, 0, SOUTH, 6)
        ABYSSAL_LEECH(3018, 4844, 0, SOUTH, 6)
        ABYSSAL_LEECH(3019, 4816, 0, SOUTH, 6)
        ABYSSAL_WALKER(3020, 4817, 0, SOUTH, 7)
        ABYSSAL_LEECH(3020, 4847, 0, SOUTH, 6)
        ABYSSAL_LEECH(3021, 4813, 0, SOUTH, 6)
        ABYSSAL_WALKER(3022, 4849, 0, SOUTH, 7)
        ABYSSAL_WALKER(3023, 4813, 0, SOUTH, 7)
        ABYSSAL_LEECH(3023, 4851, 0, SOUTH, 6)
        ABYSSAL_WALKER(3025, 4811, 0, SOUTH, 7)
        ABYSSAL_LEECH(3025, 4851, 0, SOUTH, 6)
        ABYSSAL_WALKER(3026, 4809, 0, SOUTH, 7)
        ABYSSAL_GUARDIAN(3027, 4808, 0, SOUTH, 6)
        ABYSSAL_WALKER(3028, 4807, 0, SOUTH, 7)
        ABYSSAL_WALKER(3028, 4811, 0, SOUTH, 7)
        ABYSSAL_LEECH(3028, 4854, 0, SOUTH, 6)
        ABYSSAL_LEECH(3029, 4810, 0, SOUTH, 6)
        ABYSSAL_WALKER(3030, 4808, 0, SOUTH, 7)
        ABYSSAL_WALKER(3030, 4856, 0, SOUTH, 7)
        ABYSSAL_WALKER(3031, 4811, 0, SOUTH, 7)
        ABYSSAL_WALKER(3032, 4808, 0, SOUTH, 7)
        ABYSSAL_LEECH(3033, 4854, 0, SOUTH, 6)
        ABYSSAL_WALKER(3034, 4809, 0, SOUTH, 7)
        ABYSSAL_WALKER(3036, 4808, 0, SOUTH, 7)
        ABYSSAL_WALKER(3037, 4810, 0, SOUTH, 7)
        ABYSSAL_WALKER(3038, 4807, 0, SOUTH, 7)
        ABYSSAL_LEECH(3038, 4855, 0, SOUTH, 6)
        DARK_MAGE(3039, 4834, 0, NORTH, 0)
        ABYSSAL_WALKER(3040, 4810, 0, SOUTH, 7)
        ABYSSAL_GUARDIAN(3040, 4857, 0, SOUTH, 6)
        ABYSSAL_LEECH(3043, 4809, 0, SOUTH, 6)
        ABYSSAL_LEECH(3044, 4854, 0, SOUTH, 6)
        ABYSSAL_WALKER(3045, 4811, 0, SOUTH, 7)
        ABYSSAL_WALKER(3047, 4808, 0, SOUTH, 7)
        ABYSSAL_LEECH(3047, 4853, 0, SOUTH, 6)
        ABYSSAL_WALKER(3050, 4811, 0, SOUTH, 7)
        ABYSSAL_GUARDIAN(3050, 4855, 0, SOUTH, 6)
        ABYSSAL_LEECH(3051, 4852, 0, SOUTH, 6)
        ABYSSAL_WALKER(3052, 4811, 0, SOUTH, 7)
        ABYSSAL_WALKER(3052, 4855, 0, SOUTH, 7)
        ABYSSAL_WALKER(3053, 4809, 0, SOUTH, 7)
        ABYSSAL_WALKER(3054, 4813, 0, SOUTH, 7)
        ABYSSAL_LEECH(3055, 4811, 0, SOUTH, 6)
        ABYSSAL_LEECH(3055, 4849, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3056, 4851, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3057, 4817, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3057, 4846, 0, SOUTH, 6)
        ABYSSAL_WALKER(3058, 4810, 0, SOUTH, 7)
        ABYSSAL_WALKER(3058, 4813, 0, SOUTH, 7)
        ABYSSAL_WALKER(3058, 4816, 0, SOUTH, 7)
        ABYSSAL_GUARDIAN(3058, 4848, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3059, 4812, 0, SOUTH, 6)
        ABYSSAL_LEECH(3059, 4817, 0, SOUTH, 6)
        ABYSSAL_WALKER(3059, 4844, 0, SOUTH, 7)
        ABYSSAL_GUARDIAN(3060, 4810, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3060, 4819, 0, SOUTH, 6)
        ABYSSAL_WALKER(3060, 4820, 0, SOUTH, 7)
        ABYSSAL_GUARDIAN(3060, 4824, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3060, 4836, 0, SOUTH, 6)
        ABYSSAL_LEECH(3060, 4838, 0, SOUTH, 6)
        ABYSSAL_WALKER(3061, 4811, 0, SOUTH, 7)
        ABYSSAL_WALKER(3061, 4819, 0, SOUTH, 7)
        ABYSSAL_WALKER(3061, 4828, 0, SOUTH, 7)
        ABYSSAL_GUARDIAN(3061, 4841, 0, SOUTH, 6)
        ABYSSAL_WALKER(3062, 4820, 0, SOUTH, 7)
        ABYSSAL_LEECH(3062, 4837, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3063, 4823, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3064, 4830, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3064, 4840, 0, SOUTH, 6)
        ABYSSAL_LEECH(3066, 4822, 0, SOUTH, 6)
    }
}