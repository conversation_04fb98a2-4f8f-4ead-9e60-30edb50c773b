package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12108Spawns : NPCSpawnsScript() {
    init {
        ABYSSAL_DEMON_415(3016, 4891, 0, SOUTH, 5)
        ABYSS<PERSON>_LEECH(3017, 4903, 0, SOUT<PERSON>, 6)
        ABYSS<PERSON>_LEECH(3017, 4910, 0, SOUTH, 6)
        ABYSSAL_DEMON_415(3018, 4902, 0, SOUTH, 5)
        ABYSSAL_LEECH(3020, 4877, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3020, 4896, 0, SOUTH, 6)
        ABYSSAL_WALKER(3020, 4914, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        ABY<PERSON><PERSON>_LEECH(3021, 4918, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ABY<PERSON>AL_DEMON_415(3023, 4877, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ABYSSAL_DEMON_415(3025, 4916, 0, SOUTH, 5)
        ABYSSAL_GUARDIAN(3027, 4914, 0, SOUTH, 6)
        ABYSSAL_DEMON_415(3029, 4899, 0, SOUTH, 5)
        ABYSSAL_LEECH(3029, 4917, 0, SOUTH, 6)
        ABYSSAL_LEECH(3030, 4876, 0, SOUTH, 6)
        ABYSSAL_WALKER(3031, 4872, 0, SOUTH, 7)
        ABYSSAL_WALKER(3031, 4902, 0, SOUTH, 7)
        ABYSSAL_WALKER(3033, 4886, 0, SOUTH, 7)
        ABYSSAL_DEMON_415(3035, 4883, 0, SOUTH, 5)
        ABYSSAL_DEMON_415(3036, 4905, 0, SOUTH, 5)
        ABYSSAL_LEECH(3036, 4909, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3038, 4906, 0, SOUTH, 6)
        ABYSSAL_LEECH(3040, 4870, 0, SOUTH, 6)
        ABYSSAL_WALKER(3043, 4902, 0, SOUTH, 7)
        ABYSSAL_GUARDIAN(3044, 4884, 0, SOUTH, 6)
        ABYSSAL_LEECH(3045, 4916, 0, SOUTH, 6)
        ABYSSAL_DEMON_415(3046, 4874, 0, SOUTH, 5)
        ABYSSAL_DEMON_415(3046, 4887, 0, SOUTH, 5)
        ABYSSAL_DEMON_415(3048, 4917, 0, SOUTH, 5)
        ABYSSAL_LEECH(3050, 4873, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3052, 4898, 0, SOUTH, 6)
        ABYSSAL_DEMON_415(3053, 4892, 0, SOUTH, 5)
        ABYSSAL_DEMON_415(3056, 4909, 0, SOUTH, 5)
        ABYSSAL_LEECH(3056, 4912, 0, SOUTH, 6)
        ABYSSAL_GUARDIAN(3057, 4908, 0, SOUTH, 6)
        ABYSSAL_LEECH(3061, 4880, 0, SOUTH, 6)
        ABYSSAL_DEMON_415(3061, 4882, 0, SOUTH, 5)
        ABYSSAL_WALKER(3061, 4915, 0, SOUTH, 7)
        ABYSSAL_WALKER(3063, 4885, 0, SOUTH, 7)
        ABYSSAL_LEECH(3064, 4901, 0, SOUTH, 6)
    }
}