package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12115Spawns : NPCSpawnsScript() {
    init {
        GORAK(3023, 5339, 0, SOUTH, 5)
        GORAK(3023, 5350, 0, SOUTH, 5)
        GORAK(3033, 5343, 0, SOUTH, 5)
        GORAK(3035, 5350, 0, SOUTH, 5)
        GORAK(3037, 5330, 0, SOUTH, 5)
        GORAK(3038, 5344, 0, SOUTH, 5)
        GORAK(3038, 5361, 0, SOUTH, 5)
        GORAK(3040, 5351, 0, SOUTH, 5)
        GORAK(3042, 5347, 0, SOUTH, 5)
        GORAK(3052, 5344, 0, SOUTH, 5)
        GORAK(3052, 5353, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
    }
}