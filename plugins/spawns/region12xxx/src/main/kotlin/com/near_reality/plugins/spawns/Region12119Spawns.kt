package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12119Spawns : NPCSpawnsScript() {
    init {
        CAVE_LIZARD(3010, 5574, 0, SOUTH, 10)
        CAVE_LIZARD(3010, 5604, 0, SOUTH, 10)
        ZAMORAK_RANGER(3011, 5596, 0, SOUTH, 7)
        ZAMORAK_MAGE_7423(3012, 5578, 0, SOUTH, 4)
        CAVE_LIZARD(3013, 5574, 0, SOUTH, 10)
        ENIOLA(3013, 5625, 0, SOUTH, 5)
        CAVE_LIZARD(3014, 5592, 0, SOUT<PERSON>, 10)
        ZAMORAK_CRAFTER(3014, 5624, 0, <PERSON><PERSON><PERSON><PERSON>, 16)
        CAVE_LIZARD(3015, 5572, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        ZAMORAK_WARRIOR(3015, 5583, 0, <PERSON><PERSON>UT<PERSON>, 6)
        ZAMORAK_WARRIOR_7419(3015, 5606, 0, SOUTH, 4)
        ZAMORAK_MAGE(3017, 5577, 0, SOUTH, 6)
        CAVE_LIZARD(3028, 5571, 0, SOUTH, 10)
        ZAMORAK_WARRIOR(3028, 5574, 0, SOUTH, 6)
        ZAMORAK_RANGER_7421(3031, 5574, 0, SOUTH, 2)
        CAVE_LIZARD(3032, 5570, 0, SOUTH, 10)
        CAVE_LIZARD(3033, 5573, 0, SOUTH, 10)
        ZAMORAK_MAGE(3035, 5581, 0, SOUTH, 6)
        ZAMORAK_RANGER(3045, 5576, 0, SOUTH, 7)
        CAVE_LIZARD(3045, 5579, 0, SOUTH, 10)
        ZAMORAK_CRAFTER_7427(3058, 5579, 0, SOUTH, 23)
        JARDRIC_8155(3065, 5573, 0, WEST,0)
    }
}