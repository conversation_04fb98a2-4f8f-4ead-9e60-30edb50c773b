package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12122Spawns : NPCSpawnsScript() {
    init {
        RUBBLE_10690(3014, 5777, 0, SOUTH, 5)
        RUBBLE_10690(3015, 5772, 0, SOUTH, 5)
        RUBBLE_10692(3015, 5808, 0, SOUTH, 5)
        10706(3017, 5779, 0, SOUTH, 5)
        RUBBLE_10690(3017, 5784, 0, SOUTH, 5)
        RUBBLE_10692(3017, 5810, 0, SOUTH, 5)
        RUBBLE_10692(3021, 5812, 0, SOUTH, 5)
        RUBBLE_10690(3022, 5772, 0, SOUTH, 5)
        RUBBLE_10692(3022, 5808, 0, SOUTH, 5)
        RUBBLE_10690(3024, 5785, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RUBBLE_10690(3025, 5774, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RUBBLE_10690(3025, 5780, 0, SOUTH, 5)
        RUBBLE_10692(3025, 5802, 0, SOUTH, 5)
        RUBBLE_10692(3025, 5805, 0, SOUTH, 5)
    }
}