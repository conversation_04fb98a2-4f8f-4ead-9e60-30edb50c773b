package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12181Spawns : NPCSpawnsScript() {
    init {
        HOBGOBLIN_3049(3010, 9578, 0, SOUTH, 13)
        HOBGOBLIN_3049(3010, 9593, 0, SOUTH, 13)
        HOBGOBLIN_3049(3010, 9595, 0, SOUTH, 13)
        HOBGOBLIN_3049(3011, 9579, 0, SOUTH, 13)
        HOBGOBLIN_3049(3016, 9578, 0, SOUTH, 13)
        HOBGOBLIN_3049(3016, 9594, 0, SOUTH, 13)
        HOBGOBLIN_3050(3019, 9591, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        SKELETAL_WYVERN(3025, 9543, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETAL_WYVERN_466(3025, 9553, 0, SOUTH, 2)
        SKELETAL_WYVERN_467(3033, 9554, 0, SOUTH, 6)
        SKELETAL_WYVERN_468(3039, 9541, 0, SOUTH, 4)
        ICE_WARRIOR(3044, 9581, 0, SOUTH, 6)
        SKELETAL_WYVERN_467(3045, 9553, 0, SOUTH, 6)
        ICE_WARRIOR(3046, 9575, 0, SOUTH, 6)
        ICE_WARRIOR(3048, 9583, 0, SOUTH, 6)
        ICE_WARRIOR(3049, 9590, 0, SOUTH, 6)
        ICE_WARRIOR(3053, 9576, 0, SOUTH, 6)
        ICE_WARRIOR_2842(3056, 9572, 0, SOUTH, 7)
        ICE_WARRIOR(3056, 9583, 0, SOUTH, 6)
        ICE_WARRIOR(3056, 9587, 0, SOUTH, 6)
        SKELETAL_WYVERN_466(3057, 9539, 0, SOUTH, 2)
        ICE_GIANT_2089(3057, 9573, 0, SOUTH, 4)
        ICE_WARRIOR_2842(3060, 9572, 0, SOUTH, 7)
        ICE_GIANT_2088(3061, 9573, 0, SOUTH, 2)
        ICE_WARRIOR(3062, 9576, 0, SOUTH, 6)
        ICE_WARRIOR(3062, 9581, 0, SOUTH, 6)
        SKELETAL_WYVERN(3063, 9554, 0, SOUTH, 7)
        ICE_GIANT_2089(3065, 9571, 0, SOUTH, 4)
        SKELETAL_WYVERN_468(3066, 9545, 0, SOUTH, 4)
        SKELETAL_WYVERN(3040, 9569, 0, SOUTH, 7)
        SKELETAL_WYVERN_466(3042, 9562, 0, SOUTH, 2)
        SKELETAL_WYVERN_467(3047, 9560, 0, SOUTH, 6)
        1427(3058, 9558, 0, SOUTH, 0)
        SKELETAL_WYVERN_468(3066, 9562, 0, SOUTH, 4)
        SKELETAL_WYVERN_466(3070, 9560, 0, SOUTH, 2)
        SKELETAL_WYVERN(3070, 9566, 0, SOUTH, 7)
        SKELETAL_WYVERN_468(3070, 9586, 0, SOUTH, 4)
        SKELETAL_WYVERN_467(3071, 9554, 0, SOUTH, 6)
        SKELETAL_WYVERN_466(3071, 9574, 0, SOUTH, 2)
        SKELETAL_WYVERN_467(3071, 9581, 0, SOUTH, 6)
    }
}