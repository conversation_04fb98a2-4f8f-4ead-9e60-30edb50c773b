package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12184Spawns : NPCSpawnsScript() {
    init {
        DWARF_7714(3020, 9734, 0, SOUTH, 2)
        YARSUL(3030, 9746, 0, SOUTH, 2)
        HENDOR(3032, 9748, 0, SOUTH, 2)
        SCORPION_3024(3038, 9771, 0, SOUTH, 12)
        SCORPION_3024(3039, 9790, 0, SOUTH, 12)
        KING_SCORPION(3040, 9775, 0, SOUTH, 8)
        SCORPION_3024(3041, 9781, 0, SOUTH, 12)
        DWARF_7715(3044, 9730, 0, SOUTH, 2)
        SCORPION_3024(3044, 9787, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        DWARF_7712(3045, 9758, 0, <PERSON>OUT<PERSON>, 2)
        SCORPION_3024(3045, 9777, 0, SOUTH, 12)
        SCORPION_3024(3046, 9767, 0, SOUTH, 12)
        SCORPION_3024(3047, 9784, 0, SOUTH, 12)
        KING_SCORPION(3048, 9770, 0, SOUTH, 8)
        SCORPION_3024(3049, 9777, 0, SOUTH, 12)
        SCORPION_3024(3054, 9773, 0, SOUTH, 12)
        SCORPION_3024(3056, 9778, 0, SOUTH, 12)
        GUARD_6561(3059, 9768, 0, SOUTH, 2)
    }
}