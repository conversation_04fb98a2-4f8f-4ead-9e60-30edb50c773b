package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12185Spawns : NPCSpawnsScript() {
    init {
        DWARF(3010, 9813, 0, SOUTH, 13)
        DWARF(3013, 9811, 0, SOUTH, 13)
        DWARF(3014, 9814, 0, SOUTH, 13)
        DWARF(3017, 9819, 0, SOUTH, 13)
        DWARF(3017, 9851, 0, SOUTH, 13)
        DWARF(3018, 9809, 0, SOUTH, 13)
        DWARF(3018, 9814, 0, SOUTH, 13)
        DWARF(3018, 9835, 0, SOUTH, 13)
        DWARF(3019, 9845, 0, SOUTH, 13)
        DWARF(3020, 9849, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        DWARF(3022, 9851, 0, SOUTH, 13)
        DWARF(3023, 9820, 0, SOUTH, 13)
        DWARF(3025, 9828, 0, SOUTH, 13)
        DWARF(3026, 9833, 0, SOUTH, 13)
        RAT_2854(3026, 9837, 0, SOUTH, 14)
        DWARF(3028, 9816, 0, SOUTH, 13)
        RAT_2854(3033, 9827, 0, SOUTH, 14)
        DWARF(3034, 9848, 0, SOUTH, 13)
        DROGO_DWARF(3036, 9846, 0, SOUTH, 7)
        DWARF(3037, 9844, 0, SOUTH, 13)
        SCORPION_3024(3039, 9802, 0, SOUTH, 12)
        SCORPION_3024(3042, 9793, 0, SOUTH, 12)
        SCORPION_3024(3042, 9800, 0, SOUTH, 12)
        RAT_2854(3042, 9818, 0, SOUTH, 14)
        RAT_2854(3043, 9815, 0, SOUTH, 14)
        DWARF(3044, 9830, 0, SOUTH, 13)
        DWARF(3048, 9823, 0, SOUTH, 13)
        DWARF(3050, 9813, 0, SOUTH, 13)
        DWARF(3050, 9827, 0, SOUTH, 13)
        DWARF(3051, 9817, 0, SOUTH, 13)
        DWARF(3053, 9826, 0, SOUTH, 13)
        DWARF(3054, 9821, 0, SOUTH, 13)
    }
}