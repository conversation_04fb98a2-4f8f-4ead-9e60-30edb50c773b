package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12190Spawns : NPCSpawnsScript() {
    init {
        AVIANSIE_3179(3018, 10155, 0, SOUTH, 9)
        AVIANSIE_3182(3019, 10149, 0, SOUTH, 5)
        SPIRITUAL_MAGE_3161(3020, 10159, 0, SOUTH, 8)
        SPIRITUAL_WARRIOR(3022, 10122, 0, SOUTH, 7)
        SPIRITUAL_WARRIOR_3159(3022, 10125, 0, SOUTH, 7)
        SPIRITUAL_RANGER_3167(3022, 10147, 0, SOUTH, 6)
        AVIANSIE_3175(3022, 10162, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        KNIGHT_OF_SARADOMIN(3023, 10127, 0, <PERSON>O<PERSON><PERSON>, 7)
        SPIRITUAL_RANGER_3160(3023, 10157, 0, SOUTH, 7)
        SPIRITUAL_RANGER_3160(3024, 10120, 0, SOUTH, 7)
        SPIRITUAL_RANGER(3025, 10124, 0, SOUTH, 8)
        AVIANSIE_3171(3025, 10152, 0, SOUTH, 6)
        SPIRITUAL_MAGE_3168(3026, 10142, 0, SOUTH, 2)
        AVIANSIE_3183(3026, 10146, 0, SOUTH, 7)
        SPIRITUAL_WARRIOR_3166(3026, 10156, 0, SOUTH, 5)
        AVIANSIE(3026, 10163, 0, SOUTH, 8)
        SPIRITUAL_WARRIOR(3027, 10118, 0, SOUTH, 7)
        SPIRITUAL_MAGE_3161(3027, 10124, 0, SOUTH, 8)
        AVIANSIE_3177(3027, 10159, 0, SOUTH, 10)
        SPIRITUAL_RANGER(3028, 10121, 0, SOUTH, 8)
        KNIGHT_OF_SARADOMIN_2214(3028, 10130, 0, SOUTH, 6)
        SPIRITUAL_MAGE(3029, 10127, 0, SOUTH, 6)
        BLOODVELD_3138(3030, 10125, 0, SOUTH, 7)
        KNIGHT_OF_SARADOMIN(3031, 10123, 0, SOUTH, 7)
        SPIRITUAL_RANGER_3167(3031, 10148, 0, SOUTH, 6)
        AVIANSIE_3181(3031, 10155, 0, SOUTH, 9)
        KNIGHT_OF_SARADOMIN_2214(3032, 10125, 0, SOUTH, 6)
        BLOODVELD_3138(3032, 10130, 0, SOUTH, 7)
        SPIRITUAL_MAGE_3168(3032, 10152, 0, SOUTH, 2)
        SPIRITUAL_MAGE_3161(3034, 10144, 0, SOUTH, 8)
        BLOODVELD_3138(3036, 10139, 0, SOUTH, 7)
        FERAL_VAMPYRE(3036, 10141, 0, SOUTH, 8)
        SPIRITUAL_MAGE_3161(3037, 10125, 0, SOUTH, 8)
        FERAL_VAMPYRE(3037, 10138, 0, SOUTH, 8)
        SPIRITUAL_WARRIOR_3159(3037, 10144, 0, SOUTH, 7)
        SPIRITUAL_WARRIOR_3166(3037, 10151, 0, SOUTH, 5)
        ICEFIEND(3038, 10130, 0, SOUTH, 6)
        PYREFIEND_3139(3039, 10133, 0, SOUTH, 4)
        SPIRITUAL_WARRIOR_3159(3039, 10138, 0, SOUTH, 7)
        SPIRITUAL_MAGE(3040, 10136, 0, SOUTH, 6)
        IMP(3040, 10142, 0, SOUTH, 5)
        ORK_2237(3040, 10147, 0, SOUTH, 7)
        SPIRITUAL_MAGE_3161(3040, 10152, 0, SOUTH, 8)
        BLOODVELD_3138(3041, 10139, 0, SOUTH, 7)
        ICEFIEND(3042, 10128, 0, SOUTH, 6)
        PYREFIEND_3139(3042, 10131, 0, SOUTH, 4)
        SPIRITUAL_WARRIOR_2243(3042, 10134, 0, SOUTH, 6)
        SPIRITUAL_WARRIOR_3159(3042, 10145, 0, SOUTH, 7)
        ORK_2240(3043, 10124, 0, SOUTH, 6)
        SPIRITUAL_RANGER_3160(3043, 10136, 0, SOUTH, 7)
        SPIRITUAL_MAGE_3161(3043, 10143, 0, SOUTH, 8)
        ORK_2240(3045, 10129, 0, SOUTH, 6)
        WEREWOLF_3135(3045, 10139, 0, SOUTH, 7)
        SPIRITUAL_RANGER_3160(3045, 10142, 0, SOUTH, 7)
        IMP(3046, 10144, 0, SOUTH, 5)
        GOBLIN_2248(3047, 10126, 0, SOUTH, 6)
        SPIRITUAL_RANGER_3160(3047, 10138, 0, SOUTH, 7)
        SPIRITUAL_MAGE_2244(3048, 10123, 0, SOUTH, 4)
        BLOODVELD_3138(3048, 10141, 0, SOUTH, 7)
        ORK_2238(3049, 10132, 0, SOUTH, 6)
        ORK_2238(3050, 10122, 0, SOUTH, 6)
        GOBLIN_2249(3050, 10129, 0, SOUTH, 2)
        WEREWOLF_3136(3050, 10139, 0, SOUTH, 7)
        SPIRITUAL_MAGE_2244(3051, 10125, 0, SOUTH, 4)
        GORAK_3141(3052, 10128, 0, SOUTH, 6)
        SPIRITUAL_WARRIOR_3159(3053, 10122, 0, SOUTH, 7)
        IMP(3053, 10141, 0, SOUTH, 5)
        ORK_2237(3054, 10120, 0, SOUTH, 7)
        SPIRITUAL_WARRIOR_2243(3054, 10124, 0, SOUTH, 6)
        GORAK_3141(3054, 10131, 0, SOUTH, 6)
        OGRE_2233(3054, 10136, 0, SOUTH, 2)
        SPIRITUAL_RANGER_2242(3056, 10128, 0, SOUTH, 10)
        ORK_2237(3057, 10123, 0, SOUTH, 7)
        SPIRITUAL_RANGER_3160(3058, 10129, 0, SOUTH, 7)
        SPIRITUAL_RANGER_2242(3058, 10131, 0, SOUTH, 10)
        ORK_2237(3060, 10127, 0, SOUTH, 7)
        BOULDER_6621(3053, 10165, 3, SOUTH, 5)
    }
}