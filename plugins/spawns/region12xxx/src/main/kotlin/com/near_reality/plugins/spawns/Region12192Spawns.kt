package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12192Spawns : NPCSpawnsScript() {
    init {
        GREATER_DEMON_2029(3028, 10250, 0, SOUTH, 7)
        GREATER_DEMON_2028(3030, 10259, 0, SOUTH, 4)
        GREATER_DEMON_2027(3035, 10245, 0, SOUTH, 2)
        POISON_SPIDER(3037, 10263, 0, SOUTH, 11)
        POISON_SPIDER(3043, 10262, 0, <PERSON>OUT<PERSON>, 11)
        POISON_SPIDER(3044, 10252, 0, SOUTH, 11)
        BLACK_DRAGON_255(3048, 10266, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        POISON_SPIDER(3051, 10257, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        BLACK_DRAGON_256(3054, 10269, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        POISON_SPIDER(3055, 10253, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        POISON_SPIDER(3067, 10255, 0, SOUTH, 11)
        POISON_SPIDER(3067, 10257, 0, SOUTH, 11)
        POISON_SPIDER(3068, 10253, 0, SOUTH, 11)
        POISON_SPIDER(3069, 10257, 0, SOUTH, 11)
        POISON_SPIDER(3069, 10259, 0, SOUTH, 11)
    }
}