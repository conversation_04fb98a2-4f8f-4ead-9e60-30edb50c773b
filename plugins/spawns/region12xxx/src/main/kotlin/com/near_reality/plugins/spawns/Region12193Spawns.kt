package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12193Spawns : NPCSpawnsScript() {
    init {
        SHADOW_SPIDER(3015, 10318, 0, SOUTH, 12)
        SHADOW_SPIDER(3016, 10323, 0, SOUTH, 12)
        SHADOW_SPIDER(3019, 10313, 0, SOUTH, 12)
        SHADOW_SPIDER(3020, 10322, 0, SOUTH, 12)
        SHADOW_SPIDER(3020, 10329, 0, SOUTH, 12)
        MONK_OF_ZAMORAK_8698(3020, 10355, 0, SOUTH, 5)
        SHADOW_SPIDER(3025, 10330, 0, SOUTH, 12)
        SHADOW_SPIDER(3026, 10339, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        SHADOW_SPIDER(3028, 10327, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        CHAOS_DWARF(3029, 10313, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        SHADOW_SPIDER(3029, 10334, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        CHAOS_DWARF(3030, 10308, 0, SOUTH, 14)
        SHADOW_SPIDER(3030, 10346, 0, SOUTH, 12)
        SHADOW_SPIDER(3032, 10342, 0, SOUTH, 12)
        CHAOS_DWARF(3034, 10310, 0, SOUTH, 14)
        CHAOS_DWARF(3036, 10311, 0, SOUTH, 14)
        SHADOW_SPIDER(3039, 10343, 0, SOUTH, 12)
        HILL_GIANT_2099(3042, 10317, 0, SOUTH, 3)
        HILL_GIANT_2102(3044, 10306, 0, SOUTH, 3)
        HILL_GIANT_2103(3045, 10308, 0, SOUTH, 3)
        HILL_GIANT_2101(3045, 10311, 0, SOUTH, 3)
        HILL_GIANT(3045, 10321, 0, SOUTH, 3)
        HILL_GIANT_2100(3046, 10315, 0, SOUTH, 3)
        FIRE_GIANT_2078(3046, 10341, 0, SOUTH, 6)
        FIRE_GIANT_2079(3047, 10344, 0, SOUTH, 2)
        FIRE_GIANT_2080(3049, 10338, 0, SOUTH, 4)
        FIRE_GIANT_2078(3052, 10347, 0, SOUTH, 6)
        FIRE_GIANT_2079(3053, 10338, 0, SOUTH, 2)
        MONK_OF_ZAMORAK_8698(3060, 10333, 0, SOUTH, 5)
    }
}