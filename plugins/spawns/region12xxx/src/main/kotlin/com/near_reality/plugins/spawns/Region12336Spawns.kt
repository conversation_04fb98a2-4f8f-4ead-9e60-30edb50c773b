package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12336Spawns : NPCSpawnsScript() {
    init {
        MASTER_CHEF(3075, 3085, 0, SOUTH, 5)
        BUTTERFLY(3082, 3077, 0, SOUTH, 7)
        QUEST_GUIDE(3085, 3122, 0, SOUTH, 5)
        BUTTERFLY(3092, 3082, 0, SOUTH, 7)
        BUTTERFLY_236(3093, 3086, 0, SOUTH, 9)
        EXILES_GUIDE(3094, 3107, 0, SOUTH, 0)
        BUTTERFLY_236(3097, 3085, 0, SOUTH, 9)
        FISHING_SPOT_3317(3099, 3090, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        FISHING_SPOT_3317(3101, 3092, 0, <PERSON>O<PERSON><PERSON>, 5)
        FISHING_SPOT_3317(3103, 3092, 0, SOUTH, 5)
        SURVIVAL_EXPERT(3103, 3095, 0, SOUTH, 5)
        BUTTERFLY(3109, 3090, 0, SOUTH, 7)
        BUTTERFLY(3112, 3093, 0, SOUTH, 7)
        BANKER_3318(3120, 3125, 0, SOUTH, 5)
        BANKER_3318(3122, 3125, 0, SOUTH, 5)
        BUTTERFLY_235(3125, 3084, 0, SOUTH, 7)
        BROTHER_BRACE(3125, 3106, 0, SOUTH, 5)
        ACCOUNT_GUIDE(3127, 3124, 0, SOUTH, 5)
        7942(3128, 3084, 0, SOUTH, 5)
        BUTTERFLY_236(3129, 3091, 0, SOUTH, 9)
        BUTTERFLY_236(3135, 3084, 0, SOUTH, 9)
    }
}