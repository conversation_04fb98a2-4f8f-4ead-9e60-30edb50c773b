package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12337Spawns : NPCSpawnsScript() {
    init {
        WIZARD_3257(3107, 3158, 0, SOUTH, 9)
        WIZARD_3257(3110, 3158, 0, SOUTH, 9)
        WIZARD_3257(3111, 3166, 0, SOUTH, 9)
        WIZARD_3257(3111, 3168, 0, SOUTH, 9)
        6108(3112, 3158, 0, SOUTH, 2)
        PROFESSOR_ONGLEWIP(3115, 3160, 0, SOUTH, 9)
        WIZARD_3257(3107, 3157, 1, SOUTH, 9)
        WIZARD_JALARAST(3108, 3161, 1, SOUTH, 2)
        5931(3108, 3164, 1, SOUTH, 0)
        WIZARD_3257(3112, 3157, 1, SOUTH, 9)
        WIZARD_TRAIBORN(3112, 3162, 1, WEST, 0)
        5005(3103, 3163, 2, WEST, 0)
        LESSER_DEMON(3110, 3157, 2, SOUTH, 4)
        WIZARD_GRAYZAG(3110, 3161, 2, SOUTH, 2)
    }
}