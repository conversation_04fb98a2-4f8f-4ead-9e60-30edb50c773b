package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12338Spawns : NPCSpawnsScript() {
    init {
        IMP_5007(3073, 3250, 0, SOUTH, 100)
        PIG_2797(3075, 3259, 0, SOUTH, 3)
        PIG(3075, 3261, 0, SOUTH, 3)
        PIGLET(3076, 3261, 0, SOUTH, 2)
        PIG(3077, 3259, 0, SOUTH, 3)
        PIG_2797(3077, 3260, 0, SOUTH, 3)
        OLIVIA(3078, 3251, 0, SOUTH, 3)
        MARTIN_THE_MASTER_GARDENER(3078, 3258, 0, SOUTH, 4)
        GULL_4290(3079, 3229, 0, <PERSON>O<PERSON><PERSON>, 3)
        TOWN_CRIER_277(3079, 3249, 0, <PERSON><PERSON>UT<PERSON>, 2)
        PIGLET_2800(3079, 3260, 0, SOUTH, 2)
        PIGLET_2799(3079, 3261, 0, SOUTH, 2)
        MASTER_FARMER(3080, 3250, 0, SOUTH, 5)
        DIANGO(3081, 3247, 0, SOUTH, 2)
        MARKET_GUARD_5732(3083, 3247, 0, SOUTH, 5)
        MARKET_GUARD_5732(3083, 3253, 0, SOUTH, 5)
        DARK_WIZARD_512(3084, 3236, 0, SOUTH, 4)
        GULL_4289(3085, 3219, 0, SOUTH, 3)
        FISHING_SPOT_1525(3085, 3230, 0, SOUTH, 0)
        DARK_WIZARD_512(3085, 3238, 0, SOUTH, 4)
        FORTUNATO(3085, 3251, 0, SOUTH, 2)
        FISHING_SPOT_1525(3086, 3227, 0, SOUTH, 0)
        AGGIE(3086, 3259, 0, SOUTH, 3)
        BANKER_1613(3088, 3242, 0, SOUTH, 0)
        BANK_GUARD(3088, 3247, 0, SOUTH, 5)
        2109(3088, 3255, 0, SOUTH, 0)
        BANKER_1618(3090, 3242, 0, EAST, 0)
        BANKER_1618(3090, 3243, 0, EAST, 0)
        BANKER_1613(3090, 3245, 0, EAST, 0)
        9392(3093, 3238, 0, SOUTH, 0)
        MISS_SCHISM(3095, 3252, 0, SOUTH, 0)
        TWIGGY_OKORN(3096, 3227, 0, SOUTH, 2)
        SQUIRREL_5528(3097, 3227, 0, SOUTH, 2)
        NED(3100, 3258, 0, SOUTH, 3)
        BLACK_KNIGHT(3101, 3220, 0, SOUTH, 5)
        JAIL_GUARD_4277(3109, 3237, 0, SOUTH, 4)
        LEELA(3113, 3263, 0, SOUTH, 3)
        JAIL_GUARD_4278(3120, 3238, 0, SOUTH, 4)
        JAIL_GUARD_4279(3121, 3249, 0, SOUTH, 3)
        4282(3123, 3242, 0, SOUTH, 2)
        4275(3123, 3245, 0, SOUTH, 3)
        JAIL_GUARD(3127, 3248, 0, SOUTH, 2)
        4281(3128, 3244, 0, SOUTH, 4)
        BED(3087, 3254, 1, SOUTH, 0)
    }
}