package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12339Spawns : NPCSpawnsScript() {
    init {
        10440(3091, 3266, 0, SOUTH, 5)
        MORGAN(3098, 3268, 0, SOUTH, 2)
        RAT_2854(3100, 3273, 0, SOUTH, 14)
        RAT_2854(3100, 3277, 0, SOUTH, 14)
        MAN_3106(3100, 3279, 0, SOUTH, 5)
        GOBLIN_3034(3110, 3281, 0, SOUTH, 14)
        HIGHWAYMAN(3110, 3295, 0, SOUTH, 12)
        GOBLIN_3035(3114, 3310, 0, <PERSON>OUT<PERSON>, 11)
        FARMER_3251(3119, 3276, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        WOMAN_3111(3097, 3269, 1, <PERSON><PERSON>UTH, 5)
    }
}