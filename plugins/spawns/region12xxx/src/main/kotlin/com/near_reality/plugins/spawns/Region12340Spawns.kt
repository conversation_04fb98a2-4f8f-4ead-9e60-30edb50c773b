package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12340Spawns : NPCSpawnsScript() {
    init {
        6132(3072, 3336, 0, SOUTH, 0)
        GIANT_SPIDER_3017(3075, 3366, 0, SOUTH, 10)
        TOOL_LEPRECHAUN(3087, 3352, 0, SOUTH, 0)
        4408(3093, 3357, 0, EAST, 0)
        RAT_2854(3099, 3366, 0, SOUTH, 14)
        4410(3099, 3370, 0, SOUTH, 2)
        RAT_2854(3101, 3355, 0, SOUTH, 14)
        4418(3103, 3347, 0, EAST, 0)
        4418(3107, 3337, 0, EAST, 0)
        4418(3107, 3342, 0, EAST, 0)
        4418(3107, 3344, 0, EAST, 0)
        4418(3108, 3346, 0, EAST, 0)
        VERONICA(3110, 3330, 0, SOUTH, 2)
        SKELETON_3565(3110, 3367, 0, SOUTH, 2)
        4418(3111, 3339, 0, EAST, 0)
        4418(3111, 3348, 0, EAST, 0)
        4418(3115, 3344, 0, EAST, 0)
        4418(3120, 3344, 0, EAST, 0)
        4418(3122, 3335, 0, EAST, 0)
        GIANT_SPIDER_3017(3135, 3340, 0, SOUTH, 10)
        GHOST_473(3094, 3356, 1, SOUTH, 5)
        GHOST_474(3098, 3359, 1, SOUTH, 3)
        GHOST_505(3101, 3356, 1, SOUTH, 12)
        GHOST_506(3108, 3358, 1, SOUTH, 5)
        GHOST_507(3123, 3356, 1, SOUTH, 5)
        2831(3110, 3366, 2, SOUTH, 5)
        PROFESSOR_ODDENSTEIN(3110, 3367, 2, SOUTH, 2)
    }
}