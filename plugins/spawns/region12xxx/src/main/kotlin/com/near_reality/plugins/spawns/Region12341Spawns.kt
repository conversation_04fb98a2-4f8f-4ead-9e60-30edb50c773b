package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12341Spawns : NPCSpawnsScript() {
    init {
        BARBARIAN_3071(3075, 3420, 0, SOUTH, 9)
        ATLAS(3075, 3439, 0, SOUTH, 5)
        BARBARIAN_3062(3075, 3445, 0, SOUTH, 4)
        BARBARIAN_3064(3076, 3414, 0, SOUTH, 6)
        PEKSA(3076, 3429, 0, SOUTH, 2)
        BARBARIAN_3061(3078, 3419, 0, SOUTH, 10)
        BARBARIAN_3058(3078, 3436, 0, SOUTH, 5)
        BARBARIAN_3069(3078, 3440, 0, <PERSON>O<PERSON><PERSON>, 4)
        BARBARIAN_3068(3078, 3442, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        BARBARIAN_3056(3079, 3409, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        BARBARIAN_3057(3079, 3437, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BARBARIAN(3079, 3444, 0, SOUTH, 3)
        BARBARIAN_3060(3080, 3423, 0, SOUTH, 8)
        3363(3081, 3421, 0, SOUTH, 2)
        GUNTHOR_THE_BRAVE(3081, 3444, 0, SOUTH, 2)
        BARBARIAN_3059(3083, 3429, 0, SOUTH, 8)
        TASSIE_SLIPCAST(3084, 3408, 0, SOUTH, 3)
        BARBARIAN_3070(3084, 3426, 0, SOUTH, 10)
        UNICORN(3084, 3454, 0, SOUTH, 15)
        BARBARIAN_3065(3085, 3409, 0, SOUTH, 7)
        BARBARIAN_3072(3086, 3419, 0, SOUTH, 9)
        BARBARIAN_3067(3086, 3440, 0, SOUTH, 8)
        10645(3087, 3415, 0, SOUTH, 5)
        UNICORN(3090, 3450, 0, SOUTH, 15)
        BARBARIAN_3066(3096, 3432, 0, SOUTH, 3)
        ROD_FISHING_SPOT_1526(3104, 3424, 0, SOUTH, 0)
        ROD_FISHING_SPOT_1526(3110, 3434, 0, SOUTH, 0)
        SIGURD(3113, 3413, 0, SOUTH, 2)
        GOBLIN_3075(3118, 3432, 0, SOUTH, 13)
        GOBLIN_3076(3119, 3444, 0, SOUTH, 10)
        GOBLIN_3073(3121, 3422, 0, SOUTH, 9)
        GOBLIN_3074(3126, 3431, 0, SOUTH, 12)
        GOBLIN_3073(3126, 3450, 0, SOUTH, 9)
        HUNDING(3096, 3428, 2, SOUTH, 2)
    }
}