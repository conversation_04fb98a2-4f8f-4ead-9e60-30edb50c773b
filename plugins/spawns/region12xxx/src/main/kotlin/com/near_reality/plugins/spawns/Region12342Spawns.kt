package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12342Spawns : NPCSpawnsScript() {
    init {
        //16060(3100, 3492, 0, SOUTH, 0) //teleport manager
        //IMP_5007(3073, 3498, 0, SOUTH, 100)
        //IMP_5007(3078, 3461, 0, SOUTH, 100)
        //IMP_5007(3078, 3499, 0, SOUTH, 100)
        //DORIS(3079, 3492, 0, SOUTH, 5)
        //SHOP_KEEPER_2821(3080, 3511, 0, SOUTH, 1)
        //SHOP_ASSISTANT_2822(3081, 3511, 0, SOUTH, 1)
        //GUARD_3254(3085, 3518, 0, <PERSON>OUT<PERSON>, 3)
        //10642(3088, 3471, 0, <PERSON>OUTH, 5)
        //7458(3090, 3494, 0, SOUTH, 0)
        //MAN_3108(3092, 3510, 0, SOUTH, 11)
        //MAN_3106(3093, 3512, 0, SOUTH, 5)
        //GUARD_3254(3093, 3518, 0, SOUTH, 3)
        //BANKER_1613(3096, 3489, 0, WEST, 0)
        //BANKER_1618(3096, 3491, 0, WEST, 0)
        //BANKER_1618(3096, 3492, 0, NORTH, 0)
        //EMBLEM_TRADER(3096, 3505, 0, SOUTH, 5)
        //MAN_3108(3097, 3508, 0, SOUTH, 11)
        //MAN_3106(3097, 3512, 0, SOUTH, 5)
        //BANKER_1613(3098, 3492, 0, NORTH, 0)
        //MAN_3106(3100, 3509, 0, SOUTH, 5)
        //MAN_3107(3100, 3511, 0, SOUTH, 5)
        //MAN_3108(3101, 3509, 0, SOUTH, 11)
        //GUARD_3254(3109, 3513, 0, SOUTH, 3)
        //KRYSTILIA(3109, 3516, 0, SOUTH, 0)
        //GUARD_3254(3110, 3515, 0, SOUTH, 3)
        //GUARD_3254(3114, 3512, 0, SOUTH, 3)
        //GUARD_3254(3114, 3517, 0, SOUTH, 3)
        //OUTLAW_4175(3116, 3473, 0, SOUTH, 2)
        //OUTLAW_4174(3117, 3477, 0, SOUTH, 3)
        //OUTLAW_4176(3118, 3470, 0, SOUTH, 4)
        //OUTLAW(3118, 3474, 0, SOUTH, 3)
        //OUTLAW_4168(3119, 3472, 0, SOUTH, 3)
        //OUTLAW_4173(3119, 3477, 0, SOUTH, 3)
        RAT_2854(3119, 3485, 0, SOUTH, 14)
        //OUTLAW_4169(3121, 3471, 0, SOUTH, 3)
        //LESSER_FANATIC(3121, 3517, 0, SOUTH, 1)
        //LUNA(3121, 3518, 0, SOUTH, 5)
        //LUCIEN(3122, 3483, 0, SOUTH, 5)
        RAT_2854(3122, 3487, 0, SOUTH, 14)
        //OUTLAW_4170(3123, 3473, 0, SOUTH, 4)
        //OUTLAW_4172(3123, 3477, 0, SOUTH, 4)
        //OUTLAW_4171(3124, 3476, 0, SOUTH, 4)
        RAT_2854(3125, 3485, 0, SOUTH, 14)
        //GIANT_SPIDER_3017(3131, 3486, 0, SOUTH, 10)
        //GIANT_SPIDER_3017(3134, 3483, 0, SOUTH, 10)
        IMP_5007(3134, 3487, 0, SOUTH, 100)
        //GIANT_SPIDER_3017(3135, 3487, 0, SOUTH, 10)
        //7457(3080, 3513, 1, SOUTH, 0)
    }
}