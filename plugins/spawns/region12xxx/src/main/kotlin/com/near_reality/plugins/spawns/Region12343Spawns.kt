package com.near_reality.plugins.spawns


import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12343Spawns : NPCSpawnsScript() {
    init {
        SKELETON_77(3081, 3551, 0, <PERSON>O<PERSON><PERSON>, 7)
        SKELETON_71(3096, 3559, 0, SOUT<PERSON>, 7)
        SKELETON_78(3102, 3569, 0, SOUTH, 8)
        SKELETON_79(3103, 3567, 0, SOUTH, 8)
        SKELETON_80(3104, 3566, 0, <PERSON><PERSON>UTH, 8)
        SKELETON_72(3106, 3548, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        3228(3106, 3558, 0, SOUTH, 4)
        SKELETON_81(3106, 3567, 0, <PERSON>OUT<PERSON>, 6)
        SKELETON_77(3106, 3570, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETON_73(3107, 3528, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKELETON(3112, 3538, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETON_72(3116, 3524, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKEL<PERSON><PERSON>_71(3117, 3532, 0, SOUTH, 7)
    }
}