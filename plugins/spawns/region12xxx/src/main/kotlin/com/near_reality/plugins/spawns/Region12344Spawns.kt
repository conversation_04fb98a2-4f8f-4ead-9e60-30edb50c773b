package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12344Spawns : NPCSpawnsScript() {
    init {
        GRIZZLY_BEAR(3075, 3608, 0, SOUTH, 11)
        GRIZZLY_BEAR(3078, 3601, 0, SOUTH, 11)
        GRIZZLY_BEAR(3083, 3597, 0, SOUTH, 11)
        ZAMORAKIAN_ACOLYTE_10380(3127, 3628, 0, SOUTH, 5)
        PERDU(3128, 3631, 0, SOUTH, 0)
        SISTER_SCAROPHIA(3128, 3636, 0, SOUTH, 0)
        REFUGEE_10385(3129, 3617, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        FEROX(3129, 3637, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        REFUGEE(3133, 3624, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SIGISMUND(3134, 3620, 0, <PERSON>OUTH, 5)
        BANKER_10389(3134, 3629, 0, SO<PERSON>H, 5)
        10396(3135, 3627, 0, SOUTH, 5)
        ZAMORAKIAN_ACOLYTE(3135, 3632, 0, SOUTH, 5)
        ZAMORAKIAN_ACOLYTE_10381(3135, 3636, 0, SOUTH, 5)
        REFUGEE_10384(3134, 3628, 2, SOUTH, 5)
        TROPICAL_WAGTAIL(3099, 3621, 0, SOUTH, 5)
        TROPICAL_WAGTAIL(3100, 3621, 0, SOUTH, 5)
        TROPICAL_WAGTAIL(3101, 3621, 0, SOUTH, 5)
        TROPICAL_WAGTAIL(3102, 3621, 0, SOUTH, 5)
        TROPICAL_WAGTAIL(3103, 3621, 0, SOUTH, 5)
        TROPICAL_WAGTAIL(3104, 3621, 0, SOUTH, 5)
        MASTER_FARMER(3134, 3610, 0, SOUTH, 5)
        EMBLEM_TRADER(3134, 3628, 0, SOUTH, 5)
        HERQUIN(3134, 3628, 0, SOUTH, 5)
    }
}