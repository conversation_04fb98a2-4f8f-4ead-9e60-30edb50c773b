package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12346Spawns : NPCSpawnsScript() {
    init {
        HOBGOBLIN_3049(3075, 3748, 0, SOUT<PERSON>, 13)
        HOBGOBLIN_3049(3076, 3774, 0, SOUTH, 13)
        HOBGOBLIN_3049(3078, 3742, 0, SOUTH, 13)
        HOBGOBLIN_3049(3078, 3754, 0, <PERSON>OUT<PERSON>, 13)
        HOBGOBLIN_3049(3078, 3764, 0, SOUTH, 13)
        HOBGOBLIN_3049(3080, 3750, 0, SOUTH, 13)
        HOBGOBLIN_3049(3080, 3759, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGOBLIN_3049(3081, 3770, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGOBLIN_3049(3083, 3766, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOBGOBLIN_3049(3085, 3742, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        HOB<PERSON>O<PERSON><PERSON>_3049(3085, 3757, 0, SOUTH, 13)
        HOBGOBLIN_3049(3085, 3773, 0, SOUTH, 13)
        HOBGOBLIN_3049(3087, 3752, 0, SOUTH, 13)
        HOBGOBLIN_3049(3088, 3758, 0, SOUTH, 13)
        HOBGOBLIN_3049(3088, 3765, 0, SOUTH, 13)
        HOBGOBLIN_3049(3089, 3770, 0, SOUTH, 13)
        HOBGOBLIN_3049(3092, 3756, 0, SOUTH, 13)
        HOBGOBLIN_3049(3092, 3763, 0, SOUTH, 13)
        HOBGOBLIN_3049(3094, 3746, 0, SOUTH, 13)
        HOBGOBLIN_3049(3094, 3772, 0, SOUTH, 13)
        HOBGOBLIN_3049(3096, 3767, 0, SOUTH, 13)
        HOBGOBLIN_3049(3097, 3756, 0, SOUTH, 13)
        HOBGOBLIN_3049(3097, 3761, 0, SOUTH, 13)
        HOBGOBLIN_3049(3099, 3770, 0, SOUTH, 13)
        HOBGOBLIN_3049(3105, 3763, 0, SOUTH, 13)
        HOBGOBLIN_3049(3107, 3758, 0, SOUTH, 13)
    }
}