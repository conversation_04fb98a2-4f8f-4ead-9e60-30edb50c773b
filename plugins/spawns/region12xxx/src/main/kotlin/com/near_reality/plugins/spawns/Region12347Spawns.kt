package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12347Spawns : NPCSpawnsScript() {
    init {
        EDMOND(3072, 3811, 0, SOUTH, 26)
        GREEN_DRAGON_264(3078, 3810, 0, SOUTH, 3)
        GREEN_DRAGON_263(3092, 3810, 0, SOUTH, 4)
        GREEN_DRAGON_262(3098, 3821, 0, SOUTH, 4)
        GREEN_DRAGON_261(3107, 3812, 0, <PERSON><PERSON>UTH, 4)
        GREEN_DRAGON(3118, 3820, 0, SOUTH, 2)
        BLACK_CHINCHOMPA(3134, 3786, 0, SOUTH, 8)
    }
}