package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12349Spawns : NPCSpawnsScript() {
    init {
        GIANT_BAT(3073, 3961, 0, SOUTH, 11)
        GIANT_BAT(3074, 3952, 0, SOUTH, 11)
        GIANT_BAT(3075, 3955, 0, SOUTH, 11)
        ROGUE(3076, 3916, 0, SOUTH, 7)
        GIANT_BAT(3076, 3961, 0, SOUTH, 11)
        GIANT_BAT(3077, 3949, 0, SOUTH, 11)
        ROGUE(3079, 3909, 0, SOUTH, 7)
        GIANT_BAT(3079, 3957, 0, SOUTH, 11)
        GIANT_BAT(3080, 3961, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        BATTLE_MAGE_1611(3098, 3925, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        BATTLE_MAGE_1612(3098, 3942, 0, SOUTH, 3)
        BATTLE_MAGE(3100, 3927, 0, SOUTH, 4)
        BATTLE_MAGE_1611(3100, 3940, 0, SOUTH, 2)
        BATTLE_MAGE_1612(3102, 3929, 0, SOUTH, 3)
        BATTLE_MAGE(3102, 3938, 0, SOUTH, 4)
        BATTLE_MAGE_1611(3110, 3934, 0, SOUTH, 2)
        BATTLE_MAGE_1612(3113, 3934, 0, SOUTH, 3)
        BATTLE_MAGE(3116, 3934, 0, SOUTH, 4)
    }
}