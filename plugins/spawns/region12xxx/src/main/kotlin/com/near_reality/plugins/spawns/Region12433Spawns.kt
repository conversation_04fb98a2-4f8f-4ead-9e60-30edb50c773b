package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12433Spawns : NPCSpawnsScript() {
    init {
        6151(3127, 9325, 0, SOUTH, 5)
        6145(3091, 9307, 1, SOUTH, 0)
        PENTYN(3091, 9324, 1, SOUTH, 5)
        6152(3102, 9311, 1, SOUTH, 5)
        6147(3104, 9286, 1, SOUTH, 0)
        6148(3105, 9297, 1, SOUTH, 0)
        6146(3115, 9323, 1, <PERSON>OUT<PERSON>, 0)
        6144(3104, 9307, 2, SOUTH, 5)
        6153(3108, 9320, 2, SOUTH, 5)
    }
}