package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12436Spawns : NPCSpawnsScript() {
    init {
        MINING_INSTRUCTOR(3081, 9504, 0, SOUTH, 5)
        GIANT_RAT_3313(3098, 9519, 0, SOUTH, 5)
        GIANT_RAT_3313(3099, 9514, 0, SOUTH, 5)
        GIANT_RAT_3313(3100, 9517, 0, SOUTH, 5)
        GIANT_RAT_3313(3100, 9521, 0, SOUTH, 5)
        GIANT_RAT_3313(3102, 9513, 0, SOUTH, 5)
        GIANT_RAT_3313(3103, 9518, 0, <PERSON>OUT<PERSON>, 5)
        GIANT_RAT_3313(3103, 9521, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GIANT_RAT_3313(3104, 9512, 0, <PERSON><PERSON>UT<PERSON>, 5)
        GIANT_RAT_3313(3105, 9514, 0, SOUTH, 5)
        GIANT_RAT_3313(3105, 9518, 0, SOUTH, 5)
        COMBAT_INSTRUCTOR(3106, 9509, 0, SOUTH, 5)
        GIANT_RAT_3313(3107, 9516, 0, SOUTH, 5)
        GIANT_RAT_3313(3107, 9521, 0, SOUTH, 5)
        GIANT_RAT_3313(3109, 9518, 0, SOUTH, 5)
    }
}