package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12439Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(3083, 9673, 0, SOUTH, 14)
        ZOMBIE_39(3086, 9674, 0, SOUTH, 8)
        ZOMBIE_56(3088, 9672, 0, SOUTH, 7)
        RAT_2854(3091, 9670, 0, SOUTH, 14)
        ZOMBIE_58(3096, 9672, 0, SOUTH, 6)
        RAT_2854(3099, 9674, 0, SOUTH, 14)
        SKELETON_79(3101, 9671, 0, SOUTH, 8)
        RAT_2854(3106, 9689, 0, <PERSON>O<PERSON><PERSON>, 14)
        RAT_2854(3108, 9672, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        SKELETON_77(3109, 9675, 0, SOUTH, 7)
        RUANTUN(3112, 9690, 0, WEST, 0)
        RAT_2854(3113, 9683, 0, SOUTH, 14)
        SK<PERSON>ET<PERSON>(3115, 9674, 0, SOUTH, 7)
        RAT_2854(3117, 9694, 0, SOUTH, 14)
        RAT_2854(3118, 9670, 0, SOUTH, 14)
        RAT_2854(3122, 9673, 0, SOUTH, 14)
        RAT_2854(3123, 9666, 0, SOUTH, 14)
        SKELETON_72(3123, 9669, 0, SOUTH, 8)
    }
}