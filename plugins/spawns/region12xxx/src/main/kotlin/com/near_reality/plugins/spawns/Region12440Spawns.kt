package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12440Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(3093, 9754, 0, SOUTH, 14)
        RAT_2854(3099, 9755, 0, SOUTH, 14)
        RAT_2854(3099, 9765, 0, SOUTH, 14)
        RAT_2854(3101, 9748, 0, SOUTH, 14)
        RAT_2854(3103, 9764, 0, SOUTH, 14)
        RAT_2854(3104, 9760, 0, SOUTH, 14)
        SPIDER_3019(3110, 9745, 0, SOUTH, 8)
        SPIDER_3019(3111, 9746, 0, SOUTH, 8)
        RAT_2854(3111, 9752, 0, <PERSON>O<PERSON><PERSON>, 14)
        SPIDER_3019(3112, 9748, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SPIDER_3019(3112, 9749, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        RAT_2854(3113, 9754, 0, <PERSON>O<PERSON>H, 14)
        RAT_2854(3116, 9754, 0, SOUTH, 14)
    }
}