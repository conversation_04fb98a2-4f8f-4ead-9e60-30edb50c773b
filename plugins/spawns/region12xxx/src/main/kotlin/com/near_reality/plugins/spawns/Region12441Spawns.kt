package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12441Spawns : NPCSpawnsScript() {
    init {
        HILL_GIANT_2099(3099, 9832, 0, SOUTH, 3)
        HILL_GIANT_2100(3100, 9836, 0, SOUTH, 3)
        HILL_GIANT_2103(3106, 9827, 0, SOUTH, 3)
        HILL_GIANT_2101(3107, 9835, 0, SOUTH, 3)
        HILL_GIANT_2101(3110, 9845, 0, SOUTH, 3)
        HILL_GIANT(3111, 9841, 0, SOUTH, 3)
        HILL_GIANT_2102(3113, 9831, 0, SOUTH, 3)
        HILL_GIANT_2103(3116, 9832, 0, <PERSON>O<PERSON><PERSON>, 3)
        HILL_GIANT_2102(3117, 9835, 0, <PERSON><PERSON>UT<PERSON>, 3)
        HILL_GIANT_2099(3117, 9843, 0, SOUTH, 3)
        HILL_GIANT(3117, 9850, 0, SOUTH, 3)
        HILL_GIANT_2100(3122, 9844, 0, SOUTH, 3)
    }
}