package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12443Spawns : NPCSpawnsScript() {
    init {
        BLACK_DEMON_2049(3084, 9957, 0, SOUTH, 6)
        CHRONOZON(3087, 9937, 0, SOUTH, 5)
        POISON_SPIDER(3088, 9945, 0, SOUTH, 11)
        BLACK_DEMON_2052(3088, 9961, 0, SOUTH, 2)
        POISON_SPIDER(3089, 9943, 0, SOUTH, 11)
        POISON_SPIDER(3090, 9944, 0, SOUTH, 11)
        BLACK_DEMON_2050(3092, 9958, 0, <PERSON>OUT<PERSON>, 3)
        SKELETON_82(3101, 9956, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        CHAOS_DRUID(3104, 9942, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKELETON_82(3104, 9948, 0, <PERSON><PERSON>UT<PERSON>, 7)
        SKELETON_82(3104, 9955, 0, SOUT<PERSON>, 7)
        CHAOS_DRUID(3105, 9936, 0, SOUTH, 8)
        CHAOS_DRUID(3106, 9940, 0, SOUTH, 8)
        CHAOS_DRUID(3107, 9943, 0, SOUTH, 8)
        SKELETON_82(3108, 9951, 0, SOUTH, 7)
        SKELETON_82(3108, 9954, 0, SOUTH, 7)
        CHAOS_DRUID(3109, 9931, 0, SOUTH, 8)
        CHAOS_DRUID(3110, 9941, 0, SOUTH, 8)
        CHAOS_DRUID(3111, 9936, 0, SOUTH, 8)
        CHAOS_DRUID(3111, 9939, 0, SOUTH, 8)
        SKELETON_82(3111, 9954, 0, SOUTH, 7)
        SKELETON_82(3112, 9958, 0, SOUTH, 7)
        CHAOS_DRUID(3114, 9929, 0, SOUTH, 8)
        CHAOS_DRUID(3115, 9925, 0, SOUTH, 8)
        CHAOS_DRUID(3115, 9932, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3118, 9950, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3118, 9956, 0, SOUTH, 8)
        EARTH_WARRIOR(3118, 9974, 0, SOUTH, 4)
        EARTH_WARRIOR(3119, 9971, 0, SOUTH, 4)
        DEADLY_RED_SPIDER(3120, 9952, 0, SOUTH, 8)
        EARTH_WARRIOR(3120, 9976, 0, SOUTH, 4)
        DEADLY_RED_SPIDER(3122, 9955, 0, SOUTH, 8)
        THUG(3123, 9929, 0, SOUTH, 2)
        DEADLY_RED_SPIDER(3124, 9951, 0, SOUTH, 8)
        EARTH_WARRIOR(3124, 9974, 0, SOUTH, 4)
        DEADLY_RED_SPIDER(3125, 9958, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3126, 9948, 0, SOUTH, 8)
        THUG(3127, 9929, 0, SOUTH, 2)
        THUG(3127, 9933, 0, SOUTH, 2)
        DEADLY_RED_SPIDER(3127, 9956, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3128, 9954, 0, SOUTH, 8)
        THUG(3130, 9930, 0, SOUTH, 2)
        THUG(3131, 9934, 0, SOUTH, 2)
        THUG(3132, 9921, 0, SOUTH, 2)
        THUG(3132, 9926, 0, SOUTH, 2)
        THUG(3133, 9928, 0, SOUTH, 2)
        THUG(3134, 9935, 0, SOUTH, 2)
    }
}