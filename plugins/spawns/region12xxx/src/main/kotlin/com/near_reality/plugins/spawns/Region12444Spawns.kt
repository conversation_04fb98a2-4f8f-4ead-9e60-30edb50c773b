package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12444Spawns : NPCSpawnsScript() {
    init {
        EARTH_WARRIOR(3116, 9992, 0, SOUTH, 4)
        EARTH_WARRIOR(3118, 9989, 0, SOUTH, 4)
        6123(3119, 9996, 0, SOUTH, 5)
        EARTH_WARRIOR(3121, 9992, 0, SOUTH, 4)
        EARTH_WARRIOR(3122, 9996, 0, SOUTH, 4)
        EARTH_WARRIOR(3123, 9988, 0, SOUTH, 4)
        EARTH_WARRIOR(3125, 9994, 0, SOUTH, 4)
        EARTH_WARRIOR(3126, 9992, 0, SOUTH, 4)
    }
}