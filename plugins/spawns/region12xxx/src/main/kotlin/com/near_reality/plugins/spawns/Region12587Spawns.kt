package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12587Spawns : NPCSpawnsScript() {
    init {
        GULL(3138, 2784, 0, SOUT<PERSON>, 7)
        GULL(3139, 2770, 0, SOUTH, 7)
        GULL(3139, 2810, 0, SOUTH, 7)
        FISHING_SPOT_10635(3140, 2795, 0, SOUTH, 5)
        FISHING_SPOT_10635(3140, 2797, 0, SOUTH, 5)
        FISHING_SPOT_10635(3142, 2802, 0, SOUT<PERSON>, 5)
        GULL(3148, 2776, 0, SOUTH, 7)
        GOAT(3161, 2782, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        BILLY_GOAT(3162, 2785, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        BILLY_GOAT_1797(3163, 2783, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        GOAT(3171, 2794, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        GOAT_1793(3172, 2797, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        <PERSON><PERSON>_1796(3173, 2795, 0, SOUTH, 4)
        BILLY_GOAT(3175, 2795, 0, SOUTH, 5)
        GOAT(3180, 2760, 0, SOUTH, 3)
        BILLY_GOAT(3181, 2763, 0, SOUTH, 5)
        BILLY_GO<PERSON>_1797(3182, 2761, 0, SOUTH, 4)
    }
}