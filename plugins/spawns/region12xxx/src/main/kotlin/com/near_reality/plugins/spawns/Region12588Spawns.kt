package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12588Spawns : NPCSpawnsScript() {
    init {
        GULL(3136, 2816, 0, SOUTH, 7)
        GULL(3138, 2863, 0, SOUTH, 7)
        GULL(3142, 2830, 0, SOUTH, 7)
        GULL(3148, 2852, 0, SOUTH, 7)
        SPIRIT_ANGLER(3153, 2831, 0, SOUTH, 0)
        SEAGULL(3154, 2846, 0, SOUTH, 5)
        LAURETTA(3155, 2819, 0, SOUTH, 5)
        SEAGULL(3155, 2848, 0, SOUTH, 5)
        SEAGULL(3156, 2845, 0, SOUTH, 5)
        ISHMAEL(3159, 2847, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RETIRED_SAILOR(3160, 2850, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        TAIMAN(3161, 2839, 0, <PERSON>OUTH, 5)
        TIMALLUS(3162, 2843, 0, SOUTH, 5)
        SCORPION_3024(3164, 2862, 0, SOUTH, 12)
        SHANTAY_GUARD_10634(3165, 2818, 0, SOUTH, 5)
        KOANEE(3167, 2852, 0, SOUTH, 5)
        GITA_PRYMES(3169, 2838, 0, SOUTH, 0)
        SPIRIT_ANGLER_10607(3171, 2831, 0, SOUTH, 5)
        SPIRIT_ANGLER_10606(3173, 2856, 0, SOUTH, 5)
        SPIRIT_ANGLER_10608(3174, 2846, 0, SOUTH, 5)
        SPIRIT_ANGLER_10609(3176, 2831, 0, SOUTH, 5)
        SCORPION_3024(3178, 2852, 0, SOUTH, 12)
        SCORPION_3024(3180, 2856, 0, SOUTH, 12)
        SCORPION_3024(3182, 2852, 0, SOUTH, 12)
        CROCODILE(3187, 2818, 0, SOUTH, 6)
        SCORPION_3024(3187, 2862, 0, SOUTH, 12)
        CROCODILE(3190, 2825, 0, SOUTH, 6)
        SHANTAY_GUARD_10634(3193, 2841, 0, SOUTH, 5)
        SCORPION_3024(3193, 2855, 0, SOUTH, 12)
        CROCODILE(3196, 2826, 0, SOUTH, 6)
        CROCODILE(3197, 2817, 0, SOUTH, 6)
        SAILOR_10604(3138, 2841, 0, SOUTH, 5)
        SEAGULL(3139, 2840, 0, SOUTH, 5)
        SEAGULL(3149, 2842, 0, SOUTH, 5)
        FERRYMAN_NATHWOOD(3149, 2843, 0, SOUTH, 5)
        CAPTAIN_PUDI(3153, 2840, 0, SOUTH, 0)
    }
}