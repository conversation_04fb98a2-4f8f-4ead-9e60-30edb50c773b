package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12590Spawns : NPCSpawnsScript() {
    init {
        BANDIT_692(3157, 2986, 0, SOUTH, 0)
        BANDIT_691(3157, 2989, 0, SOUTH, 0)
        BANDIT(3158, 2977, 0, SOUTH, 2)
        BANDIT(3158, 2987, 0, SOUTH, 2)
        BANDIT_693(3159, 2977, 0, SOUTH, 0)
        BANDIT(3159, 2979, 0, SOUTH, 2)
        BARTENDER(3159, 2981, 0, SOUTH, 0)
        BANDIT_694(3161, 2977, 0, SOUT<PERSON>, 0)
        BANDIT_694(3161, 2981, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        BANDIT_694(3161, 2982, 0, SOUTH, 0)
        BANDIT(3161, 2983, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        BANDIT(3161, 2986, 0, SOUTH, 2)
        6107(3162, 2982, 0, SOUTH, 2)
        BANDIT_692(3162, 2987, 0, SOUTH, 0)
        BANDIT_691(3162, 2989, 0, SOUTH, 0)
        BANDIT_694(3163, 2977, 0, SOUTH, 0)
        BANDIT(3163, 2980, 0, SOUTH, 2)
        BANDIT(3163, 2982, 0, SOUTH, 2)
        BANDIT(3163, 2986, 0, SOUTH, 2)
        BANDIT(3164, 2978, 0, SOUTH, 2)
        BANDIT(3164, 2984, 0, SOUTH, 2)
        BANDIT_695(3165, 2960, 0, SOUTH, 9)
        BANDIT_695(3168, 2989, 0, SOUTH, 9)
        BANDIT_695(3171, 2975, 0, SOUTH, 9)
        BANDIT_695(3173, 2972, 0, SOUTH, 9)
        BANDIT_695(3173, 2981, 0, SOUTH, 9)
        BANDIT_695(3175, 2974, 0, SOUTH, 9)
        BANDIT_SHOPKEEPER(3175, 2988, 0, SOUTH, 2)
        TILES(3178, 2986, 0, SOUTH, 2)
        BANDIT_695(3180, 2992, 0, SOUTH, 9)
        BANDIT_695(3183, 2974, 0, SOUTH, 9)
        BANDIT_695(3183, 2975, 0, SOUTH, 9)
        1966(3185, 2983, 0, SOUTH, 3)
        BANDIT_695(3185, 2985, 0, SOUTH, 9)
        BANDIT_695(3187, 2984, 0, SOUTH, 9)
    }
}