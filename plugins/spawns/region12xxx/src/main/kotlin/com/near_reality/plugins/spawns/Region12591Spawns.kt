package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12591Spawns : NPCSpawnsScript() {
    init {
        DESERT_WOLF(3150, 3044, 0, SOUTH, 3)
        DESERT_WOLF_4651(3153, 3047, 0, SOUTH, 5)
        BEDABIN_NOMAD(3159, 3042, 0, SOUTH, 3)
        BEDABIN_NOMAD_FIGHTER(3160, 3031, 0, SOUTH, 15)
        BEDABIN_NOMAD(3160, 3039, 0, SOUTH, 3)
        BEDABIN_NOMAD_FIGHTER(3162, 3033, 0, SOUTH, 15)
        BEDABIN_NOMAD(3163, 3035, 0, <PERSON>O<PERSON><PERSON>, 3)
        BEDABIN_NOMAD_FIGHTER(3164, 3031, 0, <PERSON>O<PERSON><PERSON>, 15)
        BEDABIN_NOMAD(3169, 3030, 0, <PERSON>OUTH, 3)
        BEDABIN_NOMAD_GUARD(3170, 3045, 0, NORTH_WEST, 0)
        AL_SHABIM(3171, 3025, 0, SOUTH, 3)
        DESERT_WOLF(3172, 3009, 0, SOUTH, 3)
        BEDABIN_NOMAD(3172, 3031, 0, SOUTH, 3)
        BEDABIN_NOMAD(3173, 3035, 0, SOUTH, 3)
        CAMEL(3173, 3039, 0, SOUTH, 5)
        DESERT_WOLF_4650(3174, 3010, 0, SOUTH, 4)
        BEDABIN_NOMAD(3176, 3038, 0, SOUTH, 3)
        BEDABIN_NOMAD(3176, 3043, 0, SOUTH, 3)
        BEDABIN_NOMAD_FIGHTER(3177, 3033, 0, SOUTH, 15)
        BEDABIN_NOMAD_FIGHTER(3178, 3030, 0, SOUTH, 15)
        ARCHAEOLOGIST(3178, 3043, 0, SOUTH, 2)
        BEDABIN_NOMAD_FIGHTER(3180, 3033, 0, SOUTH, 15)
        MONKEY(3181, 3043, 0, SOUTH, 5)
        RUG_MERCHANT_18(3182, 3043, 0, SOUTH, 2)
        UGTHANKI(3190, 3054, 0, SOUTH, 6)
        UGTHANKI(3192, 3016, 0, SOUTH, 6)
        DESERT_WOLF_4651(3196, 3063, 0, SOUTH, 5)
        DESERT_WOLF(3197, 3012, 0, SOUTH, 3)
        DESERT_WOLF(3198, 3040, 0, SOUTH, 3)
        DESERT_WOLF_4650(3198, 3061, 0, SOUTH, 4)
        DESERT_WOLF_4650(3199, 3016, 0, SOUTH, 4)
        DESERT_WOLF_4651(3199, 3036, 0, SOUTH, 5)
    }
}