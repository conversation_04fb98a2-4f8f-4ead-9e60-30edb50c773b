package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12594Spawns : NPCSpawnsScript() {
    init {
        UNICORN(3140, 3209, 0, SOUTH, 15)
        GOBLIN_3031(3141, 3258, 0, SOUTH, 11)
        GOBLIN_3029(3141, 3261, 0, SOUTH, 13)
        GOBLIN_3030(3142, 3230, 0, SOUTH, 13)
        GOBLIN_3030(3142, 3260, 0, SOUTH, 13)
        BUTTERFLY_238(3143, 3210, 0, SOUTH, 0)
        GOBLIN_3031(3143, 3227, 0, SOUTH, 11)
        GOBLIN_3033(3144, 3231, 0, <PERSON>O<PERSON><PERSON>, 12)
        GOBLIN_3034(3144, 3233, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        GOBLIN_3034(3144, 3259, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        GOBLIN_3032(3145, 3229, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        GOBLIN_3035(3146, 3234, 0, <PERSON><PERSON><PERSON>H, 11)
        GO<PERSON><PERSON>_3034(3146, 3260, 0, SOUTH, 14)
        ARCHER(3149, 3205, 0, SOUTH, 4)
        WARRIOR(3149, 3207, 0, SOUTH, 5)
        MONK_1159(3151, 3207, 0, SOUTH, 5)
        WIZARD(3152, 3205, 0, SOUTH, 5)
        BUTTERFLY_238(3155, 3253, 0, SOUTH, 0)
        SPIDER_3019(3157, 3225, 0, SOUTH, 8)
        SPIDER_3019(3158, 3225, 0, SOUTH, 8)
        GIANT_SPIDER_3017(3159, 3223, 0, SOUTH, 10)
        GIANT_SPIDER_3017(3162, 3225, 0, SOUTH, 10)
        GIANT_SPIDER_3017(3163, 3222, 0, SOUTH, 10)
        SPIDER_3019(3163, 3226, 0, SOUTH, 8)
        BUTTERFLY_238(3163, 3261, 0, SOUTH, 0)
        SPIDER_3019(3165, 3247, 0, SOUTH, 8)
        GIANT_SPIDER_3017(3165, 3249, 0, SOUTH, 10)
        GIANT_SPIDER_3017(3166, 3242, 0, SOUTH, 10)
        SPIDER_3019(3166, 3248, 0, SOUTH, 8)
        GOBLIN_3045(3167, 3205, 0, SOUTH, 22)
        GIANT_SPIDER_3017(3167, 3247, 0, SOUTH, 10)
        SPIDER_3019(3168, 3245, 0, SOUTH, 8)
        BUTTERFLY_238(3168, 3258, 0, SOUTH, 0)
        SPIDER_3019(3169, 3243, 0, SOUTH, 8)
        GIANT_SPIDER_3017(3170, 3245, 0, SOUTH, 10)
        GIANT_SPIDER_3017(3170, 3248, 0, SOUTH, 10)
        BUTTERFLY(3175, 3226, 0, SOUTH, 7)
        BUTTERFLY(3178, 3228, 0, SOUTH, 7)
        BUTTERFLY(3179, 3226, 0, SOUTH, 7)
        MUGGER(3181, 3216, 0, SOUTH, 2)
        BUTTERFLY(3182, 3251, 0, SOUTH, 7)
        GOBLIN_3033(3183, 3244, 0, SOUTH, 12)
        GOBLIN_3034(3183, 3246, 0, SOUTH, 14)
        BUTTERFLY(3183, 3254, 0, SOUTH, 7)
        SHEEP_1303(3185, 3219, 0, SOUTH, 5)
        GOBLIN_3036(3185, 3244, 0, SOUTH, 12)
        GOBLIN_3033(3185, 3246, 0, SOUTH, 12)
        SHEEP_1308(3186, 3218, 0, SOUTH, 4)
        GOBLIN_3034(3187, 3244, 0, SOUTH, 14)
        GOBLIN_3036(3187, 3246, 0, SOUTH, 12)
        RAT_2854(3191, 3204, 0, SOUTH, 14)
        GIANT_RAT_2859(3192, 3207, 0, SOUTH, 4)
        GOBLIN_3033(3192, 3245, 0, SOUTH, 12)
        RAT_2854(3193, 3205, 0, SOUTH, 14)
        FAYETH(3193, 3233, 0, SOUTH, 5)
        SHEEP_2699(3193, 3262, 0, SOUTH, 2)
        GIANT_RAT_2861(3194, 3203, 0, SOUTH, 5)
        GOBLIN_3031(3194, 3248, 0, SOUTH, 11)
        GIANT_RAT_2860(3195, 3207, 0, SOUTH, 5)
        RAT_2854(3196, 3206, 0, SOUTH, 14)
        TOOL_LEPRECHAUN(3196, 3228, 0, SOUTH, 0)
        SHEEP_2786(3196, 3259, 0, SOUTH, 3)
        GIANT_RAT_2859(3197, 3201, 0, SOUTH, 4)
        RAT_2854(3197, 3204, 0, SOUTH, 14)
        GOBLIN_3030(3197, 3250, 0, SOUTH, 13)
        GOBLIN_3029(3198, 3255, 0, SOUTH, 13)
        SHEEP_2693(3198, 3261, 0, SOUTH, 3)
    }
}