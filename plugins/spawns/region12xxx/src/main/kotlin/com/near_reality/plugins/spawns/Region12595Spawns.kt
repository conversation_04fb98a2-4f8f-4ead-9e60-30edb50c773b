package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12595Spawns : NPCSpawnsScript() {
    init {
        GOBLIN_3033(3137, 3287, 0, SOUTH, 12)
        GOBLIN_3033(3137, 3297, 0, SOUT<PERSON>, 12)
        GOBLIN_3033(3141, 3304, 0, SOUTH, 12)
        GOBLIN_3029(3142, 3294, 0, SOUTH, 13)
        GOBLIN_3031(3142, 3297, 0, <PERSON><PERSON>UTH, 11)
        GOBLIN_3033(3142, 3302, 0, SOUT<PERSON>, 12)
        GOBLIN_3036(3144, 3299, 0, SOUTH, 12)
        GOBLIN_3029(3144, 3300, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        GOBLIN_3036(3144, 3308, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        GOBLIN_3036(3145, 3294, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        GOBLIN_3029(3145, 3303, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        GOBLIN_3030(3145, 3305, 0, SO<PERSON>H, 13)
        GOBLIN_3033(3145, 3310, 0, SOUTH, 12)
        GOBLIN_3035(3146, 3296, 0, SOUTH, 11)
        GOBLIN_3031(3146, 3298, 0, SOUTH, 11)
        GOBLIN_3033(3146, 3302, 0, SOUTH, 12)
        FARMER_3250(3147, 3282, 0, SOUTH, 5)
        GOBLIN_3029(3147, 3302, 0, SOUTH, 13)
        GOBLIN_3030(3147, 3308, 0, SOUTH, 13)
        GOBLIN_3029(3148, 3296, 0, SOUTH, 13)
        GOBLIN_3035(3148, 3300, 0, SOUTH, 11)
        GOBLIN_3036(3149, 3310, 0, SOUTH, 12)
        GOBLIN_3029(3150, 3302, 0, SOUTH, 13)
        GOBLIN_3035(3151, 3305, 0, SOUTH, 11)
        FARMER(3154, 3293, 0, SOUTH, 5)
        GOBLIN_3031(3154, 3309, 0, SOUTH, 11)
        COW(3154, 3325, 0, SOUTH, 3)
        COW_2791(3155, 3321, 0, SOUTH, 4)
        GOBLIN_3031(3156, 3293, 0, SOUTH, 11)
        COW_CALF_2794(3156, 3326, 0, SOUTH, 3)
        GOBLIN_3029(3160, 3289, 0, SOUTH, 13)
        COW_2791(3160, 3318, 0, SOUTH, 4)
        GOBLIN_3033(3161, 3307, 0, SOUTH, 12)
        GOBLIN_3034(3162, 3277, 0, SOUTH, 14)
        GOBLIN_3035(3162, 3287, 0, SOUTH, 11)
        GOBLIN_3034(3163, 3286, 0, SOUTH, 14)
        GOBLIN_3030(3165, 3288, 0, SOUTH, 13)
        GOBLIN_3031(3165, 3292, 0, SOUTH, 11)
        GOBLIN_3034(3165, 3299, 0, SOUTH, 14)
        COW(3165, 3320, 0, SOUTH, 3)
        FARMER(3165, 3324, 0, SOUTH, 5)
        COW_2793(3165, 3326, 0, SOUTH, 7)
        DRAKE(3166, 3273, 0, SOUTH, 3)
        GOBLIN_3036(3167, 3287, 0, SOUTH, 12)
        GOBLIN_3034(3167, 3289, 0, SOUTH, 14)
        GOBLIN_3035(3167, 3295, 0, SOUTH, 11)
        MILLIE_MILLER(3168, 3307, 0, SOUTH, 2)
        DUCK(3169, 3270, 0, SOUTH, 18)
        DUCK_2003(3169, 3274, 0, SOUTH, 3)
        GOBLIN_3033(3169, 3283, 0, SOUTH, 12)
        DUCK_1839(3170, 3270, 0, SOUTH, 20)
        DRAKE(3170, 3275, 0, SOUTH, 3)
        GOBLIN_3033(3170, 3286, 0, SOUTH, 12)
        CHICKEN(3171, 3293, 0, SOUTH, 2)
        CHICKEN(3171, 3298, 0, SOUTH, 2)
        COW_CALF(3171, 3326, 0, SOUTH, 9)
        DUCKLING(3172, 3271, 0, SOUTH, 2)
        CHICKEN(3172, 3291, 0, SOUTH, 2)
        CHICKEN_1174(3172, 3293, 0, SOUTH, 4)
        GOBLIN_3029(3172, 3311, 0, SOUTH, 13)
        1172(3172, 3317, 0, SOUTH, 0)
        DUCKLING(3173, 3271, 0, SOUTH, 2)
        CHICKEN_1174(3173, 3296, 0, SOUTH, 4)
        CHICKEN(3173, 3302, 0, SOUTH, 2)
        COW_2793(3173, 3323, 0, SOUTH, 7)
        CHICKEN(3174, 3291, 0, SOUTH, 2)
        CHICKEN_1174(3174, 3297, 0, SOUTH, 4)
        CHICKEN_1174(3174, 3300, 0, SOUTH, 4)
        CHICKEN(3174, 3305, 0, SOUTH, 2)
        GOBLIN_3031(3175, 3286, 0, SOUTH, 11)
        CHICKEN(3175, 3293, 0, SOUTH, 2)
        CHICKEN(3175, 3298, 0, SOUTH, 2)
        CHICKEN_1174(3176, 3289, 0, SOUTH, 4)
        CHICKEN_1174(3176, 3301, 0, SOUTH, 4)
        CHICKEN_1174(3177, 3296, 0, SOUTH, 4)
        CHICKEN(3177, 3304, 0, SOUTH, 2)
        CHICKEN_1174(3177, 3305, 0, SOUTH, 4)
        COW_CALF_2794(3177, 3322, 0, SOUTH, 3)
        CHICKEN(3178, 3292, 0, SOUTH, 2)
        CHICKEN_1174(3178, 3295, 0, SOUTH, 4)
        CHICKEN(3178, 3298, 0, SOUTH, 2)
        COW(3178, 3318, 0, SOUTH, 3)
        CHICKEN(3179, 3301, 0, SOUTH, 2)
        GOBLIN_3033(3180, 3286, 0, SOUTH, 12)
        CHICKEN(3180, 3298, 0, SOUTH, 2)
        CHICKEN(3181, 3290, 0, SOUTH, 2)
        FARMER(3181, 3293, 0, SOUTH, 5)
        CHICKEN_1174(3181, 3296, 0, SOUTH, 4)
        COW_CALF_2794(3181, 3315, 0, SOUTH, 3)
        CHICKEN_1174(3182, 3301, 0, SOUTH, 4)
        COW(3182, 3321, 0, SOUTH, 3)
        GOBLIN_3030(3183, 3284, 0, SOUTH, 13)
        CHICKEN_1174(3183, 3290, 0, SOUTH, 4)
        CHICKEN(3183, 3296, 0, SOUTH, 2)
        CHICKEN(3183, 3299, 0, SOUTH, 2)
        CHICKEN_1174(3185, 3277, 0, SOUTH, 4)
        GOBLIN_3033(3185, 3282, 0, SOUTH, 12)
        GOBLIN_3031(3185, 3285, 0, SOUTH, 11)
        GOBLIN_3035(3185, 3307, 0, SOUTH, 11)
        CHICKEN(3186, 3279, 0, SOUTH, 2)
        COW(3186, 3326, 0, SOUTH, 3)
        CHICKEN_1174(3187, 3278, 0, SOUTH, 4)
        COW_2793(3187, 3322, 0, SOUTH, 7)
        GOBLIN_3031(3188, 3290, 0, SOUTH, 11)
        COW(3188, 3314, 0, SOUTH, 3)
        COW_2791(3188, 3318, 0, SOUTH, 4)
        FRED_THE_FARMER(3189, 3273, 0, SOUTH, 2)
        CHICKEN(3189, 3278, 0, SOUTH, 2)
        GOBLIN_3035(3189, 3281, 0, SOUTH, 11)
        GOBLIN_3031(3189, 3300, 0, SOUTH, 11)
        GOBLIN_3029(3190, 3283, 0, SOUTH, 13)
        GOBLIN_3029(3190, 3296, 0, SOUTH, 13)
        CHICKEN_1174(3191, 3277, 0, SOUTH, 4)
        COW(3191, 3311, 0, SOUTH, 3)
        SHEEP_2787(3194, 3267, 0, SOUTH, 3)
        COW_CALF(3194, 3289, 0, SOUTH, 9)
        COW(3194, 3293, 0, SOUTH, 3)
        COW_CALF(3194, 3300, 0, SOUTH, 9)
        RAM_1262(3195, 3265, 0, SOUTH, 5)
        SHEEP_2786(3195, 3272, 0, SOUTH, 3)
        COW_2793(3195, 3287, 0, SOUTH, 7)
        RAM_1264(3196, 3275, 0, SOUTH, 4)
        COW(3196, 3283, 0, SOUTH, 3)
        COW_2793(3196, 3311, 0, SOUTH, 7)
        SHEEP_2699(3197, 3269, 0, SOUTH, 2)
        GOBLIN_3031(3197, 3280, 0, SOUTH, 11)
        COW_2793(3197, 3291, 0, SOUTH, 7)
        COW_2793(3197, 3297, 0, SOUTH, 7)
        COW(3197, 3300, 0, SOUTH, 3)
        COW(3197, 3316, 0, SOUTH, 3)
        COW(3197, 3320, 0, SOUTH, 3)
        SHEEP_2693(3198, 3265, 0, SOUTH, 3)
        SHEEP_2693(3198, 3272, 0, SOUTH, 3)
        SHEEP(3199, 3267, 0, SOUTH, 4)
        RAM_1263(3199, 3270, 0, SOUTH, 4)
        FARMER_3243(3199, 3289, 0, SOUTH, 5)
    }
}