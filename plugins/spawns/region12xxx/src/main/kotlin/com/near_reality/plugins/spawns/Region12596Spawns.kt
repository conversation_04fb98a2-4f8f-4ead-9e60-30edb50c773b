package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12596Spawns : NPCSpawnsScript() {
    init {
        4418(3138, 3352, 0, EAST, 0)
        4418(3140, 3360, 0, EAST, 0)
        DUCK(3140, 3387, 0, SOUTH, 18)
        DUCK_1839(3142, 3386, 0, SOUTH, 20)
        4418(3143, 3357, 0, EAST, 0)
        4418(3143, 3365, 0, EAST, 0)
        DUCK(3143, 3384, 0, SOUTH, 18)
        DUCK_1839(3144, 3382, 0, SOUTH, 20)
        DUCK(3146, 3382, 0, SO<PERSON><PERSON>, 18)
        DUCK_1839(3147, 3380, 0, <PERSON><PERSON><PERSON><PERSON>, 20)
        DUCK(3148, 3372, 0, <PERSON><PERSON><PERSON><PERSON>, 18)
        DUCK_1839(3148, 3374, 0, <PERSON><PERSON><PERSON><PERSON>, 20)
        DUCK(3150, 3368, 0, <PERSON><PERSON><PERSON><PERSON>, 18)
        DUCK_1839(3150, 3370, 0, SOUTH, 20)
        DUCK(3150, 3371, 0, SOUTH, 18)
        DUCK(3150, 3373, 0, SOUTH, 18)
        DUCK_1839(3151, 3371, 0, SOUTH, 20)
        DUCK_1839(3152, 3357, 0, SOUTH, 20)
        COW_2793(3155, 3331, 0, SOUTH, 7)
        COW_CALF_2794(3155, 3336, 0, SOUTH, 3)
        COW_2791(3156, 3342, 0, SOUTH, 4)
        DUCK_1839(3157, 3352, 0, SOUTH, 20)
        DUCK(3157, 3354, 0, SOUTH, 18)
        COW_CALF_2794(3158, 3329, 0, SOUTH, 3)
        COW_2791(3158, 3336, 0, SOUTH, 4)
        COW_2791(3160, 3329, 0, SOUTH, 4)
        COW_CALF(3160, 3342, 0, SOUTH, 9)
        DUCK(3160, 3351, 0, SOUTH, 18)
        DUCK_1839(3161, 3350, 0, SOUTH, 20)
        COW(3162, 3335, 0, SOUTH, 3)
        FARMER_3243(3162, 3338, 0, SOUTH, 5)
        DUCK_1839(3162, 3351, 0, SOUTH, 20)
        DUCK(3163, 3350, 0, SOUTH, 18)
        COW_2793(3164, 3340, 0, SOUTH, 7)
        DUCK_1839(3164, 3348, 0, SOUTH, 20)
        COW_2791(3169, 3335, 0, SOUTH, 4)
        COW(3169, 3341, 0, SOUTH, 3)
        DUCK(3169, 3345, 0, SOUTH, 18)
        DUCK_1839(3169, 3346, 0, SOUTH, 20)
        DUCK(3169, 3355, 0, SOUTH, 18)
        COW_2793(3170, 3329, 0, SOUTH, 7)
        DUCK(3171, 3355, 0, SOUTH, 18)
        DUCK(3172, 3345, 0, SOUTH, 18)
        DUCK_1839(3172, 3355, 0, SOUTH, 20)
        COW(3173, 3332, 0, SOUTH, 3)
        COW_CALF_2794(3173, 3336, 0, SOUTH, 3)
        DUCK_1839(3173, 3346, 0, SOUTH, 20)
        DUCK(3173, 3356, 0, SOUTH, 18)
        COW_2791(3174, 3341, 0, SOUTH, 4)
        DUCK(3174, 3347, 0, SOUTH, 18)
        DUCK_1839(3175, 3346, 0, SOUTH, 20)
        DUCK_1839(3175, 3354, 0, SOUTH, 20)
        MUGGER(3175, 3361, 0, SOUTH, 2)
        DUCK(3176, 3347, 0, SOUTH, 18)
        DUCK(3176, 3354, 0, SOUTH, 18)
        COW_2791(3177, 3333, 0, SOUTH, 4)
        COW_CALF(3177, 3339, 0, SOUTH, 9)
        DUCK_1839(3177, 3349, 0, SOUTH, 20)
        DUCK_1839(3177, 3352, 0, SOUTH, 20)
        COW(3180, 3340, 0, SOUTH, 3)
        DREVEN(3181, 3359, 0, SOUTH, 5)
        COW_2793(3182, 3329, 0, SOUTH, 7)
        TOOL_LEPRECHAUN(3182, 3354, 0, SOUTH, 0)
        DUCK(3183, 3348, 0, SOUTH, 18)
        COW(3184, 3334, 0, SOUTH, 3)
        COW_2793(3184, 3341, 0, SOUTH, 7)
        THIEF_5217(3184, 3385, 0, SOUTH, 3)
        THIEF_5219(3184, 3389, 0, SOUTH, 3)
        THIEF_5220(3185, 3390, 0, SOUTH, 4)
        KATRINE(3186, 3385, 0, SOUTH, 3)
        COW_CALF(3187, 3331, 0, SOUTH, 9)
        COW_2791(3187, 3336, 0, SOUTH, 4)
        COW_2791(3189, 3332, 0, SOUTH, 4)
        COW_CALF_2794(3191, 3336, 0, SOUTH, 3)
        DUCK(3191, 3342, 0, SOUTH, 18)
        GUILDMASTER(3191, 3362, 0, SOUTH, 3)
        FARMER(3192, 3330, 0, SOUTH, 5)
        DUCK(3194, 3341, 0, SOUTH, 18)
        DUCK(3194, 3343, 0, SOUTH, 18)
        COW(3195, 3335, 0, SOUTH, 3)
        DUCK(3196, 3339, 0, SOUTH, 18)
        CHICKEN(3196, 3352, 0, SOUTH, 2)
        COW_2791(3197, 3331, 0, SOUTH, 4)
        CHICKEN_1174(3197, 3352, 0, SOUTH, 4)
        CHICKEN(3197, 3353, 0, SOUTH, 2)
        CHICKEN_1174(3198, 3354, 0, SOUTH, 4)
        CHICKEN(3198, 3355, 0, SOUTH, 2)
        EVIL_CHICKEN_6739(3198, 3359, 0, SOUTH, 2)
        SCAVVO(3191, 3351, 1, SOUTH, 2)
        VALAINE(3193, 3361, 1, SOUTH, 2)
    }
}