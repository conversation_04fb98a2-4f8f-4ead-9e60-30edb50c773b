package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12597Spawns : NPCSpawnsScript() {
    init {
        ROMILY_WEAKLAX(3139, 3448, 0, SOUTH, 4)
        HEAD_CHEF(3143, 3444, 0, SOUTH, 2)
        BANKER_2897(3147, 3448, 0, NORTH, 0)
        BANKER_2898(3148, 3448, 0, NORTH, 0)
        PHILOP(3150, 3407, 0, SOUTH, 0)
        3500(3151, 3410, 0, NORTH, 0)
        RAT_2854(3151, 3414, 0, SOUTH, 14)
        MAN_3106(3154, 3428, 0, SOUTH, 5)
        KANEL(3155, 3406, 0, <PERSON>OUTH, 0)
        DRAUL_LEPTOC(3161, 3435, 0, SOUT<PERSON>, 3)
        STRAY_DOG_2922(3168, 3440, 0, SOUTH, 7)
        GUARD_3010(3173, 3429, 0, SOUTH, 5)
        GUARD_3010(3175, 3423, 0, SOUTH, 5)
        GUARD_3011(3175, 3428, 0, SOUTH, 5)
        IMP_5007(3177, 3405, 0, SOUTH, 100)
        GUARD_3010(3180, 3401, 0, SOUTH, 5)
        6708(3183, 3406, 0, SOUTH, 10)
        6708(3185, 3440, 0, SOUTH, 10)
        MASTER_SMITHING_TUTOR(3187, 3423, 0, SOUTH, 2)
        6521(3187, 3436, 0, WEST, 0)
        BANKER_2898(3187, 3438, 0, WEST, 0)
        BANKER_2897(3187, 3440, 0, WEST, 0)
        BANKER_2898(3187, 3442, 0, WEST, 0)
        BANKER_2897(3187, 3444, 0, WEST, 0)
        BANKER_8666(3187, 3446, 0, SOUTH, 5)
        MAN_3108(3194, 3396, 0, SOUTH, 11)
        APOTHECARY(3195, 3404, 0, SOUTH, 3)
        6708(3198, 3429, 0, SOUTH, 10)
        6268(3158, 3425, 1, SOUTH, 2)
        PHILLIPA(3162, 3430, 1, SOUTH, 2)
        GUARD_3010(3175, 3403, 1, SOUTH, 5)
        GUARD_3011(3175, 3414, 1, SOUTH, 5)
    }
}