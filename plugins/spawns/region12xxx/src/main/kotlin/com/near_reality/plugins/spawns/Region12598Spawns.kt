package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12598Spawns : NPCSpawnsScript() {
    init {
        //SARADOMINIST_RECRUITER(3141, 3472, 0, SOUTH, 5)
        //ZAMORAKIAN_RECRUITER(3143, 3470, 0, SOUTH, 5)
        //MEREDITH(3143, 3472, 0, SOUTH, 5)
        //6708(3145, 3508, 0, SOUTH, 10)
        //6708(3148, 3477, 0, SOUTH, 10)
        //5885(3170, 3461, 0, SOUTH, 2)
        //6708(3176, 3508, 0, SOUTH, 10)
        //6708(3180, 3474, 0, <PERSON>O<PERSON><PERSON>, 10)
        //CLERK_10759(3181, 3471, 0, SOUTH, 5)
        //ABIGAILA(3185, 3516, 0, SOUTH, 0)
        //6708(3193, 3489, 0, SOUTH, 10)
        //RELOBO_BLINYO_LOGS(3152, 3489, 0, WEST, 0)
        //BOB_BARTER_HERBS(3156, 3481, 0, EAST, 0)
        //HOFUTHAND_WEAPONS_AND_ARMOUR(3156, 3498, 0, NORTH, 0)
        //BANKER_1633(3163, 3489, 0, WEST, 0)
        //6528(3163, 3490, 0, WEST, 0)
        //GRAND_EXCHANGE_CLERK_2149(3164, 3488, 0, SOUTH_EAST, 0)
        //GRAND_EXCHANGE_CLERK_2151(3164, 3491, 0, NORTH, 0)
        //BRUGSEN_BURSEN(3165, 3477, 0, NORTH, 0)
        //GRAND_EXCHANGE_CLERK(3165, 3488, 0, SOUTH, 0)
        //GRAND_EXCHANGE_CLERK_2150(3165, 3491, 0, NORTH_EAST, 0)
        //BANKER_1634(3166, 3489, 0, EAST, 0)
        //BANKER_3089(3166, 3490, 0, EAST, 0)
        //MURKY_MATT_RUNES(3173, 3481, 0, SOUTH, 0)
        //FARID_MORRISANE_ORES_AND_BARS(3173, 3498, 0, SOUTH, 0)
    }
}