package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12599Spawns : NPCSpawnsScript() {
    init {
        THUG(3160, 3568, 0, SOUTH, 2)
        THUG(3167, 3576, 0, SOUTH, 2)
        THUG(3171, 3579, 0, SOUTH, 2)
        THUG(3172, 3561, 0, SOUTH, 2)
        THUG(3175, 3572, 0, SOUTH, 2)
        THUG(3179, 3564, 0, SOUTH, 2)
        THUG(3184, 3580, 0, SOUTH, 2)
        THUG(3189, 3563, 0, SOUTH, 2)
        THUG(3190, 3570, 0, SOUTH, 2)
        CRIMSON_SWIFT(3159, 3542, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        CRIMSON_SWIFT(3160, 3542, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        CRIMSON_SWIFT(3161, 3542, 0, SOUTH, 4)
        CRIMSON_SWIFT(3162, 3542, 0, SOUTH, 4)
        CRIMSON_SWIFT(3163, 3542, 0, SOUTH, 4)
        CRIMSON_SWIFT(3164, 3542, 0, SOUTH, 4)
    }
}