package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12601Spawns : NPCSpawnsScript() {
    init {
        GREEN_DRAGON(3137, 3707, 0, SOUTH, 2)
        GREEN_DRAGON_264(3140, 3700, 0, SOUTH, 3)
        GREEN_DRAGON_263(3144, 3707, 0, SOUTH, 4)
        GREEN_DRAGON_262(3149, 3695, 0, SOUTH, 4)
        ZOMBIE_45(3152, 3674, 0, <PERSON>OUTH, 2)
        ZOMBIE_47(3153, 3658, 0, SOUTH, 9)
        ZOMBIE_60(3153, 3676, 0, S<PERSON><PERSON><PERSON>, 5)
        6112(3157, 3667, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        ZOMBIE_47(3157, 3681, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        ZOMBIE_46(3158, 3676, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        GREEN_DRAGON_261(3158, 3708, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        ZOMBIE_61(3162, 3665, 0, SOUTH, 6)
        ZOMBIE_59(3163, 3685, 0, SOUTH, 7)
        ZOMBIE_48(3165, 3678, 0, SOUTH, 7)
        ZOMBIE_46(3166, 3663, 0, SOUTH, 6)
        ZOMBIE_63(3168, 3668, 0, SOUTH, 5)
        ZOMBIE_62(3171, 3675, 0, SOUTH, 4)
        ZOMBIE_45(3172, 3669, 0, SOUTH, 2)
        ZOMBIE_45(3173, 3685, 0, SOUTH, 2)
        ZOMBIE_45(3175, 3680, 0, SOUTH, 2)
        ZOMBIE_46(3176, 3677, 0, SOUTH, 6)
        ZOMBIE_61(3176, 3682, 0, SOUTH, 6)
        ZOMBIE_46(3177, 3686, 0, SOUTH, 6)
        ZOMBIE_46(3179, 3680, 0, SOUTH, 6)
        ZOMBIE_47(3180, 3684, 0, SOUTH, 9)
        ZOMBIE_48(3181, 3675, 0, SOUTH, 7)
        ZOMBIE_48(3182, 3682, 0, SOUTH, 7)
        FISHING_SPOT_1530(3172, 3648, 0, SOUTH, 0)
    }
}