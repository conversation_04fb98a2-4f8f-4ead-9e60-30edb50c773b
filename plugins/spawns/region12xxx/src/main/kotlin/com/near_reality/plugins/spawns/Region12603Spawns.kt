package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12603Spawns : NPCSpawnsScript() {
    init {
        BLACK_CHINCHOMPA(3136, 3782, 0, SOUT<PERSON>, 8)
        SKELETON_78(3137, 3798, 0, SOUT<PERSON>, 8)
        BLACK_CHINCHOMPA(3139, 3780, 0, SOUTH, 8)
        MOSS_GIANT_2091(3139, 3818, 0, SOUTH, 4)
        BLACK_CHINCHOMPA(3140, 3784, 0, <PERSON><PERSON>UT<PERSON>, 8)
        MOSS_GIANT_2091(3140, 3805, 0, SOUTH, 4)
        MOSS_GIANT_2093(3141, 3826, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        SKELETON_77(3143, 3802, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        MOSS_GIANT(3144, 3822, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MOSS_GIANT_2093(3145, 3812, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MOSS_GIANT_2093(3147, 3805, 0, SOUTH, 4)
        SKELETON_78(3179, 3786, 0, SOUTH, 8)
        LAVA_DRAGON(3187, 3812, 0, SOUTH, 4)
        LAVA_DRAGON(3190, 3834, 0, SOUTH, 4)
    }
}