package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12604Spawns : NPCSpawnsScript() {
    init {
        GIANT_SPIDER_3018(3162, 3882, 0, SOUTH, 10)
        SPIDER_3019(3162, 3887, 0, SOUT<PERSON>, 8)
        SPIDER_3019(3163, 3879, 0, SOUTH, 8)
        GIANT_SPIDER_3018(3163, 3892, 0, SOUTH, 10)
        SPIDER_3019(3164, 3881, 0, SOUTH, 8)
        SPIDER_3019(3164, 3887, 0, SOUTH, 8)
        GIANT_SPIDER_3018(3165, 3889, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        GIANT_SPIDER_3018(3165, 3895, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        GIANT_SPIDER_3018(3166, 3883, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        SPIDER_3019(3166, 3886, 0, <PERSON><PERSON>UT<PERSON>, 8)
        SPIDER_3019(3167, 3889, 0, SOUTH, 8)
        G<PERSON>NT_SPIDER_3018(3168, 3877, 0, SOUTH, 10)
        SPIDER_3019(3168, 3886, 0, SOUTH, 8)
        SPIDER_3019(3168, 3893, 0, SOUTH, 8)
        SPIDER_3019(3169, 3883, 0, SOUTH, 8)
        GIANT_SPIDER_3018(3169, 3888, 0, SOUTH, 10)
        SPIDER_3019(3171, 3881, 0, SOUTH, 8)
        GIANT_SPIDER_3018(3171, 3883, 0, SOUTH, 10)
        SPIDER_3019(3171, 3889, 0, SOUTH, 8)
        GIANT_SPIDER_3018(3171, 3894, 0, SOUTH, 10)
        SPIDER_3019(3173, 3882, 0, SOUTH, 8)
        SPIDER_3019(3173, 3885, 0, SOUTH, 8)
        GIANT_SPIDER_3018(3173, 3888, 0, SOUTH, 10)
        SPIDER_3019(3174, 3879, 0, SOUTH, 8)
        SPIDER_3019(3176, 3887, 0, SOUTH, 8)
        GIANT_SPIDER_3018(3177, 3877, 0, SOUTH, 10)
        SPIDER_3019(3177, 3883, 0, SOUTH, 8)
        SPIDER_3019(3178, 3885, 0, SOUTH, 8)
        GIANT_SPIDER_3018(3181, 3887, 0, SOUTH, 10)
    }
}