package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12605Spawns : NPCSpawnsScript() {
    init {
        HELLHOUND(3169, 3956, 0, SOUTH, 7)
        HELLHOUND(3172, 3944, 0, SOUTH, 7)
        HELLHOUND(3176, 3950, 0, SOUTH, 7)
        RUNITE_GOLEM(3178, 3938, 0, SOUTH, 3)
        HELLHOUND(3180, 3906, 0, SOUTH, 7)
        HELLHOUND(3180, 3917, 0, SOUTH, 7)
        FISHING_SPOT_1536(3181, 3926, 0, SOUTH, 0)
        FISHING_SPOT_1536(3183, 3926, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MANDRITH(3183, 3945, 0, NORTH, 0)
        FISHING_SPOT_1536(3185, 3926, 0, <PERSON>O<PERSON>H, 0)
        PILES(3185, 3933, 0, <PERSON>O<PERSON><PERSON>, 2)
        FISHING_SPOT_1536(3187, 3927, 0, SOUTH, 0)
        MAGIC_AXE(3187, 3959, 0, SOUTH, 6)
        MAGIC_AXE(3188, 3961, 0, SOUTH, 6)
        MAGIC_AXE(3189, 3960, 0, SOUTH, 6)
        MAGIC_AXE(3190, 3959, 0, SOUTH, 6)
        HELLHOUND(3191, 3914, 0, SOUTH, 7)
        RUNITE_GOLEM(3191, 3934, 0, SOUTH, 3)
        MAGIC_AXE(3191, 3959, 0, SOUTH, 6)
        MAGIC_AXE(3191, 3961, 0, SOUTH, 6)
        MAGIC_AXE(3192, 3960, 0, SOUTH, 6)
        MAGIC_AXE(3193, 3958, 0, SOUTH, 6)
        MAGIC_AXE(3194, 3960, 0, SOUTH, 6)
    }
}