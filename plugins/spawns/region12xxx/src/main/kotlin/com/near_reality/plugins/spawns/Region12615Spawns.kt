package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12615Spawns : NPCSpawnsScript() {
    init {
        ZOMBIE_6459(3141, 4557, 0, SOUTH, 0)
        ZOMBIE_6451(3141, 4598, 0, SOUTH, 0)
        ZOMBIE_6460(3143, 4555, 0, SOUTH, 9)
        SKELETON_6442(3143, 4595, 0, SOUTH, 0)
        SKELETON_6442(3145, 4598, 0, SOUTH, 0)
        ZOMBIE_6449(3146, 4596, 0, SOUTH, 0)
        ZOMBIE_6458(3154, 4559, 0, SOUTH, 7)
        ZOMBIE_6453(3155, 4583, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        SKELETON_6445(3157, 4568, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKELETON_6447(3158, 4552, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETON_6443(3158, 4584, 0, <PERSON><PERSON><PERSON>H, 6)
        SKELETON_6443(3159, 4577, 0, SOUTH, 6)
        ZOMBIE_6461(3163, 4559, 0, SOUTH, 3)
        SKELETON_6447(3163, 4569, 0, SOUTH, 7)
        ZOM<PERSON>E_6449(3163, 4598, 0, SOUTH, 0)
        G<PERSON>NT_SK<PERSON><PERSON>ON_6440(3165, 4598, 0, SOUTH, 0)
        GIANT_SKELETON_6440(3167, 4577, 0, SOUTH, 0)
        ZOMBIE_6450(3167, 4589, 0, SOUTH, 2)
        SKELETON_6443(3169, 4587, 0, SOUTH, 6)
        ZOMBIE_6451(3170, 4592, 0, SOUTH, 0)
        ZOMBIE_6450(3172, 4598, 0, SOUTH, 2)
        ZOMBIE_6465(3173, 4550, 0, SOUTH, 8)
        SKELETON_6447(3177, 4549, 0, SOUTH, 7)
        SKELETON_6446(3182, 4562, 0, SOUTH, 0)
        SKELETON_6442(3182, 4577, 0, SOUTH, 0)
        SKELETON_6447(3184, 4550, 0, SOUTH, 7)
        SKELETON_6446(3185, 4560, 0, SOUTH, 0)
        SKELETON_6445(3185, 4562, 0, SOUTH, 8)
        SKELETON_6442(3187, 4578, 0, SOUTH, 0)
        ZOMBIE_6465(3190, 4551, 0, SOUTH, 8)
        ZOMBIE_6466(3190, 4561, 0, SOUTH, 3)
        ODOVACAR(3194, 4569, 0, SOUTH, 5)
        ZOMBIE_6463(3141, 4547, 1, SOUTH, 0)
        ZOMBIE_6460(3144, 4549, 1, SOUTH, 9)
        ZOMBIE_6451(3144, 4589, 1, SOUTH, 0)
        ZOMBIE_6454(3145, 4574, 1, SOUTH, 5)
        ZOMBIE_6452(3145, 4583, 1, SOUTH, 2)
        ZOMBIE_6461(3146, 4565, 1, SOUTH, 3)
        ZOMBIE_6457(3151, 4573, 1, SOUTH, 1)
        SKELETON_6443(3152, 4569, 1, SOUTH, 6)
        ZOMBIE_6449(3155, 4591, 1, SOUTH, 0)
        SKELETON_6447(3158, 4560, 1, SOUTH, 7)
        SKELETON_6442(3158, 4589, 1, SOUTH, 0)
        ZOMBIE_6462(3159, 4603, 1, SOUTH, 5)
        ZOMBIE_6464(3170, 4559, 1, SOUTH, 3)
        ZOMBIE_6458(3171, 4571, 1, SOUTH, 7)
        SKELETON_6447(3174, 4566, 1, SOUTH, 7)
        ZOMBIE_6456(3175, 4579, 1, SOUTH, 4)
        ZOMBIE_6453(3176, 4576, 1, SOUTH, 6)
        ZOMBIE_6458(3179, 4586, 1, SOUTH, 7)
        ZOMBIE_6454(3182, 4586, 1, SOUTH, 5)
        ZOMBIE_6460(3190, 4587, 1, SOUTH, 9)
        SKELETON_6441(3191, 4595, 1, SOUTH, 0)
        SKELETON_6441(3191, 4601, 1, SOUTH, 0)
        SKELETON_6442(3194, 4599, 1, SOUTH, 0)
        SKELETON_6447(3195, 4577, 1, SOUTH, 7)
        ZOMBIE_6461(3195, 4582, 1, SOUTH, 3)
        ZOMBIE_6455(3140, 4557, 2, SOUTH, 3)
        ZOMBIE_6456(3142, 4560, 2, SOUTH, 4)
        SKELETON_6444(3156, 4567, 2, SOUTH, 0)
        ZOMBIE_6456(3159, 4569, 2, SOUTH, 4)
        ZOMBIE_6459(3182, 4577, 2, SOUTH, 0)
        ZOMBIE_6460(3186, 4576, 2, SOUTH, 9)
        ZOMBIE_6455(3188, 4578, 2, SOUTH, 3)
    }
}