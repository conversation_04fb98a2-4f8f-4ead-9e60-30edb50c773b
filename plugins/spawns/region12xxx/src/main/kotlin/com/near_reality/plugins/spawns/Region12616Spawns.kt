package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12616Spawns : NPCSpawnsScript() {
    init {
        TERROR_DOG_6474(3140, 4647, 0, SOUTH, 4)
        TERROR_DOG_6474(3140, 4653, 0, SOUTH, 4)
        TERROR_DOG(3146, 4648, 0, SOUTH, 8)
        TERROR_DOG_6474(3146, 4656, 0, SOUTH, 4)
        TERROR_DOG(3148, 4645, 0, SOUTH, 8)
        TERROR_DOG(3151, 4653, 0, SOUTH, 8)
        TERROR_DOG(3152, 4647, 0, SOUTH, 8)
        TERROR_DOG_6474(3158, 4647, 0, SOUTH, 4)
        TERROR_DOG_6474(3158, 4653, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
    }
}