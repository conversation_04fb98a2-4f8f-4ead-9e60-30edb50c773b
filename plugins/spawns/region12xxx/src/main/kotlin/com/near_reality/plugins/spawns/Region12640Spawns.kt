package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12640Spawns : NPCSpawnsScript() {
    init {
        DIRE_WOLF_9181(3156, 6177, 0, SOUTH, 5)
        DIRE_WOLF(3167, 6169, 0, SOUTH, 12)
        DIRE_WOLF(3179, 6158, 0, SOUTH, 12)
        BLACK_DRAGON_253(3179, 6179, 0, SOUTH, 2)
        POSSESSED_PICKAXE_6469(3188, 6171, 0, SOUTH, 0)
        SHADOW_4004(3188, 6183, 0, SOUTH, 5)
        POSSESSED_PICKAXE_6469(3190, 6168, 0, <PERSON>O<PERSON><PERSON>, 0)
        SHADOW_4004(3191, 6181, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SHADOW_4004(3191, 6184, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SHADOW_4004(3192, 6179, 0, <PERSON><PERSON><PERSON>H, 5)
        SHADOW_4004(3194, 6180, 0, SOUTH, 5)
        DIRE_WOLF(3196, 6161, 0, SOUTH, 12)
    }
}