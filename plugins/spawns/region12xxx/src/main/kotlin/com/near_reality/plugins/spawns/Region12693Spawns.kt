package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12693Spawns : NPCSpawnsScript() {
    init {
        CAVE_BUG_LARVA(3148, 9576, 0, SOUTH, 4)
        CAVE_BUG(3149, 9578, 0, SOUTH, 12)
        CAVE_SLIME(3150, 9555, 0, SOUTH, 9)
        CAVE_BUG(3150, 9571, 0, SOUTH, 12)
        BIG_FROG(3151, 9550, 0, SOUTH, 3)
        BIG_FROG(3151, 9562, 0, SOUTH, 3)
        CAVE_BUG_LARVA(3152, 9551, 0, SOUTH, 4)
        CAVE_BUG(3152, 9576, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        CAVE_SLIME(3153, 9546, 0, <PERSON>O<PERSON><PERSON>, 9)
        CAVE_BUG_LARVA(3153, 9559, 0, SOUTH, 4)
        CAVE_BUG_LARVA(3153, 9574, 0, SOUTH, 4)
        FISHING_SPOT_1497(3154, 9544, 0, SOUTH, 0)
        BIG_FROG(3154, 9556, 0, SOUTH, 3)
        CAVE_BUG(3156, 9558, 0, SOUTH, 12)
        CAVE_SLIME(3157, 9544, 0, SOUTH, 9)
        CAVE_BUG_LARVA(3157, 9547, 0, SOUTH, 4)
        CAVE_SLIME(3157, 9561, 0, SOUTH, 9)
        BIG_FROG(3159, 9554, 0, SOUTH, 3)
        CAVE_BUG_LARVA(3159, 9572, 0, SOUTH, 4)
        CAVE_SLIME(3159, 9591, 0, SOUTH, 9)
        CAVE_BUG_LARVA(3160, 9589, 0, SOUTH, 4)
        HOLE_IN_THE_WALL(3161, 9547, 0, SOUTH, 0)
        CAVE_SLIME(3162, 9557, 0, SOUTH, 9)
        HOLE_IN_THE_WALL(3162, 9574, 0, SOUTH, 0)
        CAVE_BUG_LARVA(3163, 9547, 0, SOUTH, 4)
        CAVE_BUG(3164, 9542, 0, SOUTH, 12)
        HOLE_IN_THE_WALL(3164, 9556, 0, SOUTH, 0)
        CAVE_SLIME(3164, 9591, 0, SOUTH, 9)
        CAVE_BUG_LARVA(3166, 9587, 0, SOUTH, 4)
        CAVE_BUG_LARVA(3167, 9545, 0, SOUTH, 4)
        CAVE_BUG_LARVA(3167, 9573, 0, SOUTH, 4)
        CAVE_BUG_LARVA(3168, 9556, 0, SOUTH, 4)
        6280(3168, 9572, 0, SOUTH, 3)
        CAVE_SLIME(3168, 9592, 0, SOUTH, 9)
        CAVE_BUG(3169, 9546, 0, SOUTH, 12)
        CAVE_BUG_LARVA(3170, 9571, 0, SOUTH, 4)
        6281(3170, 9572, 0, SOUTH, 2)
        CAVE_BUG(3172, 9558, 0, SOUTH, 12)
        CAVE_BUG_LARVA(3175, 9585, 0, SOUTH, 4)
        CAVE_SLIME(3178, 9548, 0, SOUTH, 9)
        CAVE_BUG(3178, 9558, 0, SOUTH, 12)
        CAVE_SLIME(3180, 9543, 0, SOUTH, 9)
        CAVE_SLIME(3181, 9546, 0, SOUTH, 9)
        CAVE_BUG_LARVA(3181, 9549, 0, SOUTH, 4)
        CAVE_BUG(3181, 9585, 0, SOUTH, 12)
        CAVE_BUG(3182, 9556, 0, SOUTH, 12)
        BIG_FROG(3183, 9543, 0, SOUTH, 3)
        CAVE_BUG(3184, 9550, 0, SOUTH, 12)
        CAVE_BUG_LARVA(3184, 9555, 0, SOUTH, 4)
        CAVE_BUG_LARVA(3184, 9581, 0, SOUTH, 4)
        CAVE_BUG_LARVA(3185, 9571, 0, SOUTH, 4)
        CAVE_CRAWLER(3186, 9582, 0, SOUTH, 6)
        CAVE_BUG_LARVA(3187, 9550, 0, SOUTH, 4)
        CAVE_BUG(3187, 9561, 0, SOUTH, 12)
        CAVE_BUG(3187, 9577, 0, SOUTH, 12)
        CAVE_BUG(3189, 9550, 0, SOUTH, 12)
        CAVE_CRAWLER_409(3189, 9566, 0, SOUTH, 5)
        CAVE_CRAWLER_408(3189, 9574, 0, SOUTH, 4)
        CAVE_BUG(3191, 9571, 0, SOUTH, 12)
        CAVE_BUG(3191, 9582, 0, SOUTH, 12)
        CAVE_BUG_LARVA(3193, 9577, 0, SOUTH, 4)
        CAVE_BUG_LARVA(3194, 9553, 0, SOUTH, 4)
        CAVE_BUG_LARVA(3194, 9570, 0, SOUTH, 4)
        HOLE_IN_THE_WALL(3198, 9554, 0, SOUTH, 0)
        HOLE_IN_THE_WALL(3198, 9572, 0, SOUTH, 0)
    }
}