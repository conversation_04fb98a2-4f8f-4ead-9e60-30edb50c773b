package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12694Spawns : NPCSpawnsScript() {
    init {
        HAM_MEMBER_4327(3144, 9619, 0, SOUTH, 0)
        HAM_GUARD_2537(3147, 9620, 0, SOUTH, 4)
        HAM_MEMBER_2541(3149, 9624, 0, SOUTH, 4)
        HAM_MEMBER_4327(3150, 9621, 0, SOUTH, 0)
        HAM_GUARD_2538(3150, 9650, 0, SOUTH, 4)
        HAM_MEMBER_2542(3151, 9625, 0, WEST, 0)
        HAM_MEMBER_4327(3152, 9627, 0, SOUTH, 0)
        HAM_GUARD_2537(3152, 9647, 0, <PERSON>OUTH, 4)
        HAM_MEMBER_2542(3153, 9625, 0, WEST, 0)
        HAM_GUARD_2538(3155, 9629, 0, SOUTH, 4)
        HAM_MEMBER_4327(3157, 9612, 0, SOUTH, 0)
        HAM_GUARD_2537(3157, 9642, 0, SOUTH, 4)
        HAM_MEMBER_4327(3158, 9611, 0, SOUTH, 0)
        HAM_GUARD_2538(3158, 9629, 0, SOUTH, 4)
        HAM_GUARD(3159, 9613, 0, SOUTH, 4)
        HAM_GUARD_2537(3159, 9621, 0, SOUTH, 4)
        HAM_MEMBER_4327(3160, 9618, 0, SOUTH, 0)
        HAM_MEMBER_4327(3161, 9614, 0, SOUTH, 0)
        HAM_GUARD(3161, 9616, 0, SOUTH, 4)
        HAM_MEMBER_2541(3161, 9632, 0, SOUTH, 4)
        HAM_GUARD(3161, 9638, 0, SOUTH, 4)
        HAM_MEMBER_2543(3162, 9633, 0, SOUTH, 0)
        HAM_MEMBER_2543(3162, 9635, 0, SOUTH, 0)
        HAM_MEMBER_4327(3162, 9636, 0, SOUTH, 0)
        HAM_GUARD_2537(3164, 9626, 0, SOUTH, 4)
        HAM_MEMBER_4327(3164, 9630, 0, SOUTH, 0)
        HAM_MEMBER_2543(3164, 9631, 0, SOUTH, 0)
        HAM_MEMBER_2543(3164, 9633, 0, SOUTH, 0)
        HAM_MEMBER_2541(3164, 9634, 0, SOUTH, 4)
        HAM_GUARD_2537(3166, 9626, 0, SOUTH, 4)
        HAM_MEMBER_2541(3166, 9630, 0, SOUTH, 4)
        HAM_MEMBER_2543(3166, 9631, 0, SOUTH, 0)
        HAM_MEMBER_4327(3166, 9632, 0, SOUTH, 0)
        HAM_MEMBER_2541(3166, 9636, 0, SOUTH, 4)
        HAM_MEMBER_2543(3167, 9633, 0, SOUTH, 0)
        HAM_MEMBER_4327(3167, 9634, 0, SOUTH, 0)
        6290(3169, 9627, 0, SOUTH, 4)
        HAM_MEMBER_4327(3169, 9632, 0, SOUTH, 0)
        HAM_MEMBER_4327(3169, 9634, 0, SOUTH, 0)
        HAM_GUARD(3169, 9638, 0, SOUTH, 4)
        JOHANHUS_ULSBRECHT(3171, 9620, 0, SOUTH, 2)
        HAM_GUARD_2538(3171, 9629, 0, SOUTH, 4)
        HAM_GUARD_2537(3172, 9622, 0, SOUTH, 4)
        HAM_MEMBER_4327(3172, 9646, 0, SOUTH, 0)
        HAM_MEMBER_2541(3172, 9651, 0, SOUTH, 4)
        HAM_GUARD_2537(3173, 9648, 0, SOUTH, 4)
        HAM_MEMBER_4327(3173, 9655, 0, SOUTH, 0)
        HAM_MEMBER_2541(3174, 9643, 0, SOUTH, 4)
        HAM_GUARD_2537(3175, 9640, 0, SOUTH, 4)
        HAM_MEMBER_2541(3175, 9646, 0, SOUTH, 4)
        HAM_GUARD_2538(3177, 9617, 0, SOUTH, 4)
        HAM_MEMBER_2541(3177, 9630, 0, SOUTH, 4)
        HAM_GUARD_2538(3178, 9644, 0, SOUTH, 4)
        HAM_GUARD(3179, 9614, 0, SOUTH, 4)
        HAM_MEMBER_2542(3180, 9630, 0, WEST, 0)
        HAM_MEMBER_2542(3181, 9632, 0, WEST, 0)
        HAM_MEMBER_2541(3181, 9633, 0, SOUTH, 4)
        HAM_MEMBER_4327(3182, 9630, 0, SOUTH, 0)
        HAM_MEMBER_2542(3182, 9631, 0, WEST, 0)
        JIMMY_THE_CHISEL(3184, 9610, 0, SOUTH, 2)
        HAM_GUARD(3163, 9628, 0, SOUTH, 4)
        HAM_DEACON(3165, 9628, 0, SOUTH, 2)
        HAM_GUARD(3167, 9628, 0, SOUTH, 4)
    }
}