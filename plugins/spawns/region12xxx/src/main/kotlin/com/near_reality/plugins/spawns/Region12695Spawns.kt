package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12695Spawns : NPCSpawnsScript() {
    init {
        SOURHOG(3157, 9698, 0, SOUTH, 5)
        SOURHOG(3163, 9699, 0, SOUTH, 5)
        SOURHOG(3165, 9678, 0, SOUTH, 5)
        SOURHOG(3167, 9673, 0, SOUTH, 5)
        SOURHOG(3168, 9692, 0, SOUTH, 5)
        SOURHOG(3171, 9679, 0, SOUTH, 5)
        SOURHOG(3173, 9688, 0, SOUTH, 5)
        SOURHOG(3177, 9683, 0, SOUTH, 5)
    }
}