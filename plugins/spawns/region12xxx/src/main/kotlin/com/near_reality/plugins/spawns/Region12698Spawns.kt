package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12698Spawns : NPCSpawnsScript() {
    init {
        SKELETON_77(3137, 9871, 0, SOUT<PERSON>, 7)
        SKELETON_73(3138, 9874, 0, SOUTH, 8)
        SKELETON_78(3139, 9877, 0, SOUTH, 8)
        ZOMBIE_42(3139, 9885, 0, SOUTH, 6)
        ZOMBIE_43(3140, 9893, 0, <PERSON>OUTH, 6)
        SKELETON_81(3141, 9869, 0, <PERSON>O<PERSON><PERSON>, 6)
        SKELETON_79(3141, 9873, 0, SOUTH, 8)
        ZOMBIE_43(3143, 9904, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        SKELETON_80(3144, 9869, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        ZOMBIE_42(3146, 9892, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ZOMBIE_43(3147, 9883, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ZOMBIE_42(3147, 9899, 0, SOUTH, 6)
        VANNAKA(3147, 9914, 0, SOUTH, 3)
        ZOMBIE_44(3148, 9904, 0, SOUTH, 9)
        ZOMBIE_44(3150, 9889, 0, SOUTH, 9)
        ZOMBIE_42(3150, 9907, 0, SOUTH, 6)
        ZOMBIE_44(3151, 9884, 0, SOUTH, 9)
        MOSS_GIANT_2092(3155, 9903, 0, SOUTH, 3)
        MOSS_GIANT(3156, 9906, 0, SOUTH, 4)
        MOSS_GIANT_2091(3158, 9895, 0, SOUTH, 4)
        MOSS_GIANT(3158, 9898, 0, SOUTH, 4)
        MOSS_GIANT_2091(3158, 9904, 0, SOUTH, 4)
        MOSS_GIANT_2093(3159, 9901, 0, SOUTH, 4)
        MOSS_GIANT(3163, 9877, 0, SOUTH, 4)
        MOSS_GIANT_2091(3164, 9880, 0, SOUTH, 4)
        MOSS_GIANT_2093(3166, 9883, 0, SOUTH, 4)
        MOSS_GIANT_2092(3167, 9880, 0, SOUTH, 3)
        DEADLY_RED_SPIDER(3171, 9884, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3174, 9891, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3175, 9881, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3176, 9883, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3178, 9880, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3178, 9888, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3180, 9883, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3183, 9885, 0, SOUTH, 8)
        DEADLY_RED_SPIDER(3186, 9892, 0, SOUTH, 8)
    }
}