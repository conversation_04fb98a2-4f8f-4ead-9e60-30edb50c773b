package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12737Spawns : NPCSpawnsScript() {
    init {
        IORWERTH_WARRIOR_9502(3177, 12401, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9503(3177, 12405, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9502(3179, 12410, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9503(3180, 12399, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9502(3180, 12403, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9502(3183, 12399, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9503(3183, 12411, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9503(3187, 12407, 0, <PERSON>O<PERSON>H, 5)
        IORWERTH_WARRIOR_9502(3188, 12411, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9502(3190, 12406, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9503(3191, 12402, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9503(3192, 12411, 0, SOUTH, 5)
        IORWERTH_WARRIOR_9502(3194, 12410, 0, SOUTH, 5)
    }
}