package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12738Spawns : NPCSpawnsScript() {
    init {
        DARK_BEAST(3158, 12427, 0, SOUTH, 16)
        DARK_BEAST(3161, 12422, 0, SOUTH, 16)
        WATERFIEND(3161, 12457, 0, SOUTH, 16)
        WATERFIEND(3163, 12448, 0, SOUTH, 16)
        WATERFIEND(3163, 12454, 0, SOUTH, 16)
        DARK_BEAST(3165, 12431, 0, SOUTH, 16)
        WATERFIEND(3168, 12447, 0, SOUTH, 16)
        WATERFIEND(3168, 12454, 0, SOUTH, 16)
        DARK_BEAST(3169, 12425, 0, <PERSON>O<PERSON><PERSON>, 16)
        WATERFIEND(3170, 12451, 0, SOUTH, 16)
        WATERFIEND(3172, 12474, 0, SOUTH, 16)
        WATERFIEND(3174, 12475, 0, SOUTH, 16)
        WATERFIEND(3175, 12471, 0, SOUTH, 16)
        WATERFIEND(3178, 12469, 0, SOUTH, 16)
        WATERFIEND(3179, 12475, 0, SOUTH, 16)
        WATERFIEND(3182, 12476, 0, SOUTH, 16)
        WATERFIEND(3184, 12469, 0, SOUTH, 16)
        WATERFIEND(3184, 12472, 0, SOUTH, 16)
        WATERFIEND(3185, 12445, 0, SOUTH, 16)
        WATERFIEND(3185, 12448, 0, SOUTH, 16)
        WATERFIEND(3187, 12451, 0, SOUTH, 16)
        WATERFIEND(3190, 12447, 0, SOUTH, 16)
        WATERFIEND(3192, 12450, 0, SOUTH, 16)
        WATERFIEND(3192, 12454, 0, SOUTH, 16)
    }
}