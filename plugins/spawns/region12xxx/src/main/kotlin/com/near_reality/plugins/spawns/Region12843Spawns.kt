package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12843Spawns : NPCSpawnsScript() {
    init {
        CRAB_1553(3236, 2778, 0, SOUT<PERSON>, 4)
        24(3243, 2812, 0, SOUTH, 4)
        1322(3243, 2813, 0, SOUTH, 2)
    }
}