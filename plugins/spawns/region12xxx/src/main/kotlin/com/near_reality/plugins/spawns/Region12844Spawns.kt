package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12844Spawns : NPCSpawnsScript() {
    init {
        JACKAL(3212, 2863, 0, SOUTH, 11)
        JACKAL(3213, 2866, 0, SOUTH, 11)
        VULTURE(3215, 2841, 0, SOUTH, 4)
        JACKAL(3215, 2863, 0, SOUTH, 11)
        JACKAL(3216, 2865, 0, SOUTH, 11)
        JACKAL(3216, 2868, 0, SOUTH, 11)
        VULTURE(3217, 2845, 0, SOUTH, 4)
        JACKAL(3218, 2831, 0, SOUTH, 11)
        JACKAL(3220, 2830, 0, <PERSON>O<PERSON><PERSON>, 11)
        J<PERSON><PERSON><PERSON>(3220, 2833, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        VULTURE(3220, 2841, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        JACKAL(3221, 2831, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        JACKAL(3238, 2845, 0, <PERSON>O<PERSON><PERSON>, 11)
        JACKAL(3240, 2845, 0, SOUTH, 11)
        JACKAL(3241, 2843, 0, SOUTH, 11)
        JACKAL(3241, 2847, 0, SOUTH, 11)
        CROCODILE(3258, 2868, 0, SOUTH, 6)
        CROC<PERSON><PERSON>E(3259, 2823, 0, SOUTH, 6)
        CROCODILE(3259, 2845, 0, SOUTH, 6)
        CROCODILE(3260, 2830, 0, SOUTH, 6)
        CROCODILE(3260, 2859, 0, SOUTH, 6)
        CROCODILE(3261, 2819, 0, SOUTH, 6)
        CROCODILE(3261, 2851, 0, SOUTH, 6)
    }
}