package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12846Spawns : NPCSpawnsScript() {
    init {
        1967(3213, 2956, 0, SOUTH, 4)
        DESERT_WOLF(3237, 2968, 0, SOUTH, 3)
        UGTHANKI(3237, 3000, 0, SOUTH, 6)
        UGTHANKI(3245, 2960, 0, SOUTH, 6)
    }
}