package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12847Spawns : NPCSpawnsScript() {
    init {
        UGTHANKI(3208, 3032, 0, SOUTH, 6)
        UGTHANKI(3217, 3064, 0, SOUTH, 6)
        DESERT_WOLF_4650(3223, 3011, 0, SOUTH, 4)
        DESERT_WOLF_4651(3223, 3035, 0, SOUTH, 5)
        DESERT_WOLF(3224, 3013, 0, SOUTH, 3)
        DESERT_WOLF_4651(3225, 3011, 0, SOUTH, 5)
        DESERT_WOLF(3225, 3034, 0, SOUTH, 3)
        DESERT_WOLF(3226, 3060, 0, SOUTH, 3)
        DESERT_WOLF_4650(3228, 3060, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        UGTHANKI(3238, 3015, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        DESERT_WOLF(3250, 3057, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        DESERT_<PERSON><PERSON>F_4650(3251, 3059, 0, SOUTH, 4)
        UGTHANKI(3258, 3063, 0, SOUTH, 6)
    }
}