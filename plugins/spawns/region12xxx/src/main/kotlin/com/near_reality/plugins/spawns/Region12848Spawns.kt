package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12848Spawns : NPCSpawnsScript() {
    init {
        GOAT_1795(3206, 3082, 0, SOUTH, 4)
        GOAT_1795(3207, 3081, 0, SOUTH, 4)
        GOAT_1796(3207, 3083, 0, SOUTH, 4)
        BILLY_GOAT_1797(3208, 3080, 0, SOUTH, 4)
        GOAT_1796(3208, 3082, 0, SOUTH, 4)
        GOAT_1796(3209, 3081, 0, SOUTH, 4)
        DESERT_WOLF_4651(3215, 3092, 0, SOUTH, 5)
        DESERT_WOLF(3217, 3092, 0, <PERSON>O<PERSON><PERSON>, 3)
        UGTHANKI(3217, 3111, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        DESERT_WOLF_4651(3217, 3124, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        DESERT_WOLF_4650(3219, 3125, 0, SOUTH, 4)
        UGTHANKI(3222, 3086, 0, SOUTH, 6)
        WEIRD_OLD_MAN(3227, 3106, 0, SOUTH, 11)
        DESERT_WOLF_4650(3233, 3075, 0, SOUTH, 4)
        DESERT_WOLF(3235, 3074, 0, SOUTH, 3)
        UGTHANKI(3238, 3101, 0, SOUTH, 6)
        UGTHANKI(3244, 3080, 0, SOUTH, 6)
        DESERT_WOLF_4650(3250, 3126, 0, SOUTH, 4)
        DESERT_WOLF(3252, 3125, 0, SOUTH, 3)
        UGTHANKI(3253, 3116, 0, SOUTH, 6)
        UGTHANKI(3255, 3095, 0, SOUTH, 6)
        DESERT_WOLF(3258, 3078, 0, SOUTH, 3)
        DESERT_WOLF_4651(3260, 3077, 0, SOUTH, 5)
    }
}