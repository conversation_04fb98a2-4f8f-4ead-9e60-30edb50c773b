package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12850Spawns : NPCSpawnsScript() {
    init {
        GIANT_SPIDER_3017(3200, 3238, 0, SOUTH, 10)
        GOBLIN_3032(3202, 3253, 0, SOUTH, 14)
        SHEEP_2694(3202, 3262, 0, SOUTH, 3)
        GIANT_SPIDER_3017(3203, 3239, 0, SOUTH, 10)
        SHEEP_2693(3204, 3259, 0, SOUTH, 3)
        SHEEP_2699(3204, 3261, 0, SOUTH, 2)
        RAT_2854(3205, 3203, 0, SOUTH, 14)
        RAT_2854(3205, 3204, 0, <PERSON>OUT<PERSON>, 14)
        RAT_2854(3205, 3209, 0, <PERSON>O<PERSON><PERSON>, 14)
        RAT_2854(3206, 3202, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        RAT_2854(3206, 3204, 0, SOUTH, 14)
        RAT_2854(3206, 3209, 0, SOUTH, 14)
        GOBLIN_3031(3206, 3252, 0, SOUTH, 11)
        LIL_LAMB(3206, 3260, 0, SOUTH, 5)
        RAT_2854(3207, 3202, 0, SOUTH, 14)
        RAT_2854(3207, 3203, 0, SOUTH, 14)
        MAN_3106(3207, 3227, 0, SOUTH, 5)
        HANS(3207, 3233, 0, SOUTH, 2)
        SHEEP_2693(3207, 3260, 0, SOUTH, 3)
        6709(3208, 3234, 0, SOUTH, 5)
        LAMB(3208, 3260, 0, SOUTH, 4)
        6709(3209, 3204, 0, SOUTH, 5)
        COOK_4626(3209, 3215, 0, SOUTH, 3)
        SHOP_KEEPER(3209, 3247, 0, SOUTH, 2)
        RAM_1263(3209, 3259, 0, SOUTH, 4)
        SHEEP_2699(3210, 3262, 0, SOUTH, 2)
        SHOP_ASSISTANT(3212, 3247, 0, SOUTH, 2)
        1980(3212, 3263, 0, SOUTH, 0)
        MAN_3106(3216, 3219, 0, SOUTH, 5)
        DONIE(3216, 3254, 0, SOUTH, 4)
        WOMAN_3111(3217, 3205, 0, SOUTH, 5)
        IMP_5007(3217, 3226, 0, SOUTH, 100)
        MAN_3108(3217, 3236, 0, SOUTH, 11)
        MAGIC_COMBAT_TUTOR(3217, 3238, 0, SOUTH, 2)
        RANGED_COMBAT_TUTOR(3218, 3239, 0, SOUTH, 2)
        GEE(3219, 3230, 0, SOUTH, 5)
        MELEE_COMBAT_TUTOR(3219, 3238, 0, SOUTH, 2)
        MAN_3108(3221, 3219, 0, SOUTH, 11)
        6709(3221, 3222, 0, SOUTH, 5)
        6709(3221, 3238, 0, SOUTH, 5)
        RAT_2854(3222, 3215, 0, SOUTH, 14)
        7458(3223, 3220, 0, SOUTH, 0)
        MAN_3106(3223, 3240, 0, SOUTH, 5)
        GOBLIN_3030(3224, 3262, 0, SOUTH, 13)
        317(3225, 3213, 0, SOUTH, 5)
        SMITHING_APPRENTICE(3227, 3253, 0, SOUTH, 2)
        8632(3228, 3242, 0, SOUTH, 5)
        WOODSMAN_TUTOR(3228, 3245, 0, SOUTH, 2)
        RAT_2854(3229, 3223, 0, SOUTH, 14)
        IRON_MAN_TUTOR(3229, 3228, 0, SOUTH, 0)
        ARTHUR_THE_CLUE_HUNTER(3229, 3236, 0, SOUTH, 5)
        MAN_6818(3229, 3238, 0, SOUTH, 5)
        7457(3230, 3215, 0, SOUTH, 0)
        MAN_3106(3231, 3207, 0, SOUTH, 5)
        BOB_10619(3232, 3203, 0, SOUTH, 5)
        119(3232, 3223, 0, SOUTH, 5)
        RAT_2854(3232, 3229, 0, SOUTH, 14)
        6709(3232, 3231, 0, SOUTH, 5)
        9245(3232, 3235, 0, SOUTH, 5)
        BARTENDER_7546(3232, 3241, 0, SOUTH, 5)
        HATIUS_COSAINTUS(3234, 3214, 0, SOUTH, 2)
        WOMAN_3111(3235, 3206, 0, SOUTH, 5)
        MAN_3107(3235, 3219, 0, SOUTH, 5)
        DONIE(3235, 3225, 0, SOUTH, 4)
        DUCK(3235, 3259, 0, SOUTH, 18)
        MAN_3108(3236, 3201, 0, SOUTH, 11)
        WOMAN_3112(3236, 3203, 0, SOUTH, 2)
        6709(3236, 3217, 0, SOUTH, 5)
        RAT_2854(3236, 3222, 0, SOUTH, 14)
        ACCOUNT_SECURITY_TUTOR(3238, 3200, 0, SOUTH, 5)
        LUMBRIDGE_GUIDE(3238, 3220, 0, SOUTH, 2)
        DUCK_1839(3238, 3238, 0, SOUTH, 20)
        DUCKLINGS(3238, 3239, 0, SOUTH, 2)
        DUCK(3238, 3244, 0, SOUTH, 18)
        DUCK_1839(3238, 3246, 0, SOUTH, 20)
        DUCKLINGS(3238, 3247, 0, SOUTH, 2)
        ROD_FISHING_SPOT_1527(3238, 3252, 0, SOUTH, 0)
        DUCK_1839(3239, 3235, 0, SOUTH, 20)
        ROD_FISHING_SPOT_1527(3239, 3241, 0, SOUTH, 0)
        GIANT_SPIDER_3017(3239, 3256, 0, SOUTH, 10)
        DUCK(3241, 3230, 0, SOUTH, 18)
        DUCK(3241, 3234, 0, SOUTH, 18)
        GIANT_SPIDER_3017(3241, 3241, 0, SOUTH, 10)
        GOBLIN_3029(3241, 3244, 0, SOUTH, 13)
        GIANT_SPIDER_3017(3241, 3249, 0, SOUTH, 10)
        GOBLIN_3033(3241, 3251, 0, SOUTH, 12)
        GOBLIN_3033(3242, 3242, 0, SOUTH, 12)
        6093(3243, 3201, 0, SOUTH, 2)
        PRAYER_TUTOR(3243, 3214, 0, SOUTH, 2)
        DUCK(3243, 3228, 0, SOUTH, 18)
        GIANT_SPIDER_3017(3243, 3236, 0, SOUTH, 10)
        FATHER_AERECK(3244, 3206, 0, SOUTH, 3)
        WOMAN_3113(3244, 3211, 0, SOUTH, 5)
        BARFY_BILL(3244, 3237, 0, SOUTH, 2)
        GOBLIN_3032(3244, 3245, 0, SOUTH, 14)
        GOBLIN_3029(3244, 3247, 0, SOUTH, 13)
        GOBLIN_3029(3244, 3251, 0, SOUTH, 13)
        GIANT_SPIDER_3017(3245, 3232, 0, SOUTH, 10)
        GEE(3245, 3261, 0, SOUTH, 5)
        GOBLIN_3032(3246, 3235, 0, SOUTH, 14)
        GOBLIN_3029(3246, 3236, 0, SOUTH, 13)
        GOBLIN_3029(3246, 3241, 0, SOUTH, 13)
        GOBLIN_3033(3246, 3244, 0, SOUTH, 12)
        GOBLIN_3031(3246, 3245, 0, SOUTH, 11)
        DUCK(3247, 3221, 0, SOUTH, 18)
        GIANT_SPIDER_3017(3247, 3236, 0, SOUTH, 10)
        GOBLIN_3031(3247, 3240, 0, SOUTH, 11)
        GOBLIN_3032(3247, 3245, 0, SOUTH, 14)
        GOBLIN_3032(3247, 3247, 0, SOUTH, 14)
        6487(3248, 3201, 0, SOUTH, 2)
        DUCK(3248, 3220, 0, SOUTH, 18)
        GOBLIN_3031(3248, 3229, 0, SOUTH, 11)
        GOBLIN_3033(3248, 3241, 0, SOUTH, 12)
        GOBLIN_3029(3249, 3243, 0, SOUTH, 13)
        GOBLIN_3032(3249, 3252, 0, SOUTH, 14)
        GOBLIN_3029(3249, 3256, 0, SOUTH, 13)
        GOBLIN_3034(3250, 3227, 0, SOUTH, 14)
        GOBLIN_3029(3250, 3228, 0, SOUTH, 13)
        GOBLIN_3030(3250, 3238, 0, SOUTH, 13)
        GOBLIN_3032(3250, 3256, 0, SOUTH, 14)
        GOBLIN_3034(3251, 3243, 0, SOUTH, 14)
        GOBLIN_3031(3251, 3252, 0, SOUTH, 11)
        GOBLIN_3032(3252, 3228, 0, SOUTH, 14)
        GOBLIN_3034(3252, 3246, 0, SOUTH, 14)
        GOBLIN_3031(3253, 3234, 0, SOUTH, 11)
        GOBLIN_3034(3253, 3241, 0, SOUTH, 14)
        GOBLIN_3033(3253, 3250, 0, SOUTH, 12)
        BUTTERFLY_236(3254, 3230, 0, SOUTH, 9)
        COW_2793(3254, 3255, 0, SOUTH, 7)
        COW_2791(3254, 3258, 0, SOUTH, 4)
        COW(3254, 3262, 0, SOUTH, 3)
        GOBLIN_3033(3255, 3222, 0, SOUTH, 12)
        GOBLIN_3033(3255, 3245, 0, SOUTH, 12)
        GOBLIN_3029(3255, 3247, 0, SOUTH, 13)
        GOBLIN_3031(3255, 3249, 0, SOUTH, 11)
        GOBLIN_3032(3255, 3252, 0, SOUTH, 14)
        GOBLIN_3031(3256, 3226, 0, SOUTH, 11)
        GOBLIN_3034(3258, 3220, 0, SOUTH, 14)
        GOBLIN_3033(3258, 3228, 0, SOUTH, 12)
        GOBLIN_3031(3258, 3245, 0, SOUTH, 11)
        GOBLIN_3030(3258, 3249, 0, SOUTH, 13)
        COW(3258, 3260, 0, SOUTH, 3)
        DUCK(3259, 3212, 0, SOUTH, 18)
        GOBLIN_3029(3259, 3223, 0, SOUTH, 13)
        GOBLIN_3033(3259, 3230, 0, SOUTH, 12)
        GOBLIN_3032(3260, 3229, 0, SOUTH, 14)
        GOBLIN_3034(3260, 3233, 0, SOUTH, 14)
        GOBLIN_3035(3260, 3237, 0, SOUTH, 11)
        GOBLIN_3036(3260, 3240, 0, SOUTH, 12)
        COW_2791(3261, 3259, 0, SOUTH, 4)
        GOBLIN_3032(3262, 3218, 0, SOUTH, 14)
        GOBLIN_3029(3263, 3220, 0, SOUTH, 13)
        GOBLIN_3033(3263, 3228, 0, SOUTH, 12)
        GOBLIN_3029(3263, 3243, 0, SOUTH, 13)
        6709(3206, 3218, 1, SOUTH, 5)
        MAN_3106(3206, 3219, 1, SOUTH, 5)
        MAN_3108(3209, 3215, 1, SOUTH, 11)
        6288(3209, 3219, 1, SOUTH, 2)
        CRAFTING_TUTOR(3210, 3212, 1, SOUTH, 2)
        ABIGAILA(3210, 3249, 1, SOUTH, 0)
        WOMAN_3111(3211, 3213, 1, SOUTH, 5)
        DUKE_HORACIO(3212, 3220, 1, SOUTH, 3)
        6709(3206, 3219, 2, SOUTH, 5)
        BANKER_TUTOR(3208, 3222, 2, SOUTH, 2)
        6520(3209, 3222, 2, SOUTH, 0)
        6709(3228, 3219, 2, SOUTH, 5)
    }
}