package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12851Spawns : NPCSpawnsScript() {
    init {
        COW_2793(3200, 3284, 0, SOUTH, 7)
        COW_CALF_2794(3200, 3299, 0, SOUTH, 3)
        COW_2791(3201, 3295, 0, SOUTH, 4)
        SHEEP_2786(3202, 3271, 0, SOUTH, 3)
        COW_2791(3202, 3289, 0, SOUTH, 4)
        COW(3203, 3286, 0, SOUTH, 3)
        COW_2793(3204, 3293, 0, SOUTH, 7)
        COW_2793(3204, 3298, 0, <PERSON>O<PERSON><PERSON>, 7)
        SHEEP_2695(3205, 3266, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        COW_CALF_2794(3205, 3285, 0, <PERSON><PERSON>UT<PERSON>, 3)
        COW(3206, 3290, 0, SOUTH, 3)
        COW_CALF_2794(3207, 3299, 0, SOUTH, 3)
        COW_2791(3208, 3294, 0, SOUTH, 4)
        COW_2793(3209, 3288, 0, SOUTH, 7)
        COW_2793(3209, 3300, 0, SOUTH, 7)
        SHEEP_2787(3210, 3274, 0, SOUTH, 3)
        COW(3211, 3291, 0, SOUTH, 3)
        DUCK(3211, 3322, 0, SOUTH, 18)
        IMP_5007(3214, 3281, 0, SOUTH, 100)
        DUCK_1839(3214, 3313, 0, SOUTH, 20)
        DUCK(3214, 3314, 0, SOUTH, 18)
        DUCK(3214, 3318, 0, SOUTH, 18)
        DUCK_1839(3214, 3320, 0, SOUTH, 20)
        GOBLIN_3030(3215, 3276, 0, SOUTH, 13)
        DUCK_1839(3215, 3303, 0, SOUTH, 20)
        DUCK(3216, 3314, 0, SOUTH, 18)
        GOBLIN_3029(3217, 3278, 0, SOUTH, 13)
        DUCK(3218, 3287, 0, SOUTH, 18)
        DUCK_1839(3219, 3285, 0, SOUTH, 20)
        DUCK_1839(3219, 3288, 0, SOUTH, 20)
        TOOL_LEPRECHAUN(3222, 3316, 0, SOUTH, 0)
        DUCK(3223, 3281, 0, SOUTH, 18)
        SETH_GROATS(3223, 3293, 0, SOUTH, 2)
        6219(3224, 3286, 0, SOUTH, 2)
        CHICKEN_1174(3225, 3300, 0, SOUTH, 4)
        DUCK_2003(3226, 3283, 0, SOUTH, 3)
        CHICKEN_1174(3226, 3296, 0, SOUTH, 4)
        VASQUEN(3226, 3311, 0, SOUTH, 4)
        FARMER(3227, 3290, 0, SOUTH, 5)
        DRAKE(3228, 3282, 0, SOUTH, 3)
        CHICKEN_1174(3228, 3297, 0, SOUTH, 4)
        CHICKEN_1174(3228, 3299, 0, SOUTH, 4)
        DUCK_2003(3229, 3280, 0, SOUTH, 3)
        CHICKEN(3229, 3296, 0, SOUTH, 2)
        4515(3230, 3285, 0, SOUTH, 5)
        CHICKEN(3230, 3298, 0, SOUTH, 2)
        CHICKEN(3230, 3299, 0, SOUTH, 2)
        DUCK(3231, 3271, 0, SOUTH, 18)
        CHICKEN_1174(3231, 3297, 0, SOUTH, 4)
        CHICKEN(3231, 3300, 0, SOUTH, 2)
        CHICKEN_1174(3232, 3299, 0, SOUTH, 4)
        DUCK(3233, 3267, 0, SOUTH, 18)
        DUCK(3233, 3270, 0, SOUTH, 18)
        CHICKEN_1174(3233, 3294, 0, SOUTH, 4)
        FARMER_3243(3233, 3308, 0, SOUTH, 5)
        DUCK_1839(3234, 3267, 0, SOUTH, 20)
        DUCK_1839(3234, 3268, 0, SOUTH, 20)
        CHICKEN(3234, 3297, 0, SOUTH, 2)
        DUCK(3235, 3265, 0, SOUTH, 18)
        CHICKEN(3235, 3298, 0, SOUTH, 2)
        CHICKEN(3237, 3323, 0, SOUTH, 2)
        IMP_5007(3240, 3307, 0, SOUTH, 100)
        COW_2793(3243, 3295, 0, SOUTH, 7)
        COW_2791(3244, 3283, 0, SOUTH, 4)
        COW_2791(3244, 3289, 0, SOUTH, 4)
        COW_2791(3246, 3293, 0, SOUTH, 4)
        COW_CALF_2794(3247, 3279, 0, SOUTH, 3)
        COW(3247, 3284, 0, SOUTH, 3)
        COW_CALF(3247, 3287, 0, SOUTH, 9)
        COW(3250, 3293, 0, SOUTH, 3)
        FARMER_3245(3250, 3310, 0, SOUTH, 2)
        1172(3252, 3275, 0, SOUTH, 0)
        COW_2793(3252, 3282, 0, SOUTH, 7)
        COW(3252, 3288, 0, SOUTH, 3)
        COW(3254, 3267, 0, SOUTH, 3)
        1172(3254, 3272, 0, SOUTH, 0)
        GILLIE_GROATS(3254, 3274, 0, SOUTH, 2)
        COW_CALF_2794(3254, 3291, 0, SOUTH, 3)
        COW_2791(3255, 3278, 0, SOUTH, 4)
        COW_CALF(3256, 3273, 0, SOUTH, 9)
        COW_2793(3256, 3292, 0, SOUTH, 7)
        COW_CALF_2794(3257, 3280, 0, SOUTH, 3)
        COW_2791(3257, 3286, 0, SOUTH, 4)
        COW(3258, 3265, 0, SOUTH, 3)
        CROW_2071(3258, 3315, 0, SOUTH, 6)
        CROW(3258, 3317, 0, SOUTH, 5)
        COW_2793(3259, 3295, 0, SOUTH, 7)
        CROW_2070(3260, 3317, 0, SOUTH, 4)
        COW_2793(3261, 3270, 0, SOUTH, 7)
        COW_CALF_2794(3261, 3273, 0, SOUTH, 3)
        COW(3261, 3282, 0, SOUTH, 3)
        COW_CALF(3261, 3289, 0, SOUTH, 9)
        COW_2791(3261, 3292, 0, SOUTH, 4)
        COW_2791(3262, 3277, 0, SOUTH, 4)
        COW_CALF_2794(3262, 3287, 0, SOUTH, 3)
        FARMER_3244(3262, 3325, 0, SOUTH, 3)
    }
}