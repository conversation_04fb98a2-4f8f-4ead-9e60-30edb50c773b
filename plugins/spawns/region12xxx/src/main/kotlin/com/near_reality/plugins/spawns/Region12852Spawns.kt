package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12852Spawns : NPCSpawnsScript() {
    init {
        TARQUIN(3203, 3344, 0, EAST, 0)
        IMP_5007(3205, 3355, 0, SOUTH, 100)
        GUARD_3010(3205, 3379, 0, SOUTH, 5)
        CHARLIE_THE_TRAMP(3208, 3391, 0, SOUTH, 2)
        6708(3210, 3385, 0, SOUTH, 10)
        GUARD_3011(3211, 3378, 0, SOUTH, 5)
        GUARD_3010(3211, 3381, 0, SOUTH, 5)
        6708(3212, 3388, 0, SOUTH, 10)
        DARK_WIZARD_5088(3223, 3367, 0, SOUTH, 3)
        DARK_WIZARD_5087(3223, 3372, 0, <PERSON>O<PERSON><PERSON>, 3)
        DARK_WIZARD_5089(3224, 3370, 0, SOUTH, 3)
        DARK_WIZARD_5086(3225, 3365, 0, SOUTH, 3)
        DARK_WIZARD_5086(3225, 3374, 0, SOUTH, 3)
        DARK_WIZARD_5089(3228, 3373, 0, SOUTH, 3)
        SHEEP_1299(3230, 3346, 0, SOUTH, 5)
        DARK_WIZARD_5087(3230, 3363, 0, SOUTH, 3)
        DARK_WIZARD_5088(3230, 3365, 0, SOUTH, 3)
        DARK_WIZARD_5088(3230, 3374, 0, SOUTH, 3)
        THIEF_5217(3230, 3391, 0, SOUTH, 3)
        DARK_WIZARD_5086(3232, 3367, 0, SOUTH, 3)
        DARK_WIZARD_5087(3232, 3372, 0, SOUTH, 3)
        SHEEP_2787(3233, 3343, 0, SOUTH, 3)
        RAM_1264(3234, 3345, 0, SOUTH, 4)
        SHEEP_1300(3234, 3349, 0, SOUTH, 3)
        SARAH_10416(3235, 3383, 0, SOUTH, 5)
        SHEEP_1299(3238, 3345, 0, SOUTH, 5)
        SHEEP_2693(3238, 3349, 0, SOUTH, 3)
        IMP_5007(3238, 3390, 0, SOUTH, 100)
        SHEEP_1178(3240, 3342, 0, SOUTH, 5)
        MASTER_FARMER_5731(3240, 3345, 0, SOUTH, 5)
        SHEEP_2786(3246, 3343, 0, SOUTH, 3)
        SHEEP_2787(3246, 3350, 0, SOUTH, 3)
        RAM_1262(3248, 3351, 0, SOUTH, 5)
        MUGGER(3249, 3391, 0, SOUTH, 2)
        SHEEP_1300(3253, 3355, 0, SOUTH, 3)
        GOBLIN_3073(3259, 3338, 0, SOUTH, 9)
        SWAN(3259, 3355, 0, SOUTH, 5)
        RAM_1263(3260, 3349, 0, SOUTH, 4)
        3229(3260, 3383, 0, SOUTH, 4)
        SWAN(3261, 3355, 0, SOUTH, 5)
        SHEEP_2693(3262, 3348, 0, SOUTH, 3)
        MAN_3107(3234, 3385, 1, SOUTH, 5)
        WEAPONSMASTER(3246, 3384, 1, SOUTH, 5)
    }
}