package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12853Spawns : NPCSpawnsScript() {
    init {
        STRAY_DOG(3200, 3399, 0, SOUTH, 10)
        SHOP_KEEPER_2884(3203, 3397, 0, SOUTH, 3)
        GYPSY_ARIS(3203, 3424, 0, SOUTH, 2)
        ZAFF(3203, 3433, 0, SOUTH, 3)
        534(3204, 3417, 0, SOUTH, 3)
        IFFIE(3204, 3419, 0, SOUTH, 0)
        SHOP_ASSISTANT_2885(3205, 3399, 0, SOUTH, 4)
        6708(3209, 3433, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        6708(3211, 3406, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        ROMEO(3211, 3425, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        6708(3213, 3449, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        SHOP_ASSISTANT_2816(3217, 3411, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        BARAEK(3217, 3435, 0, SOUTH, 2)
        WOMAN_3015(3218, 3395, 0, SOUTH, 3)
        SHOP_KEEPER_2815(3218, 3415, 0, SOUTH, 2)
        BENNY(3219, 3432, 0, SOUTH, 2)
        SHILOP(3221, 3434, 0, SOUTH, 5)
        WILOUGH(3221, 3435, 0, SOUTH, 2)
        DR_HARLOW(3222, 3397, 0, SOUTH, 4)
        JONNY_THE_BEARD(3223, 3395, 0, SOUTH, 3)
        BARBARIAN_3262(3225, 3402, 0, SOUTH, 11)
        TOBY(3225, 3415, 0, WEST, 0)
        BARTENDER_1312(3226, 3399, 0, SOUTH, 2)
        TRAMP(3228, 3412, 0, SOUTH, 3)
        HORVIK(3229, 3438, 0, SOUTH, 3)
        COOK_2895(3230, 3401, 0, NORTH_WEST, 0)
        LOWE(3232, 3423, 0, SOUTH, 3)
        TOOL_LEPRECHAUN(3232, 3455, 0, SOUTH, 0)
        STRAY_DOG_2922(3233, 3394, 0, SOUTH, 7)
        MAN_3106(3234, 3398, 0, SOUTH, 5)
        TRAMP_3255(3235, 3401, 0, SOUTH, 3)
        MAN_3106(3237, 3406, 0, SOUTH, 5)
        SPIDER_3019(3238, 3392, 0, SOUTH, 8)
        MAN_3108(3238, 3406, 0, SOUTH, 11)
        6708(3238, 3429, 0, SOUTH, 10)
        OLD_MAN_YARLO(3239, 3395, 0, SOUTH, 5)
        JEFF_10415(3242, 3452, 0, SOUTH, 5)
        RAT_2855(3243, 3393, 0, SOUTH, 3)
        6708(3243, 3402, 0, SOUTH, 10)
        6708(3246, 3448, 0, SOUTH, 10)
        CLEANER(3250, 3429, 0, SOUTH, 10)
        6522(3251, 3418, 0, NORTH, 0)
        6708(3251, 3421, 0, SOUTH, 10)
        BANKER_2897(3252, 3418, 0, NORTH, 0)
        2886(3253, 3402, 0, SOUTH, 2)
        BANKER_2897(3253, 3418, 0, NORTH, 0)
        TOWN_CRIER(3253, 3429, 0, SOUTH, 3)
        BARNABUS_HURMA(3253, 3445, 0, NORTH, 0)
        INFORMATION_CLERK(3253, 3454, 0, EAST, 0)
        BANKER_2898(3254, 3418, 0, NORTH, 0)
        MARIUS_GISTE(3254, 3444, 0, SOUTH, 0)
        BANKER_2898(3255, 3418, 0, NORTH, 0)
        BANKER_2898(3256, 3418, 0, NORTH, 0)
        CADEN_AZRO(3256, 3443, 0, NORTH, 0)
        THIAS_LEACKE(3257, 3442, 0, EAST, 0)
        CURATOR_HAIG_HALEN(3257, 3447, 0, SOUTH, 4)
        MUSEUM_GUARD(3260, 3447, 0, SOUTH, 0)
        TEACHER_AND_PUPIL_1922(3260, 3452, 0, SOUTH, 5)
        MAN_3107(3261, 3402, 0, SOUTH, 5)
        SCHOOLBOY_1920(3261, 3455, 0, SOUTH, 5)
        STRAY_DOG(3262, 3410, 0, SOUTH, 10)
        GUARD_1147(3263, 3407, 0, SOUTH, 0)
        MUSEUM_GUARD_1912(3263, 3441, 0, NORTH_EAST, 0)
        SCHOOLGIRL_1921(3263, 3452, 0, SOUTH, 5)
        MAN_3014(3231, 3395, 1, SOUTH, 5)
        MAN_3108(3231, 3399, 1, SOUTH, 11)
        TORRCS(3253, 3453, 1, SOUTH, 3)
        MARFET(3262, 3442, 1, SOUTH, 4)
        ART_CRITIC_JACQUES(3257, 3454, 2, SOUTH, 0)
        TEACHER_AND_PUPIL_1922(3261, 3451, 2, SOUTH, 5)
        BIGREDJAPAN(3232, 3401, 3, SOUTH, 5)
    }
}