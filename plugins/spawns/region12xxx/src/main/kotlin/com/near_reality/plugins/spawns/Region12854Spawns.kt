package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12854Spawns : NPCSpawnsScript() {
    init {
        WARRIOR_WOMAN(3202, 3487, 0, SOUTH, 5)
        SIR_PRYSIN(3204, 3473, 0, SOUTH, 3)
        GUARD_3010(3204, 3496, 0, SOUTH, 5)
        6708(3205, 3463, 0, SOUTH, 10)
        WARRIOR_WOMAN(3205, 3487, 0, SOUTH, 5)
        GUARD_3010(3206, 3462, 0, SOUTH, 5)
        6186(3208, 3496, 0, SOUTH, 2)
        6203(3209, 3495, 0, <PERSON>OUT<PERSON>, 2)
        GUARD_3010(3211, 3463, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GUARD_3010(3212, 3462, 0, <PERSON><PERSON>UT<PERSON>, 5)
        GUARD_3011(3213, 3465, 0, <PERSON>OUTH, 5)
        MONK_OF_ZAMORA<PERSON>_528(3213, 3476, 0, SOUTH, 5)
        IMP_5007(3213, 3502, 0, SOUTH, 100)
        GUARD_3010(3215, 3464, 0, SOUTH, 5)
        GUARD_3010(3217, 3461, 0, SOUTH, 5)
        8110(3221, 3476, 0, SOUTH, 2)
        6708(3222, 3464, 0, SOUTH, 10)
        KING_ROALD_5215(3222, 3472, 0, SOUTH, 2)
        AEONISIG_RAISPHER(3224, 3473, 0, SOUTH, 2)
        7503(3224, 3477, 0, SOUTH, 2)
        TREZNOR(3226, 3458, 0, SOUTH, 3)
        BLACK_KNIGHT(3226, 3515, 0, SOUTH, 5)
        QUEEN_ELLAMARIA_11025(3228, 3477, 0, SOUTH, 5)
        GRIZZLY_BEAR(3230, 3500, 0, SOUTH, 11)
        IMP_5007(3234, 3506, 0, SOUTH, 100)
        BLACK_KNIGHT(3234, 3518, 0, SOUTH, 5)
        BOB_10414(3237, 3484, 0, SOUTH, 5)
        ESTATE_AGENT(3240, 3476, 0, SOUTH, 2)
        GUARD_3010(3240, 3500, 0, SOUTH, 5)
        10429(3241, 3471, 0, SOUTH, 5)
        6708(3244, 3496, 0, SOUTH, 10)
        IMP_5007(3246, 3458, 0, SOUTH, 100)
        6708(3246, 3463, 0, SOUTH, 10)
        6708(3246, 3479, 0, SOUTH, 10)
        GUARD_3010(3246, 3501, 0, SOUTH, 5)
        JEREMY_CLERKSIN(3253, 3477, 0, SOUTH, 0)
        FATHER_LAWRENCE(3254, 3484, 0, SOUTH, 3)
        MARTINA_SCORSBY(3256, 3481, 0, SOUTH, 0)
        IMP_5007(3261, 3514, 0, SOUTH, 100)
        GUARD_3010(3201, 3495, 1, SOUTH, 5)
        GUARD_3010(3204, 3491, 1, SOUTH, 5)
        GUARD_3010(3205, 3494, 1, SOUTH, 5)
        GUARD_3010(3205, 3498, 1, SOUTH, 5)
        AMBASSADOR_FERRNOOK(3209, 3474, 1, SOUTH, 5)
        ELSIE(3255, 3488, 1, SOUTH, 0)
        GUARD_3010(3203, 3479, 2, SOUTH, 5)
        GUARD_3010(3204, 3477, 2, SOUTH, 5)
        CAPTAIN_ROVIN(3204, 3496, 2, SOUTH, 3)
        GUARD_3010(3206, 3478, 2, SOUTH, 5)
        GUARD_3010(3206, 3481, 2, SOUTH, 5)
        GUARD_3010(3207, 3476, 2, SOUTH, 5)
        GUARD_3010(3208, 3473, 2, SOUTH, 5)
        GUARD_3010(3209, 3471, 2, SOUTH, 5)
        TRAINEE_GUARD(3209, 3490, 2, SOUTH, 0)
        GUARD_3010(3210, 3474, 2, SOUTH, 5)
        GUARD_3010(3211, 3468, 2, SOUTH, 5)
        TRAINEE_GUARD(3211, 3490, 2, SOUTH, 0)
        TRAINEE_GUARD(3213, 3490, 2, SOUTH, 0)
        CAPTAIN(3213, 3491, 2, SOUTH, 3)
        TRAINEE_GUARD(3215, 3490, 2, SOUTH, 0)
        TRAINEE_GUARD(3217, 3490, 2, SOUTH, 0)
        GUARD_3010(3221, 3483, 2, SOUTH, 5)
        GUARD_3010(3222, 3470, 2, SOUTH, 5)
    }
}