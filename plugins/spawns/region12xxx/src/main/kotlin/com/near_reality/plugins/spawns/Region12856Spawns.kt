package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12856Spawns : NPCSpawnsScript() {
    init {
        ELDER_CHAOS_DRUID(3229, 3613, 0, SOUTH, 6)
        ELDER_CHAOS_DRUID(3231, 3607, 0, SOUTH, 6)
        ELDER_CHAOS_DRUID(3235, 3615, 0, SOUTH, 6)
        ELDER_CHAOS_DRUID(3235, 3620, 0, SOUTH, 6)
        ELDER_CHAOS_DRUID(3236, 3629, 0, SOUTH, 6)
        ELDER_CHAOS_DRUID(3237, 3600, 0, SOUTH, 6)
        SIMON(3237, 3621, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        ELDER_CHAOS_DRUID(3238, 3625, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ELDER_CHAOS_DRUID(3242, 3600, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        ELDER_CHAOS_DRUID(3242, 3619, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        <PERSON>LD<PERSON>_<PERSON><PERSON><PERSON>_DRUID(3245, 3616, 0, SOUTH, 6)
        ELDER_CHAOS_DR<PERSON>D(3247, 3602, 0, SOUTH, 6)
        ELDER_CHAOS_DRUID(3249, 3608, 0, SOUTH, 6)
        <PERSON>LDER_CHAOS_DRUID(3249, 3611, 0, SOUTH, 6)
    }
}