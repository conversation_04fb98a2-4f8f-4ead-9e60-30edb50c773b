package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12857Spawns : NPCSpawnsScript() {
    init {
        ENT(3201, 3684, 0, SOUTH, 11)
        ENT(3202, 3669, 0, SOUTH, 11)
        BLACK_UNICORN(3209, 3672, 0, SOUTH, 15)
        ENT(3211, 3690, 0, SOUTH, 11)
        BLACK_UNICORN(3213, 3673, 0, SOUTH, 15)
        BLACK_UNICORN(3214, 3670, 0, SOUTH, 15)
        BLACK_UNICORN(3215, 3677, 0, SOUTH, 15)
        BLACK_UNICORN(3216, 3683, 0, <PERSON><PERSON><PERSON><PERSON>, 15)
        BLACK_UNICORN(3218, 3675, 0, <PERSON>O<PERSON><PERSON>, 15)
        ENT(3218, 3697, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        BLACK_UNICORN(3220, 3681, 0, <PERSON><PERSON><PERSON><PERSON>, 15)
        BLACK_UNICORN(3222, 3685, 0, SOUTH, 15)
        ENT(3223, 3671, 0, SOUTH, 11)
        LARRY_2197(3228, 3687, 0, SOUTH, 44)
        ENT(3231, 3702, 0, SOUTH, 11)
        BLACK_UNICORN(3238, 3668, 0, SOUTH, 15)
        BLA<PERSON>K_UNICORN(3240, 3670, 0, SOUTH, 15)
        BLACK_UNICORN(3240, 3673, 0, SOUTH, 15)
        BLACK_UNICORN(3244, 3670, 0, SOUTH, 15)
        RAT_2854(3251, 3677, 0, SOUTH, 14)
        RAT_2854(3252, 3673, 0, SOUTH, 14)
        RAT_2854(3252, 3676, 0, SOUTH, 14)
        RAT_2854(3257, 3668, 0, SOUTH, 14)
        RAT_2854(3260, 3670, 0, SOUTH, 14)
        RAT_2854(3261, 3673, 0, SOUTH, 14)
    }
}