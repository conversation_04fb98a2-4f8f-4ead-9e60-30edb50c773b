package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12858Spawns : NPCSpawnsScript() {
    init {
        SKELETON_77(3211, 3741, 0, <PERSON>OUT<PERSON>, 7)
        SKELETON_78(3217, 3738, 0, SOUT<PERSON>, 8)
        SKELETON_79(3221, 3742, 0, SOUTH, 8)
        SKELETON_81(3227, 3746, 0, SOUTH, 6)
        SKELETON_80(3228, 3739, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKELETON_77(3240, 3741, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SKELETON_78(3246, 3745, 0, SOUTH, 8)
        SKELETON_79(3252, 3737, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SKELETON_79(3261, 3736, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
    }
}