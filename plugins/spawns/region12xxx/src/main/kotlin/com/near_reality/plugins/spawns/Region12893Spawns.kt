package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12893Spawns : NPCSpawnsScript() {
    init {
        BUTTERFLY(3200, 5954, 0, SOUTH, 7)
        BUTTERFLY_238(3201, 5986, 0, SOUTH, 0)
        BUTTERFLY_235(3202, 5955, 0, SOUTH, 7)
        BUTTERFLY(3202, 5958, 0, SOUTH, 7)
        WILL_O_THE_WISP(3202, 5990, 0, SOUTH, 0)
        BUTTERFLY(3204, 5955, 0, SOUTH, 7)
        BUTTERFLY(3204, 5959, 0, SOUTH, 7)
        WILL_O_THE_WISP_3441(3204, 5988, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        BUTTERFLY_235(3206, 5953, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        RABBIT_3422(3211, 5975, 0, <PERSON><PERSON>UT<PERSON>, 3)
        RABBIT_3420(3211, 5977, 0, SOUTH, 4)
        IORWERTH_ARCHER(3212, 5994, 0, SOUTH, 7)
        RABBIT_3420(3213, 5972, 0, SOUTH, 4)
        RABBIT_3420(3213, 5975, 0, SOUTH, 4)
        RABBIT_3422(3213, 5977, 0, SOUTH, 3)
        RABBIT_3421(3214, 5973, 0, SOUTH, 3)
        IORWERTH_WARRIOR(3217, 5996, 0, SOUTH, 7)
        IORWERTH_WARRIOR(3217, 6008, 0, SOUTH, 7)
        IORWERTH_ARCHER(3219, 6002, 0, SOUTH, 7)
        GRIZZLY_BEAR_CUB_3425(3221, 5970, 0, SOUTH, 10)
        IORWERTH_WARRIOR(3221, 6003, 0, SOUTH, 7)
        IORWERTH_WARRIOR(3222, 6013, 0, SOUTH, 7)
        GRIZZLY_BEAR_CUB(3223, 5974, 0, SOUTH, 8)
        GRIZZLY_BEAR_3423(3224, 5976, 0, SOUTH, 11)
        IORWERTH_WARRIOR(3224, 6001, 0, SOUTH, 7)
        GRIZZLY_BEAR_CUB(3225, 5970, 0, SOUTH, 8)
        IORWERTH_ARCHER(3226, 6011, 0, SOUTH, 7)
        IORWERTH_WARRIOR(3228, 6007, 0, SOUTH, 7)
        638(3229, 6004, 0, SOUTH, 0)
        BUTTERFLY(3231, 5967, 0, SOUTH, 7)
        BUTTERFLY_235(3231, 5976, 0, SOUTH, 7)
        WILL_O_THE_WISP(3232, 5977, 0, SOUTH, 0)
        WILL_O_THE_WISP_3441(3232, 5979, 0, SOUTH, 0)
        IORWERTH_ARCHER(3232, 6009, 0, SOUTH, 7)
        BUTTERFLY_235(3233, 5974, 0, SOUTH, 7)
        WILL_O_THE_WISP_3441(3234, 5967, 0, SOUTH, 0)
        WILL_O_THE_WISP(3234, 5969, 0, SOUTH, 0)
        WILL_O_THE_WISP_3441(3234, 5976, 0, SOUTH, 0)
        ROD_FISHING_SPOT_3417(3234, 5989, 0, SOUTH, 0)
        IORWERTH_ARCHER(3234, 5991, 0, SOUTH, 7)
        IORWERTH_ARCHER(3234, 6006, 0, SOUTH, 7)
        BUTTERFLY_235(3235, 5970, 0, SOUTH, 7)
        BUTTERFLY(3236, 5974, 0, SOUTH, 7)
        BUTTERFLY(3237, 5968, 0, SOUTH, 7)
        ROD_FISHING_SPOT_3417(3237, 5984, 0, SOUTH, 0)
        WILL_O_THE_WISP(3237, 5995, 0, SOUTH, 0)
        WILL_O_THE_WISP_3441(3238, 5996, 0, SOUTH, 0)
        WILL_O_THE_WISP_3441(3239, 5997, 0, SOUTH, 0)
        ROD_FISHING_SPOT_3417(3239, 6000, 0, SOUTH, 0)
        BUTTERFLY(3240, 5992, 0, SOUTH, 7)
        BUTTERFLY(3240, 5994, 0, SOUTH, 7)
        BUTTERFLY_235(3241, 5996, 0, SOUTH, 7)
        BUTTERFLY_235(3242, 5988, 0, SOUTH, 7)
        BUTTERFLY(3242, 5990, 0, SOUTH, 7)
        BUTTERFLY(3242, 5993, 0, SOUTH, 7)
        WILL_O_THE_WISP(3242, 5997, 0, SOUTH, 0)
        WILL_O_THE_WISP_3441(3243, 5996, 0, SOUTH, 0)
        BUTTERFLY_235(3244, 5992, 0, SOUTH, 7)
        BUTTERFLY(3244, 5994, 0, SOUTH, 7)
        WILL_O_THE_WISP(3244, 5995, 0, SOUTH, 0)
        WILL_O_THE_WISP(3245, 5995, 0, SOUTH, 0)
        BUTTERFLY(3246, 5989, 0, SOUTH, 7)
        ROD_FISHING_SPOT_3417(3249, 5995, 0, SOUTH, 0)
        RABBIT_3420(3252, 5989, 0, SOUTH, 4)
        RABBIT_3420(3253, 5991, 0, SOUTH, 4)
        BUTTERFLY_238(3254, 5972, 0, SOUTH, 0)
        RABBIT_3422(3254, 5989, 0, SOUTH, 3)
        RABBIT_3421(3254, 5990, 0, SOUTH, 3)
        BUTTERFLY_238(3255, 5986, 0, SOUTH, 0)
        RABBIT_3421(3255, 5989, 0, SOUTH, 3)
        RABBIT_3420(3255, 5990, 0, SOUTH, 4)
        BUTTERFLY_238(3256, 6008, 0, SOUTH, 0)
        BUTTERFLY_238(3257, 5973, 0, SOUTH, 0)
        BUTTERFLY_238(3257, 5975, 0, SOUTH, 0)
        RABBIT_3420(3258, 5989, 0, SOUTH, 4)
        BUTTERFLY_238(3260, 5974, 0, SOUTH, 0)
        BUTTERFLY_238(3261, 5977, 0, SOUTH, 0)
        BUTTERFLY_238(3262, 5957, 0, SOUTH, 0)
    }
}