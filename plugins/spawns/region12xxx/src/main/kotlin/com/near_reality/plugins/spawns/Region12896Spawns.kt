package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12896Spawns : NPCSpawnsScript() {
    init {
        COW_2791(3213, 6170, 0, SOUTH, 4)
        COW(3214, 6167, 0, SOUTH, 3)
        COW_2791(3215, 6172, 0, SOUTH, 4)
        COW_2793(3216, 6168, 0, SOUTH, 7)
        CROW_2074(3217, 6155, 0, SOUTH, 5)
        COW(3217, 6171, 0, SOUTH, 3)
        COW_2793(3218, 6173, 0, SOUTH, 7)
        CROW(3219, 6157, 0, SOUTH, 5)
        COW_2791(3219, 6168, 0, <PERSON>OUT<PERSON>, 4)
        CROW_2071(3221, 6153, 0, <PERSON>O<PERSON><PERSON>, 6)
        1172(3221, 6171, 0, <PERSON><PERSON>UTH, 0)
        GWERIN_HAPUS(3222, 6164, 0, <PERSON>OUT<PERSON>, 5)
        MARWOLAETH_DAWNSIO(3231, 6157, 0, SOUTH, 5)
        PRESWYLWYR_DALL(3234, 6165, 0, SOUTH, 5)
    }
}