package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12948Spawns : NPCSpawnsScript() {
    init {
        JUNA(3252, 9517, 1, SOUTH, 0)
        LIGHT_CREATURE_5783(3224, 9516, 2, SOUTH, 7)
        LIGHT_CREATURE_5783(3224, 9517, 2, SOUTH, 7)
        LIGHT_CREATURE_5783(3224, 9518, 2, <PERSON>OUT<PERSON>, 7)
        LIGHT_CREATURE_5783(3225, 9516, 2, SOUTH, 7)
        LIGHT_CREATURE_5783(3225, 9517, 2, SOUTH, 7)
        LIGHT_CREATURE_5783(3225, 9518, 2, <PERSON>OUT<PERSON>, 7)
        LIGHT_CREATURE_5783(3226, 9516, 2, <PERSON><PERSON><PERSON><PERSON>, 7)
        LIGHT_CREATURE_5783(3226, 9517, 2, <PERSON><PERSON><PERSON><PERSON>, 7)
        LIGHT_CREATURE_5783(3226, 9518, 2, <PERSON>O<PERSON><PERSON>, 7)
    }
}