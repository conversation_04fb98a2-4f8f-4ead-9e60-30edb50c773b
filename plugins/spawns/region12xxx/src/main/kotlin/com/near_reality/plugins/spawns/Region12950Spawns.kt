package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12950Spawns : NPCSpawnsScript() {
    init {
        SPIDER_3019(3209, 9618, 0, SOUTH, 8)
        4507(3210, 9623, 0, SOUTH, 4)
        SPIDER_3019(3211, 9621, 0, SOUTH, 8)
        SPIDER_3019(3216, 9616, 0, SOUTH, 8)
        SPIDER_3019(3216, 9624, 0, <PERSON>OUT<PERSON>, 8)
        5325(3231, 9610, 0, SOUTH, 2)
        1996(3245, 9647, 0, SOUTH, 2)
    }
}