package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12952Spawns : NPCSpawnsScript() {
    init {
        THIEF_5217(3237, 9769, 0, SOUTH, 3)
        THIEF_5220(3238, 9764, 0, SOUTH, 4)
        THIEF_5218(3245, 9769, 0, SOUTH, 2)
        THIEF_5220(3245, 9772, 0, SOUTH, 4)
        STRAVEN(3246, 9780, 0, SOUTH, 2)
        THIEF_5217(3247, 9776, 0, SOUTH, 3)
        THIEF_5219(3251, 9768, 0, SOUTH, 3)
    }
}