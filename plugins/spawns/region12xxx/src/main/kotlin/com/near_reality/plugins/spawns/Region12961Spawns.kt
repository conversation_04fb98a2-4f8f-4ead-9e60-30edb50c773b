package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12961Spawns : NPCSpawnsScript() {
    init {
        SCORPIAS_OFFSPRING_6616(3221, 10336, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3221, 10340, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3221, 10344, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3221, 10349, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3223, 10333, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3223, 10347, 0, SO<PERSON><PERSON>, 2)
        SCORPIAS_OFFSPRING_6616(3224, 10336, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        SCOR<PERSON>AS_OFFSPRING_6616(3225, 10350, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        SCORPIAS_OFFSPRING_6616(3229, 10333, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        SCORPIAS_OFFSPRING_6616(3229, 10350, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3231, 10344, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3233, 10333, 0, SOUTH, 2)
        SCORPIA(3233, 10341, 0, SOUTH, 3)
        SCORPIAS_OFFSPRING_6616(3233, 10350, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3234, 10339, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3237, 10333, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3237, 10350, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3238, 10344, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3241, 10333, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3241, 10350, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3242, 10336, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3242, 10347, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3244, 10350, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3245, 10333, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3245, 10337, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3245, 10340, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3245, 10344, 0, SOUTH, 2)
        SCORPIAS_OFFSPRING_6616(3245, 10347, 0, SOUTH, 2)
    }
}