package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12993Spawns : NPCSpawnsScript() {
    init {
        KURASK_410(3208, 12365, 0, SOUTH, 7)
        KURASK_410(3208, 12371, 0, SOUTH, 7)
        KURASK_410(3214, 12370, 0, SOUTH, 7)
        KURASK_410(3217, 12361, 0, SOUTH, 7)
        KURASK_410(3219, 12368, 0, SOUTH, 7)
        DARK_BEAST(3219, 12391, 0, SOUTH, 16)
        KURASK_410(3220, 12357, 0, SOUTH, 7)
        DARK_BEAST(3224, 12385, 0, <PERSON><PERSON><PERSON><PERSON>, 16)
        KURASK_410(3225, 12367, 0, <PERSON>OUT<PERSON>, 7)
        DARK_BEAST(3226, 12393, 0, <PERSON><PERSON><PERSON><PERSON>, 16)
        KURASK_410(3228, 12361, 0, <PERSON>OUT<PERSON>, 7)
        DARK_BEAST(3228, 12399, 0, SOUTH, 16)
        DARK_BEAST(3232, 12388, 0, SOUTH, 16)
        DARK_BEAST(3234, 12394, 0, SOUTH, 16)
        KURASK_410(3249, 12377, 0, SOUTH, 7)
        KURASK_410(3249, 12386, 0, SOUTH, 7)
        MUTATED_BLOODVELD(3249, 12414, 0, SOUTH, 2)
        KURASK_410(3254, 12382, 0, SOUTH, 7)
        KURASK_410(3255, 12372, 0, SOUTH, 7)
        KURASK_410(3255, 12389, 0, SOUTH, 7)
        KURASK_410(3258, 12380, 0, SOUTH, 7)
    }
}