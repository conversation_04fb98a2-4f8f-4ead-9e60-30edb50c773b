package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region12995Spawns : NPCSpawnsScript() {
    init {
        ELDALOTE(3214, 12534, 0, SOUTH, 5)
        GWINDOR(3215, 12520, 0, SOUTH, 5)
        MAEDHROS(3225, 12529, 0, SOUTH, 5)
        LILIFANG(3232, 12534, 0, SOUTH, 5)
        NIMRODEL(3242, 12520, 0, SOUTH, 5)
        FINARFIN(3246, 12536, 0, SOUTH, 5)
    }
}