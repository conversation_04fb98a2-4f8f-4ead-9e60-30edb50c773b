package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14129Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_4477(3527, 3169, 0, SOUTH, 0)
        FISHING_SPOT_4477(3529, 3165, 0, SOUTH, 0)
        FISHING_SPOT_4477(3530, 3165, 0, SOUTH, 0)
        FISHING_SPOT_4477(3531, 3169, 0, SOUTH, 0)
        FISHING_SPOT_4477(3534, 3175, 0, SOUTH, 0)
        FISHING_SPOT_4477(3536, 3177, 0, SOUTH, 0)
        FISHING_SPOT_4477(3538, 3179, 0, <PERSON>OUT<PERSON>, 0)
        FISHING_SPOT_4477(3540, 3178, 0, <PERSON>OUT<PERSON>, 0)
        FISHING_SPOT_4477(3543, 3180, 0, SOUTH, 0)
        9517(3529, 3168, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        3489(3530, 3169, 0, SOUTH, 2)
    }
}