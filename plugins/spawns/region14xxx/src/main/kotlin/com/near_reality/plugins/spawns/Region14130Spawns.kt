package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14130Spawns : NPCSpawnsScript() {
    init {
        VAMPYRE_JUVINATE_4430(3540, 3206, 0, SOUTH, 6)
        VAMPYRE_JUVINATE_4429(3546, 3206, 0, SOUTH, 4)
        VAMPYRE_JUVINATE_4427(3546, 3218, 0, SOUTH, 13)
        VAMPYRE_JUVENILE_4437(3546, 3249, 0, SOUTH, 4)
        VAMPYRE_JUVENILE_4436(3550, 3200, 0, SOUTH, 2)
        VAMPYRE_JUVINATE_4428(3550, 3213, 0, <PERSON>OUT<PERSON>, 8)
        VAMPYRE_JUVENILE_4437(3556, 3216, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        <PERSON><PERSON>YRE_JUVENILE_4437(3559, 3200, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        VAMPYRE_JUVENILE_4436(3561, 3246, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        VA<PERSON>YRE_JUVENILE_4436(3562, 3229, 0, SOUTH, 2)
        VAMPYRE_JUVENILE_4438(3564, 3201, 0, SOUTH, 3)
        VAMPYRE_JUVENILE_4436(3564, 3206, 0, SOUTH, 2)
        VAMPYRE_JUVENILE_4436(3564, 3218, 0, SOUTH, 2)
        VAMPYRE_JUVINATE_4430(3564, 3232, 0, SOUTH, 6)
        VAMPYRE_JUVENILE_4436(3564, 3253, 0, SOUTH, 2)
        VAMPYRE_JUVENILE_4438(3565, 3241, 0, SOUTH, 3)
        VAMPYRE_JUVINATE_4428(3567, 3214, 0, SOUTH, 8)
        VAMPYRE_JUVENILE_4436(3567, 3236, 0, SOUTH, 2)
        VAMPYRE_JUVINATE_4427(3572, 3216, 0, SOUTH, 13)
        VAMPYRE_JUVINATE_4429(3575, 3240, 0, SOUTH, 4)
        VAMPYRE_JUVINATE_4429(3577, 3216, 0, SOUTH, 4)
        VAMPYRE_JUVINATE_4430(3578, 3221, 0, SOUTH, 6)
        VAMPYRE_JUVINATE_4427(3578, 3231, 0, SOUTH, 13)
        VAMPYRE_JUVINATE_4428(3579, 3207, 0, SOUTH, 8)
        VAMPYRE_JUVINATE_4427(3579, 3250, 0, SOUTH, 13)
        VAMPYRE_JUVINATE_4430(3581, 3210, 0, SOUTH, 6)
        VAMPYRE_JUVENILE_4438(3582, 3216, 0, SOUTH, 3)
        VAMPYRE_JUVINATE_4428(3582, 3242, 0, SOUTH, 8)
        VAMPYRE_JUVENILE_4438(3583, 3200, 0, SOUTH, 3)
    }
}