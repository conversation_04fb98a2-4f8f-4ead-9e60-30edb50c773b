package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14133Spawns : NPCSpawnsScript() {
    init {
        BRUISE_BLAMISH_SNAIL(3520, 3407, 0, SOUTH, 22)
        GHAST(3523, 3436, 0, SOUTH, 4)
        MYRE_BLAMISH_SNAIL(3524, 3413, 0, SOUTH, 18)
        GHAST(3526, 3398, 0, SOUTH, 4)
        BARK_BLAMISH_SNAIL(3530, 3398, 0, SOUTH, 15)
        MYRE_BLAMISH_SNAIL(3530, 3441, 0, SOUTH, 18)
        GHAST(3531, 3425, 0, SOUTH, 4)
        BLOOD_BLAMISH_SNAIL(3531, 3438, 0, <PERSON><PERSON><PERSON><PERSON>, 30)
        SWAMP_LIZARD(3532, 3446, 0, <PERSON>OUT<PERSON>, 5)
        BLOOD_BLAMISH_SNAIL_2650(3534, 3428, 0, SOUTH, 19)
        SWAMP_LIZARD(3537, 3445, 0, SOUTH, 5)
        SWAMP_LIZARD(3537, 3451, 0, SOUTH, 5)
        OCHRE_BLAMISH_SNAIL(3540, 3430, 0, SOUTH, 14)
        MYRE_BLAMISH_SNAIL(3541, 3410, 0, SOUTH, 18)
        MYRE_BLAMISH_SNAIL(3544, 3430, 0, SOUTH, 18)
        OCHRE_BLAMISH_SNAIL_2651(3545, 3440, 0, SOUTH, 15)
        SWAMP_LIZARD(3547, 3442, 0, SOUTH, 5)
        GHAST(3548, 3450, 0, SOUTH, 4)
        SWAMP_LIZARD(3549, 3437, 0, SOUTH, 5)
        SWAMP_LIZARD(3549, 3449, 0, SOUTH, 5)
        SWAMP_LIZARD(3552, 3450, 0, SOUTH, 5)
        SWAMP_LIZARD(3554, 3440, 0, SOUTH, 5)
        SWAMP_LIZARD(3554, 3453, 0, SOUTH, 5)
        SWAMP_LIZARD(3556, 3438, 0, SOUTH, 5)
        SWAMP_LIZARD(3558, 3444, 0, SOUTH, 5)
        BLOOD_BLAMISH_SNAIL(3558, 3453, 0, SOUTH, 30)
        SWAMP_LIZARD(3562, 3435, 0, SOUTH, 5)
        MYRE_BLAMISH_SNAIL(3563, 3438, 0, SOUTH, 18)
        GHAST(3563, 3444, 0, SOUTH, 4)
    }
}