package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14134Spawns : NPCSpawnsScript() {
    init {
        LEECH(3537, 3496, 0, SOUTH, 8)
        WEREWOLF_5928(3544, 3462, 0, NORTH_WEST, 0)
        LEECH(3545, 3502, 0, SOUTH, 8)
        LEECH(3546, 3462, 0, SOUTH, 8)
        LEECH(3548, 3480, 0, SOUTH, 8)
        1035(3548, 3516, 0, SOUTH, 0)
        9516(3549, 3517, 0, SOUTH, 5)
        LEECH(3551, 3489, 0, SOUTH, 8)
        LEECH(3554, 3506, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        TREE(3556, 3470, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        LEECH(3560, 3462, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        BAT(3565, 3503, 0, <PERSON><PERSON><PERSON><PERSON>, 23)
        SPIDER_10443(3566, 3457, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        LEECH(3566, 3483, 0, SOUTH, 8)
        LEECH(3567, 3474, 0, SOUTH, 8)
        BAT(3567, 3481, 0, SOUTH, 23)
        SPIDER_10443(3567, 3503, 0, SOUTH, 5)
        TREE(3570, 3507, 0, SOUTH, 0)
        LEECH(3572, 3492, 0, SOUTH, 8)
        LEECH(3574, 3507, 0, SOUTH, 8)
        TREE(3577, 3493, 0, SOUTH, 0)
        TREE(3579, 3477, 0, SOUTH, 0)
        BAT(3580, 3491, 0, SOUTH, 23)
    }
}