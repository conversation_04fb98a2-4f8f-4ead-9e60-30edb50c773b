package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14172Spawns : NPCSpawnsScript() {
    init {
        DARK_WARRIOR(3556, 5948, 0, SOUTH, 5)
        DARK_WARRIOR(3558, 5941, 0, SOUTH, 5)
        DARK_WARRIOR(3562, 5935, 0, SOUTH, 5)
        DARK_WARRIOR(3567, 5930, 0, SOUTH, 5)
        DARK_WARRIOR(3573, 5926, 0, SOUTH, 5)
        DARK_WARRIOR(3580, 5924, 0, SOUTH, 5)
    }
}