package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14173Spawns : NPCSpawnsScript() {
    init {
        DARK_WARRIOR(3556, 5955, 0, SOUTH, 5)
        DARK_WARRIOR(3558, 5962, 0, SOUTH, 5)
        DARK_WARRIOR(3562, 5968, 0, SOUTH, 5)
        DARK_WARRIOR(3567, 5973, 0, SOUT<PERSON>, 5)
        DARK_WARRIOR(3573, 5977, 0, SOUTH, 5)
        DARK_WARRIOR(3580, 5979, 0, SOUTH, 5)
    }
}