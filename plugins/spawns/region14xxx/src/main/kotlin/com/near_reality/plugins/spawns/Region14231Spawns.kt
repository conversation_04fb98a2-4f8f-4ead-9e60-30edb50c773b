package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14231Spawns : NPCSpawnsScript() {
    init {
        CRYPT_RAT(3531, 9694, 0, SOUTH, 5)
        GIANT_CRYPT_RAT_1681(3532, 9696, 0, SOUTH, 5)
        GIANT_CRYPT_RAT(3533, 9692, 0, SOUTH, 5)
        CRYPT_RAT(3534, 9690, 0, SOUTH, 5)
        CRYPT_RAT(3534, 9698, 0, SOUTH, 5)
        CRYPT_RAT(3535, 9695, 0, SOUTH, 5)
        GIANT_CRYPT_RAT(3536, 9697, 0, SOUTH, 5)
        CRYPT_RAT(3537, 9692, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GIANT_CRYPT_RAT_1682(3537, 9694, 0, <PERSON>OUTH, 5)
        CRYPT_RAT(3539, 9695, 0, SOUTH, 5)
        BLOODWORM(3548, 9675, 0, SOUTH, 5)
        SKELETON_1686(3548, 9709, 0, SOUTH, 5)
        BLOODWORM(3549, 9679, 0, SOUTH, 5)
        SKELETON_1685(3549, 9713, 0, SOUTH, 5)
        BLOODWORM(3551, 9676, 0, SOUTH, 5)
        SKELETON_1687(3551, 9714, 0, SOUTH, 5)
        BLOODWORM(3552, 9679, 0, SOUTH, 5)
        BLOODWORM(3552, 9682, 0, SOUTH, 5)
        SKELETON_1685(3553, 9707, 0, SOUTH, 5)
        SKELETON_1688(3554, 9713, 0, SOUTH, 5)
        BLOODWORM(3555, 9675, 0, SOUTH, 5)
        BLOODWORM(3555, 9679, 0, SOUTH, 5)
        SKELETON_1687(3555, 9709, 0, SOUTH, 5)
        CRYPT_SPIDER(3566, 9693, 0, SOUTH, 5)
        GIANT_CRYPT_SPIDER(3566, 9695, 0, SOUTH, 5)
        GIANT_CRYPT_SPIDER(3567, 9691, 0, SOUTH, 5)
        GIANT_CRYPT_SPIDER(3567, 9697, 0, SOUTH, 5)
        CRYPT_SPIDER(3567, 9698, 0, SOUTH, 5)
        CRYPT_SPIDER(3569, 9695, 0, SOUTH, 5)
        CRYPT_SPIDER(3570, 9691, 0, SOUTH, 5)
        GIANT_CRYPT_SPIDER(3570, 9697, 0, SOUTH, 5)
        GIANT_CRYPT_SPIDER(3571, 9692, 0, SOUTH, 5)
        GIANT_CRYPT_SPIDER(3572, 9694, 0, SOUTH, 5)
        CRYPT_SPIDER(3572, 9697, 0, SOUTH, 5)
    }
}