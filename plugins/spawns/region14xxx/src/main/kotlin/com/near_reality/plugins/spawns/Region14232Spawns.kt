package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14232Spawns : NPCSpawnsScript() {
    init {
        BLOODVELD(3564, 9741, 0, SOUTH, 6)
        BLOODVELD(3566, 9735, 0, SOUTH, 6)
        BLOODVELD(3572, 9741, 0, SOUTH, 6)
        BLOODVELD(3574, 9737, 0, SOUTH, 6)
    }
}