package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14234Spawns : NPCSpawnsScript() {
    init {
        AGILITY_TRAINER(3527, 9909, 0, SOUTH, 5)
        AGILITY_TRAINER_5927(3528, 9865, 0, SOUTH, 5)
        AGILITY_TRAINER(3533, 9912, 0, SOUTH, 5)
        AGILITY_BOSS(3540, 9873, 0, SOUTH, 5)
        AGILITY_TRAINER(3540, 9892, 0, SOUTH, 5)
        AGILITY_TRAINER(3540, 9902, 0, SOUTH, 5)
        SKULLBALL_BOSS(3549, 9867, 0, <PERSON>OUT<PERSON>, 5)
        SKULLBALL_TRAINER(3554, 9886, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKULLBALL_TRAINER(3560, 9908, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKULLBALL_TRAINER(3565, 9865, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKULL<PERSON>LL_TRAINER(3572, 9908, 0, SOUTH, 5)
        SKULLBALL_TRAINER(3573, 9891, 0, SOUTH, 5)
        SKULLBALL_TRAINER(3577, 9875, 0, SOUTH, 5)
    }
}