package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14235Spawns : NPCSpawnsScript() {
    init {
        EXPERIMENT(3525, 9932, 0, SOUTH, 3)
        EXPERIMENT(3529, 9968, 0, SOUTH, 3)
        EXPERIMENT_1275(3534, 9926, 0, SOUTH, 4)
        EXPERIMENT_1274(3539, 9958, 0, SOUTH, 3)
        EXPERIMENT_1274(3547, 9933, 0, SOUTH, 3)
        EXPERIMENT_1275(3549, 9957, 0, SOUTH, 4)
        EXPERIMENT_1275(3552, 9940, 0, SOUTH, 4)
        EXPERIMENT(3554, 9928, 0, <PERSON>O<PERSON>H, 3)
        EXPERIMENT(3554, 9948, 0, <PERSON>OUTH, 3)
        EXPERIMENT_1275(3565, 9951, 0, SOUTH, 4)
        EXPERIMENT_1274(3565, 9960, 0, SOUTH, 3)
    }
}