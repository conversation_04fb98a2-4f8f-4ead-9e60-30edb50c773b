package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14390Spawns : NPCSpawnsScript() {
    init {
        FERAL_VAMPYRE_3237(3585, 3478, 0, SOUTH, 18)
        FERAL_VAMPYRE_3237(3586, 3480, 0, SOUTH, 18)
        TREE(3586, 3482, 0, SOUTH, 0)
        FERAL_VAMPYRE_3237(3586, 3496, 0, SOUTH, 18)
        LEECH(3588, 3461, 0, SOUTH, 8)
        BAT(3588, 3472, 0, SOUTH, 23)
        LEECH(3589, 3493, 0, SO<PERSON><PERSON>, 8)
        LEECH(3590, 3509, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        FERAL_VAMPYRE_3237(3592, 3477, 0, <PERSON><PERSON><PERSON><PERSON>, 18)
        FERAL_VAMPYRE_3237(3593, 3458, 0, <PERSON><PERSON><PERSON><PERSON>, 18)
        FERAL_VAMPYRE_3237(3593, 3508, 0, SOUTH, 18)
        FERAL_VAMPYRE_3237(3595, 3491, 0, SOUTH, 18)
        LEECH(3596, 3476, 0, SOUTH, 8)
        TREE(3596, 3477, 0, SOUTH, 0)
        FERAL_VAMPYRE_3237(3597, 3479, 0, SOUTH, 18)
        SPIDER_10443(3597, 3489, 0, SOUTH, 5)
        BAT(3598, 3509, 0, SOUTH, 23)
        LEECH(3599, 3483, 0, SOUTH, 8)
        BAT(3599, 3486, 0, SOUTH, 23)
        TREE(3600, 3506, 0, SOUTH, 0)
        LEECH(3607, 3460, 0, SOUTH, 8)
        BAT(3607, 3462, 0, SOUTH, 23)
        FERAL_VAMPYRE_3237(3607, 3466, 0, SOUTH, 18)
        TREE(3607, 3497, 0, SOUTH, 0)
        TREE(3610, 3465, 0, SOUTH, 0)
        SPIDER_10443(3611, 3462, 0, SOUTH, 5)
        BAT(3611, 3499, 0, SOUTH, 23)
        BAT(3613, 3482, 0, SOUTH, 23)
        TREE(3613, 3491, 0, SOUTH, 0)
        LEECH(3613, 3499, 0, SOUTH, 8)
        LEECH(3614, 3515, 0, SOUTH, 8)
        FERAL_VAMPYRE_3237(3617, 3477, 0, SOUTH, 18)
        TREE(3617, 3514, 0, SOUTH, 0)
        FERAL_VAMPYRE_3237(3618, 3514, 0, SOUTH, 18)
        TREE(3619, 3502, 0, SOUTH, 0)
        LEECH(3620, 3472, 0, SOUTH, 8)
        SPIDER_10443(3620, 3500, 0, SOUTH, 5)
        LEECH(3623, 3504, 0, SOUTH, 8)
        LEECH(3626, 3475, 0, SOUTH, 8)
        LEECH(3626, 3491, 0, SOUTH, 8)
        BAT(3627, 3511, 0, SOUTH, 23)
        BAT(3628, 3473, 0, SOUTH, 23)
        TREE(3628, 3476, 0, SOUTH, 0)
        TREE(3629, 3509, 0, SOUTH, 0)
        LEECH(3629, 3511, 0, SOUTH, 8)
        TREE(3630, 3489, 0, SOUTH, 0)
        FERAL_VAMPYRE_3237(3631, 3471, 0, SOUTH, 18)
        FERAL_VAMPYRE_3237(3631, 3499, 0, SOUTH, 18)
        FERAL_VAMPYRE_3237(3632, 3459, 0, SOUTH, 18)
        FERAL_VAMPYRE_3237(3632, 3475, 0, SOUTH, 18)
        LEECH(3633, 3463, 0, SOUTH, 8)
        BAT(3636, 3466, 0, SOUTH, 23)
        SPIDER_10443(3636, 3484, 0, SOUTH, 5)
        FERAL_VAMPYRE_3237(3638, 3472, 0, SOUTH, 18)
        BAT(3639, 3510, 0, SOUTH, 23)
        LEECH(3644, 3498, 0, SOUTH, 8)
        FERAL_VAMPYRE_3237(3646, 3467, 0, SOUTH, 18)
    }
}