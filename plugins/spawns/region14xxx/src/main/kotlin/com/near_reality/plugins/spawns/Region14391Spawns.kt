package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14391Spawns : NPCSpawnsScript() {
    init {
        TOOL_LEPRECHAUN(3596, 3524, 0, SOUTH, 0)
        LYRA(3601, 3527, 0, SOUTH, 5)
        UNDEAD_COW(3611, 3527, 0, SOUTH, 4)
        UNDEAD_COW(3616, 3525, 0, SOUTH, 4)
        4415(3617, 3528, 0, SOUTH, 4)
        UNDEAD_COW(3620, 3524, 0, <PERSON>OUTH, 4)
        UNDEAD_COW(3622, 3527, 0, SOUTH, 4)
        ALICE(3627, 3526, 0, SOUTH, 2)
        UNDEAD_CHICKEN(3627, 3531, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        UNDEAD_CHICKEN(3629, 3530, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        UNDEAD_CHICKEN(3631, 3529, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        UNDEAD_CHICKEN(3631, 3531, 0, <PERSON><PERSON><PERSON>H, 4)
        UN<PERSON>AD_CHICKEN(3632, 3526, 0, SOUTH, 4)
        UNDEAD_CHICKEN(3633, 3525, 0, SOUTH, 4)
        OLD_MAN(3617, 3544, 1, SOUTH, 5)
    }
}