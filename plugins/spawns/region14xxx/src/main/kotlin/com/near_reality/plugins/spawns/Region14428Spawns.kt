package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14428Spawns : NPCSpawnsScript() {
    init {
        DARK_WARRIOR(3587, 5924, 0, SOUTH, 5)
        BLACK_UNICORN(3588, 5924, 0, SOUTH, 15)
        BLACK_UNICORN(3592, 5924, 0, SOUTH, 15)
        DARK_WARRIOR(3594, 5926, 0, SOUTH, 5)
        DARK_WARRIOR(3600, 5930, 0, SOUTH, 5)
        DARK_WARRIOR(3605, 5935, 0, SOUTH, 5)
        DARK_WARRIOR(3609, 5941, 0, SOUTH, 5)
        DARK_WARRIOR(3611, 5948, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
    }
}