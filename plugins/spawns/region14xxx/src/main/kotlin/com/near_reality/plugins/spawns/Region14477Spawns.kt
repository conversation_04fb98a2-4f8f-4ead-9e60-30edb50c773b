package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14477Spawns : NPCSpawnsScript() {
    init {
        8014(3594, 9067, 0, SOUTH, 5)
        8014(3594, 9081, 0, SOUTH, 5)
        8014(3595, 9073, 0, SOUTH, 5)
        8014(3598, 9073, 0, SOUTH, 5)
        8014(3599, 9063, 0, SOUTH, 5)
        8014(3603, 9068, 0, SOUTH, 5)
        8014(3604, 9079, 0, SOUTH, 5)
        8014(3607, 9063, 0, SOUTH, 5)
        8014(3609, 9072, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8014(3612, 9065, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8014(3613, 9077, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7857(3618, 9086, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8014(3623, 9040, 0, SOUT<PERSON>, 5)
        8014(3623, 9048, 0, S<PERSON><PERSON><PERSON>, 5)
        8014(3625, 9035, 0, SOUTH, 5)
        7857(3625, 9062, 0, SOUTH, 5)
        7857(3625, 9069, 0, SOUTH, 5)
        7857(3626, 9085, 0, SOUTH, 5)
        8014(3627, 9053, 0, SOUTH, 5)
        8014(3628, 9044, 0, SOUTH, 5)
        7857(3629, 9074, 0, SOUTH, 5)
        8014(3632, 9038, 0, SOUTH, 5)
        8014(3633, 9049, 0, SOUTH, 5)
        8014(3633, 9052, 0, SOUTH, 5)
        7857(3636, 9067, 0, SOUTH, 5)
        8014(3637, 9034, 0, SOUTH, 5)
        8014(3638, 9079, 0, SOUTH, 5)
        8014(3639, 9043, 0, SOUTH, 5)
        7857(3640, 9056, 0, SOUTH, 5)
        8014(3641, 9053, 0, SOUTH, 5)
        8014(3642, 9038, 0, SOUTH, 5)
        7857(3644, 9065, 0, SOUTH, 5)
        7857(3646, 9056, 0, SOUTH, 5)
        8014(3647, 9078, 0, SOUTH, 5)
        8014(3586, 9069, 1, SOUTH, 5)
        8014(3593, 9072, 1, SOUTH, 5)
        8014(3593, 9080, 1, SOUTH, 5)
        8014(3594, 9063, 1, SOUTH, 5)
        8014(3601, 9066, 1, SOUTH, 5)
        8014(3603, 9065, 1, SOUTH, 5)
        8014(3603, 9080, 1, SOUTH, 5)
        8014(3604, 9069, 1, SOUTH, 5)
        8014(3604, 9076, 1, SOUTH, 5)
        8014(3614, 9065, 1, SOUTH, 5)
        8014(3614, 9073, 1, SOUTH, 5)
        8014(3614, 9079, 1, SOUTH, 5)
        8014(3621, 9076, 1, SOUTH, 5)
        8014(3623, 9053, 1, SOUTH, 5)
        8014(3625, 9033, 1, SOUTH, 5)
        8014(3625, 9044, 1, SOUTH, 5)
        8014(3626, 9046, 1, SOUTH, 5)
        8014(3629, 9043, 1, SOUTH, 5)
        8014(3629, 9061, 1, SOUTH, 5)
        8014(3632, 9054, 1, SOUTH, 5)
        8014(3633, 9033, 1, SOUTH, 5)
        8014(3636, 9026, 1, SOUTH, 5)
        8014(3636, 9043, 1, SOUTH, 5)
        8014(3639, 9033, 1, SOUTH, 5)
        8014(3640, 9044, 1, SOUTH, 5)
        8014(3640, 9054, 1, SOUTH, 5)
        8014(3587, 9071, 2, SOUTH, 5)
        8014(3589, 9075, 2, SOUTH, 5)
        8014(3590, 9070, 2, SOUTH, 5)
        8014(3597, 9072, 2, SOUTH, 5)
        8014(3609, 9072, 2, SOUTH, 5)
        8014(3616, 9075, 2, SOUTH, 5)
        8014(3617, 9070, 2, SOUTH, 5)
        8014(3620, 9072, 2, SOUTH, 5)
        8014(3620, 9075, 2, SOUTH, 5)
        8014(3630, 9030, 2, SOUTH, 5)
        8014(3630, 9057, 2, SOUTH, 5)
        8014(3631, 9060, 2, SOUTH, 5)
        8014(3632, 9027, 2, SOUTH, 5)
        8014(3632, 9038, 2, SOUTH, 5)
        8014(3632, 9050, 2, SOUTH, 5)
        8014(3635, 9027, 2, SOUTH, 5)
        8014(3635, 9031, 2, SOUTH, 5)
        8014(3635, 9058, 2, SOUTH, 5)
    }
}