package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14478Spawns : NPCSpawnsScript() {
    init {
        8014(3594, 9098, 0, SOUTH, 5)
        8014(3595, 9110, 0, SOUTH, 5)
        8014(3598, 9093, 0, SOUTH, 5)
        8014(3598, 9103, 0, SOUTH, 5)
        8014(3600, 9112, 0, SOUTH, 5)
        8017(3601, 9126, 0, SOUTH, 5)
        8015(3601, 9132, 0, SOUTH, 5)
        8014(3603, 9096, 0, SOUTH, 5)
        8014(3604, 9107, 0, SOUTH, 5)
        8018(3607, 9127, 0, <PERSON>O<PERSON><PERSON>, 5)
        8016(3607, 9133, 0, <PERSON>OUT<PERSON>, 5)
        8014(3608, 9112, 0, <PERSON>OUT<PERSON>, 5)
        8014(3609, 9102, 0, SOUTH, 5)
        8014(3612, 9102, 0, SOUTH, 5)
        8014(3613, 9094, 0, SOUTH, 5)
        8014(3613, 9108, 0, SOUTH, 5)
        7857(3616, 9089, 0, SOUTH, 5)
        7857(3616, 9095, 0, SOUTH, 5)
        7857(3622, 9110, 0, SOUTH, 5)
        8014(3623, 9128, 0, SOUTH, 5)
        8014(3623, 9136, 0, SOUTH, 5)
        7857(3625, 9091, 0, SOUTH, 5)
        8014(3625, 9123, 0, SOUTH, 5)
        7857(3627, 9099, 0, SOUTH, 5)
        8014(3627, 9141, 0, SOUTH, 5)
        8014(3628, 9132, 0, SOUTH, 5)
        7857(3629, 9110, 0, SOUTH, 5)
        8014(3632, 9126, 0, SOUTH, 5)
        8014(3633, 9137, 0, SOUTH, 5)
        8014(3633, 9140, 0, SOUTH, 5)
        7857(3634, 9106, 0, SOUTH, 5)
        8014(3637, 9122, 0, SOUTH, 5)
        8014(3638, 9088, 0, SOUTH, 5)
        8014(3639, 9097, 0, SOUTH, 5)
        8014(3639, 9131, 0, SOUTH, 5)
        8014(3641, 9141, 0, SOUTH, 5)
        7857(3645, 9109, 0, SOUTH, 5)
        7857(3646, 9117, 0, SOUTH, 5)
        8014(3586, 9099, 1, SOUTH, 5)
        8014(3593, 9096, 1, SOUTH, 5)
        8014(3593, 9102, 1, SOUTH, 5)
        8014(3593, 9110, 1, SOUTH, 5)
        8014(3603, 9099, 1, SOUTH, 5)
        8014(3603, 9106, 1, SOUTH, 5)
        8014(3604, 9095, 1, SOUTH, 5)
        8014(3604, 9110, 1, SOUTH, 5)
        8014(3606, 9109, 1, SOUTH, 5)
        8014(3613, 9112, 1, SOUTH, 5)
        8014(3614, 9095, 1, SOUTH, 5)
        8014(3614, 9103, 1, SOUTH, 5)
        8014(3621, 9106, 1, SOUTH, 5)
        8014(3623, 9141, 1, SOUTH, 5)
        8014(3625, 9121, 1, SOUTH, 5)
        8014(3625, 9132, 1, SOUTH, 5)
        8014(3626, 9134, 1, SOUTH, 5)
        8014(3629, 9131, 1, SOUTH, 5)
        8014(3629, 9149, 1, SOUTH, 5)
        8014(3632, 9142, 1, SOUTH, 5)
        8014(3633, 9121, 1, SOUTH, 5)
        8014(3636, 9114, 1, SOUTH, 5)
        8014(3636, 9131, 1, SOUTH, 5)
        8014(3639, 9121, 1, SOUTH, 5)
        8014(3640, 9132, 1, SOUTH, 5)
        8014(3640, 9142, 1, SOUTH, 5)
        8014(3587, 9100, 2, SOUTH, 5)
        8014(3587, 9103, 2, SOUTH, 5)
        8014(3590, 9105, 2, SOUTH, 5)
        8014(3591, 9100, 2, SOUTH, 5)
        8014(3598, 9103, 2, SOUTH, 5)
        8014(3610, 9103, 2, SOUTH, 5)
        8014(3617, 9105, 2, SOUTH, 5)
        8014(3618, 9100, 2, SOUTH, 5)
        8014(3620, 9104, 2, SOUTH, 5)
        8014(3630, 9118, 2, SOUTH, 5)
        8014(3630, 9145, 2, SOUTH, 5)
        8014(3631, 9148, 2, SOUTH, 5)
        8014(3632, 9115, 2, SOUTH, 5)
        8014(3632, 9126, 2, SOUTH, 5)
        8014(3632, 9138, 2, SOUTH, 5)
        8014(3635, 9115, 2, SOUTH, 5)
        8014(3635, 9119, 2, SOUTH, 5)
        8014(3635, 9146, 2, SOUTH, 5)
    }
}