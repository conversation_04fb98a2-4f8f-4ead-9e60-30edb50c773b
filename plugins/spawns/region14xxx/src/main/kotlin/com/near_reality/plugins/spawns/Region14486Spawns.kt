package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14486Spawns : NPCSpawnsScript() {
    init {
        8318(3594, 9613, 0, <PERSON>OUT<PERSON>, 5)
        5049(3598, 9613, 0, SOUTH, 5)
        8285(3598, 9616, 0, SOUTH, 3)
        8313(3599, 9611, 0, SOUTH, 5)
        1034(3600, 9614, 0, SOUTH, 0)
        8312(3600, 9615, 0, SOUTH, 5)
        8290(3601, 9612, 0, SOUTH, 5)
        RAT_2854(3619, 9621, 0, SOUTH, 14)
        8314(3623, 9643, 0, SOUTH, 2)
        8317(3624, 9630, 0, SOUTH, 2)
        RAT_2854(3627, 9618, 0, <PERSON>O<PERSON><PERSON>, 14)
        6160(3627, 9646, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        3773(3630, 9643, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        8315(3634, 9623, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        8316(3638, 9636, 0, SOUTH, 2)
    }
}