package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14487Spawns : NPCSpawnsScript() {
    init {
        8311(3594, 9674, 0, SOUTH, 5)
        RAT_2854(3594, 9677, 0, SOUTH, 14)
        RAT_2854(3598, 9673, 0, SOUTH, 14)
        RAT_2854(3629, 9683, 0, SOUTH, 14)
        RAT_2854(3629, 9690, 0, SOUTH, 14)
        RAT_2854(3632, 9696, 0, SOUTH, 14)
        8279(3633, 9690, 0, SOUTH, 5)
        RAT_2854(3636, 9684, 0, SOUTH, 14)
    }
}