package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14488Spawns : NPCSpawnsScript() {
    init {
        MUTATED_BLOODVELD(3588, 9736, 0, SOUTH, 2)
        MUTATED_BLOODVELD(3592, 9745, 0, SOUTH, 2)
        MUTATED_BLOODVELD(3594, 9742, 0, SOUTH, 2)
        MUTATED_BLOODVELD(3596, 9750, 0, SOUTH, 2)
        MUTATED_BLOODVELD(3597, 9735, 0, SOUTH, 2)
        MUTATED_BLOODVELD(3599, 9747, 0, SOUT<PERSON>, 2)
        MUTATED_BLOODVELD(3600, 9739, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        CRAWLING_HAND_450(3608, 9773, 0, <PERSON>OUT<PERSON>, 5)
        CRAWLING_HAND_448(3609, 9776, 0, SOUTH, 7)
        8280(3611, 9737, 0, SOUTH, 5)
        CRAWLING_HAND_451(3611, 9771, 0, SOUTH, 5)
        CRAWLING_HAND_449(3612, 9774, 0, SOUTH, 5)
        CRAWLING_HAND_448(3616, 9751, 0, SOUTH, 7)
        CRAWLING_HAND_452(3616, 9756, 0, SOUTH, 5)
        CRAWLING_HAND_448(3617, 9759, 0, SOUTH, 7)
        CRAWLING_HAND_451(3619, 9748, 0, SOUTH, 5)
        CRAWLING_HAND_450(3620, 9755, 0, SOUTH, 5)
        CRAWLING_HAND_449(3622, 9746, 0, SOUTH, 5)
    }
}