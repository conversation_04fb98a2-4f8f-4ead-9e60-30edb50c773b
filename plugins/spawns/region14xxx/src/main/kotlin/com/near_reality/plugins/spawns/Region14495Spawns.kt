package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14495Spawns : NPCSpawnsScript() {
    init {
        ICE_SPIDER(3593, 10217, 0, SOUTH, 6)
        LONGTAILED_WYVERN(3596, 10195, 0, SOUTH, 8)
        LONGTAILED_WYVERN(3599, 10186, 0, SOUTH, 8)
        LONGTAILED_WYVERN(3601, 10200, 0, SOUTH, 8)
        LONGTAILED_WYVERN(3602, 10193, 0, SOUTH, 8)
        ICE_SPIDER(3603, 10223, 0, SOUTH, 6)
        TALONED_WYVERN(3605, 10215, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        TALONED_WYVERN(3608, 10204, 0, <PERSON>O<PERSON><PERSON>, 8)
        TALONED_WYVERN(3610, 10215, 0, <PERSON>O<PERSON><PERSON>, 8)
        TALONED_WYVERN(3612, 10210, 0, SOUTH, 8)
        SPITTING_WYVERN(3623, 10189, 0, SOUTH, 9)
        SPITTING_WYVERN(3623, 10195, 0, SOUTH, 9)
        SPITTING_WYVERN(3624, 10185, 0, SOUTH, 9)
        SPITTING_WYVERN(3630, 10193, 0, SOUTH, 9)
        ICE_SPIDER(3631, 10228, 0, SOUTH, 6)
        ANCIENT_WYVERN(3634, 10205, 0, SOUTH, 2)
        ANCIENT_WYVERN(3634, 10212, 0, SOUTH, 2)
        ICE_SPIDER(3635, 10233, 0, SOUTH, 6)
        ICE_SPIDER(3636, 10227, 0, SOUTH, 6)
    }
}