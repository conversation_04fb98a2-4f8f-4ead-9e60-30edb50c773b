package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14496Spawns : NPCSpawnsScript() {
    init {
        FOSSIL_ROCK(3596, 10285, 0, SOUTH, 2)
        LONGTAILED_WYVERN(3597, 10254, 0, SOUTH, 8)
        LONGTAILED_WYVERN(3602, 10250, 0, SOUTH, 8)
        SPITTING_WYVERN(3603, 10273, 0, SOUTH, 9)
        FOSSIL_ROCK(3603, 10295, 0, SOUTH, 2)
        SPITTING_WYVERN(3604, 10266, 0, SOUTH, 9)
        FOSSIL_ROCK(3606, 10293, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        LONGTAILED_WYVERN(3609, 10251, 0, <PERSON>O<PERSON><PERSON>, 8)
        SPITTING_WYVERN(3611, 10280, 0, SOUTH, 9)
        SPITTING_WYVERN(3612, 10274, 0, SOUTH, 9)
        LONGTAILED_WYVERN(3615, 10249, 0, SOUTH, 8)
        TALONED_WYVERN(3622, 10291, 0, SOUTH, 8)
        TALONED_WYVERN(3627, 10288, 0, SOUTH, 8)
        TALONED_WYVERN(3628, 10282, 0, SOUTH, 8)
        FOSSIL_ROCK(3629, 10266, 0, SOUTH, 2)
        TALONED_WYVERN(3634, 10280, 0, SOUTH, 8)
        FOSSIL_ROCK(3637, 10265, 0, SOUTH, 2)
        FOSSIL_ROCK(3639, 10268, 0, SOUTH, 2)
        FOSSIL_ROCK(3640, 10263, 0, SOUTH, 2)
        WEVE(3597, 10293, 0, SOUTH, 0)
        ANCIENT_WYVERN(3627, 10246, 0, SOUTH, 2)
        ANCIENT_WYVERN(3632, 10252, 0, SOUTH, 2)
        ANCIENT_WYVERN(3636, 10246, 0, SOUTH, 2)
    }
}