package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14638Spawns : NPCSpawnsScript() {
    init {
        PIRATE_4026(3655, 2973, 0, SOUTH, 3)
        PIRATE_4028(3656, 2960, 0, SOUTH, 3)
        PIRATE_4023(3658, 2952, 0, SOUTH, 4)
        MAMA(3663, 2980, 0, SOUTH, 3)
        PIRATE_4023(3664, 2995, 0, SOUTH, 4)
        PIRATE_4028(3666, 2977, 0, SOUT<PERSON>, 3)
        PIRATE_4025(3666, 2983, 0, SOUTH, 3)
        JOE_4019(3666, 2990, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        PIRATE_4032(3667, 2963, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BILL_TEACH_4015(3667, 2981, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        PIRATE_4027(3667, 2993, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        P<PERSON><PERSON>E_4024(3668, 2994, 0, SOUTH, 5)
        PIRATE_4026(3670, 2983, 0, SOUTH, 3)
        PIRATE_4030(3671, 2978, 0, SOUTH, 5)
        PIRATE_4023(3672, 2967, 0, SOUTH, 4)
        CHARLEY(3673, 2970, 0, SOUTH, 3)
        PIRATE_4030(3675, 2988, 0, SOUTH, 5)
        PIRATE_4027(3676, 2991, 0, SOUTH, 5)
        PATCHY(3677, 2978, 0, SOUTH, 5)
        1953(3681, 2963, 0, SOUTH, 0)
        RAT_2854(3682, 2948, 0, SOUTH, 14)
        BANKER_4054(3682, 2981, 0, WEST, 0)
        BANKER_4055(3682, 2982, 0, WEST, 0)
        BANKER_4054(3682, 2983, 0, WEST, 0)
        PIRATE_4031(3683, 2971, 0, SOUTH, 5)
        RAT_2854(3688, 2947, 0, SOUTH, 14)
        PIRATE_4024(3690, 2979, 0, SOUTH, 5)
        PIRATE_4025(3691, 2977, 0, SOUTH, 3)
        PIRATE_4024(3691, 2984, 0, SOUTH, 5)
        PIRATE_4023(3695, 2950, 0, SOUTH, 4)
        PIRATE_4023(3695, 2969, 0, SOUTH, 4)
        MIKE(3695, 2978, 0, SOUTH, 2)
        PIRATE_4027(3697, 2990, 0, SOUTH, 5)
        PIRATE_4033(3649, 3004, 0, SOUTH, 3)
        PIRATE_4034(3653, 3006, 0, SOUTH, 5)
        PIRATE_4028(3656, 2992, 1, SOUTH, 3)
        PIRATE_4035(3660, 3005, 0, SOUTH, 5)
        PIRATE_4026(3663, 2978, 1, SOUTH, 3)
        PIRATE_4031(3664, 2992, 1, SOUTH, 5)
        PIRATE_4025(3665, 2983, 1, SOUTH, 3)
        PIRATE_4032(3665, 2990, 1, SOUTH, 4)
        PIRATE_4029(3665, 2995, 1, SOUTH, 2)
        PIRATE_4036(3665, 3006, 0, SOUTH, 5)
        PIRATE_4028(3666, 2976, 1, SOUTH, 3)
        PIRATE_4024(3670, 2979, 1, SOUTH, 5)
        PIRATE_4030(3670, 2983, 1, SOUTH, 5)
        PIRATE_4037(3673, 3006, 0, SOUTH, 4)
        PIRATE_4023(3675, 2954, 0, SOUTH, 4)
        PIRATE_4031(3676, 2949, 1, SOUTH, 5)
        PIRATE_4024(3678, 2957, 0, SOUTH, 5)
        RAT_2854(3679, 2949, 1, SOUTH, 14)
        PIRATE_4029(3679, 2950, 1, SOUTH, 2)
        PIRATE_4028(3682, 2947, 1, SOUTH, 3)
        PIRATE_4038(3682, 3006, 0, SOUTH, 5)
        BILL_TEACH_4016(3683, 2948, 1, SOUTH, 3)
        PIRATE_4027(3683, 2954, 0, SOUTH, 5)
        PIRATE_4027(3684, 2949, 1, SOUTH, 5)
        PIRATE_4032(3686, 2955, 0, SOUTH, 4)
        PIRATE_4039(3688, 3006, 0, SOUTH, 5)
        SMITH(3689, 2954, 0, SOUTH, 3)
        PIRATE_4040(3692, 3005, 0, SOUTH, 5)
        PIRATE_4029(3698, 2997, 1, SOUTH, 2)
        PIRATE_4041(3701, 3006, 0, SOUTH, 5)
        PIRATE_4042(3710, 3004, 0, SOUTH, 3)
    }
}