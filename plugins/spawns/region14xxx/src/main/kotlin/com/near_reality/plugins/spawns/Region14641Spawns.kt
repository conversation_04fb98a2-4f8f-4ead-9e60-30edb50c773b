package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14641Spawns : NPCSpawnsScript() {
    init {
        GULL(3677, 3179, 0, SOUTH, 7)
        SEAGULL_9609(3683, 3186, 0, SOUTH, 5)
        5050(3684, 3182, 0, SOUTH, 5)
        9518(3685, 3181, 0, SOUTH, 5)
        8281(3686, 3186, 0, SOUTH, 5)
        9519(3689, 3182, 0, SOUTH, 5)
        8284(3690, 3189, 0, SOUTH, 3)
        5047(3696, 3181, 0, SOUTH, 2)
        8282(3697, 3186, 0, <PERSON>OUTH, 0)
        5059(3704, 3192, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8289(3705, 3182, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8291(3705, 3189, 0, <PERSON><PERSON>UT<PERSON>, 5)
        GULL(3706, 3171, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        5058(3706, 3188, 0, SOUTH, 5)
        5060(3707, 3186, 0, SOUTH, 5)
        9520(3707, 3188, 0, SOUTH, 5)
        8292(3708, 3187, 0, SOUTH, 5)
    }
}