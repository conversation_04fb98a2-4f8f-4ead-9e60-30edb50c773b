package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14642Spawns : NPCSpawnsScript() {
    init {
        BANKER_8322(3649, 3206, 0, NORTH, 0)
        MEIYERDITCH_CITIZEN_8331(3649, 3215, 0, SOUTH, 3)
        VAMPYRE_JUVENILE_8327(3649, 3220, 0, SOUTH, 0)
        BANKER_8321(3651, 3206, 0, NORTH, 0)
        BANKER_8322(3652, 3206, 0, NORTH, 0)
        VYRELADY_8333(3652, 3219, 0, SOUTH, 3)
        MEIYERDITCH_CITIZEN_8329(3652, 3226, 0, <PERSON>OUTH, 3)
        VYRELORD_8334(3653, 3213, 0, <PERSON>O<PERSON>H, 3)
        8319(3658, 3221, 0, SOUTH, 2)
        MEIYERDITCH_CITIZEN_8330(3665, 3222, 0, SOUTH, 3)
        GARTH_8206(3669, 3215, 0, NORTH, 0)
        VYRELORD_8332(3670, 3220, 0, SOUTH, 3)
        8308(3672, 3208, 0, SOUTH, 2)
        VYRELADY_8335(3672, 3212, 0, SOUTH, 3)
        8309(3673, 3207, 0, SOUTH, 2)
        8325(3673, 3223, 0, WEST, 0)
        8310(3674, 3209, 0, SOUTH, 2)
        MEIYERDITCH_CITIZEN_8328(3676, 3230, 0, SOUTH, 3)
        VYRE_ORATOR_8324(3662, 3213, 0, NORTH, 0)
        VYRE_ORATOR(3662, 3224, 0, SOUTH, 0)
    }
}