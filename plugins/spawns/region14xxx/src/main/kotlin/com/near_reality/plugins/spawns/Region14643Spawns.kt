package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14643Spawns : NPCSpawnsScript() {
    init {
        9800(3692, 3295, 0, SOUTH, 0)
        9829(3693, 3309, 0, SOUTH, 5)
        9839(3693, 3311, 0, SOUTH, 5)
        9833(3695, 3314, 0, SOUTH, 5)
        9845(3697, 3313, 0, SOUTH, 5)
        9843(3703, 3302, 0, SOUTH, 5)
        9831(3705, 3303, 0, SOUTH, 5)
        9830(3705, 3314, 0, SOUTH, 5)
        9836(3706, 3309, 0, SOUTH, 5)
        9846(3706, 3312, 0, <PERSON>O<PERSON><PERSON>, 5)
        9835(3707, 3298, 0, <PERSON>O<PERSON><PERSON>, 5)
        9842(3707, 3313, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        9832(3708, 3311, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        9837(3709, 3299, 0, SOUTH, 5)
        9844(3709, 3309, 0, SOUTH, 5)
        9828(3709, 3312, 0, SOUTH, 5)
        9834(3709, 3313, 0, SOUTH, 5)
        9840(3710, 3310, 0, SOUTH, 5)
        9848(3710, 3313, 0, SOUTH, 5)
        9841(3710, 3327, 0, SOUTH, 5)
        ANDRAS_8268(3662, 3278, 0, WEST, 0)
        9827(3709, 3315, 0, SOUTH, 5)
    }
}