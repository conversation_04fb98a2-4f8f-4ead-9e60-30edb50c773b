package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14645Spawns : NPCSpawnsScript() {
    init {
        GHOST_GUARD(3668, 3453, 0, WEST, 0)
        GHOST_GUARD(3671, 3453, 0, WEST, 0)
        SWAMP_LIZARD(3673, 3416, 0, SOUTH, 5)
        SWAMP_LIZARD(3675, 3403, 0, SOUTH, 5)
        SWAMP_LIZARD(3678, 3403, 0, SOUTH, 5)
        SWAMP_LIZARD(3678, 3416, 0, SOUTH, 5)
        SWAMP_LIZARD(3680, 3407, 0, SOUT<PERSON>, 5)
        SWAMP_LIZARD(3680, 3417, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SWAMP_LIZARD(3681, 3396, 0, <PERSON>OUTH, 5)
        SWAMP_LIZARD(3681, 3403, 0, SOUTH, 5)
        SWAMP_LIZARD(3681, 3413, 0, SOUTH, 5)
        SWAMP_LIZARD(3682, 3399, 0, SOUTH, 5)
        SWAMP_LIZARD(3683, 3406, 0, SOUTH, 5)
        SWAMP_LIZARD(3683, 3411, 0, SOUTH, 5)
        SWAMP_LIZARD(3683, 3416, 0, SOUTH, 5)
        SWAMP_LIZARD(3684, 3403, 0, SOUTH, 5)
        SWAMP_LIZARD(3686, 3414, 0, SOUTH, 5)
        SWAMPY_LOG(3686, 3443, 0, SOUTH, 0)
        SWAMPY_LOG(3690, 3438, 0, SOUTH, 0)
        SWAMPY_LOG(3690, 3444, 0, SOUTH, 0)
        SWAMPY_LOG(3694, 3439, 0, SOUTH, 0)
        SWAMP_LIZARD(3696, 3408, 0, SOUTH, 5)
        SWAMP_LIZARD(3696, 3411, 0, SOUTH, 5)
        SWAMPY_LOG(3696, 3443, 0, SOUTH, 0)
        SWAMP_LIZARD(3699, 3397, 0, SOUTH, 5)
        SWAMP_LIZARD(3699, 3405, 0, SOUTH, 5)
        SWAMP_LIZARD(3701, 3399, 0, SOUTH, 5)
        SWAMP_LIZARD(3701, 3406, 0, SOUTH, 5)
        SWAMP_LIZARD(3703, 3403, 0, SOUTH, 5)
        SWAMP_LIZARD(3704, 3400, 0, SOUTH, 5)
    }
}