package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14646Spawns : NPCSpawnsScript() {
    init {
        TORTURED_SOUL(3648, 3472, 0, SOUTH, 5)
        GHOST_GUARD(3652, 3484, 0, WEST, 0)
        GHOST_GUARD(3652, 3487, 0, WEST, 0)
        GHOST_DISCIPLE(3655, 3516, 0, SOUTH, 4)
        6128(3657, 3469, 0, SOUTH, 3)
        GHOST_VILLAGER(3658, 3465, 0, SOUTH, 4)
        GHOST_SHOPKEEPER(3658, 3477, 0, SOUTH, 4)
        GHOST_GUARD(3658, 3508, 0, WEST, 0)
        5857(3660, 3499, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        6710(3660, 3503, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        NECROVARUS(3660, 3516, 0, <PERSON><PERSON>UTH, 2)
        GHOST_VILLAGER(3661, 3494, 0, SOUTH, 4)
        GHOST_VILLAGER(3661, 3497, 0, SOUTH, 4)
        GHOST_GUARD(3661, 3508, 0, WEST, 0)
        GHOST_VILLAGER(3662, 3463, 0, SOUTH, 4)
        GHOST_VILLAGER(3662, 3502, 0, SOUTH, 4)
        GHOST_DISCIPLE(3662, 3516, 0, SOUTH, 4)
        6710(3664, 3488, 0, SOUTH, 8)
        GHOST_VILLAGER(3665, 3493, 0, SOUTH, 4)
        6710(3667, 3469, 0, SOUTH, 8)
        GHOST_VILLAGER(3668, 3474, 0, SOUTH, 4)
        ROBIN(3672, 3491, 0, SOUTH, 4)
        GHOST_VILLAGER(3672, 3503, 0, SOUTH, 4)
        GHOST_VILLAGER(3673, 3479, 0, SOUTH, 4)
        SARAH_8134(3674, 3468, 0, SOUTH, 4)
        6127(3674, 3484, 0, SOUTH, 2)
        GHOST_VILLAGER(3675, 3496, 0, SOUTH, 4)
        VELORINA(3678, 3510, 0, SOUTH, 2)
        GHOST_VILLAGER(3679, 3474, 0, SOUTH, 4)
        BILL_TEACH(3679, 3495, 0, SOUTH, 4)
        GHOST_INNKEEPER(3681, 3496, 0, SOUTH, 4)
        6710(3681, 3502, 0, SOUTH, 8)
        GHOST_VILLAGER(3684, 3485, 0, SOUTH, 4)
        6710(3684, 3486, 0, SOUTH, 8)
        GHOST_BANKER(3687, 3464, 0, NORTH, 0)
        GHOST_BANKER(3688, 3464, 0, NORTH, 0)
        6573(3690, 3464, 0, NORTH, 0)
        GHOST_BANKER(3691, 3464, 0, NORTH, 0)
        6710(3697, 3473, 0, SOUTH, 8)
        TORTURED_SOUL(3649, 3503, 0, SOUTH, 5)
        GHOST_DISCIPLE(3653, 3518, 1, SOUTH, 4)
        GHOST_SAILOR(3686, 3503, 0, SOUTH, 3)
        5859(3689, 3495, 0, SOUTH, 5)
        GHOST_SAILOR(3689, 3508, 0, SOUTH, 3)
        GHOST_SAILOR(3690, 3514, 0, SOUTH, 3)
        GHOST_SAILOR(3691, 3512, 0, SOUTH, 3)
        GHOST_CAPTAIN_3006(3691, 3513, 0, SOUTH, 0)
        GHOST_SAILOR(3694, 3518, 1, SOUTH, 3)
        GHOST_SAILOR(3698, 3497, 0, SOUTH, 3)
        GHOST_SAILOR(3698, 3514, 1, SOUTH, 3)
        1333(3701, 3502, 0, SOUTH, 2)
        1330(3701, 3503, 0, SOUTH, 4)
        GHOST_CAPTAIN(3703, 3487, 0, SOUTH, 0)
        GHOST_SAILOR(3704, 3496, 0, SOUTH, 3)
        5858(3660, 3499, 0, NORTH, 0)
    }
}