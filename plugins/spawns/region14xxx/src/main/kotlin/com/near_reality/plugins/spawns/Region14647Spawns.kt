package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14647Spawns : NPCSpawnsScript() {
    init {
        TORTURED_SOUL(3654, 3536, 0, SOUTH, 5)
        TORTURED_SOUL(3657, 3534, 0, SOUTH, 5)
        TORTURED_SOUL(3666, 3529, 0, SOUTH, 5)
        TORTURED_SOUL(3668, 3537, 0, SOUTH, 5)
        TORTURED_SOUL(3672, 3520, 0, SOUTH, 5)
        TORTURED_SOUL(3673, 3528, 0, SOUTH, 5)
        ANDRAS(3671, 3543, 0, SOUTH, 0)
        PIRATE_PETE(3680, 3537, 0, SOUTH_WEST, 0)
    }
}