package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14650Spawns : NPCSpawnsScript() {
    init {
        CHARLES_CHARLINGTON(3654, 3746, 0, SOUTH, 5)
        TAR_BUBBLES(3669, 3742, 0, SOUTH, 2)
        TAR_BUBBLES(3676, 3744, 0, SOUTH, 2)
        TAR_BUBBLES(3677, 3767, 0, <PERSON>OUT<PERSON>, 2)
        HOOP_SNAKE(3678, 3753, 0, SOUTH, 20)
        HOOP_SNAKE(3680, 3756, 0, SOUTH, 20)
        TAR_BUBBLES(3683, 3746, 0, SOUTH, 2)
        HOOP_SNAKE(3684, 3771, 0, <PERSON><PERSON><PERSON><PERSON>, 20)
        HOOP_SNAKE(3686, 3766, 0, <PERSON><PERSON>UT<PERSON>, 20)
    }
}