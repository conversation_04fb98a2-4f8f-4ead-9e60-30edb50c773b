package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14651Spawns : NPCSpawnsScript() {
    init {
        GULL(3660, 3828, 0, <PERSON><PERSON>UT<PERSON>, 7)
        GULL(3663, 3837, 0, <PERSON>OUTH, 7)
        HOOP_SNAKE(3664, 3784, 0, SOUTH, 20)
        HOOP_SNAKE(3665, 3803, 0, SOUTH, 20)
        HOOP_SNAKE(3666, 3780, 0, SOUTH, 20)
        TAR_BUBBLES(3666, 3792, 0, SOUTH, 2)
        HOOP_SNAKE(3666, 3806, 0, SOUTH, 20)
        HOOP_SNAKE(3678, 3804, 0, <PERSON><PERSON><PERSON><PERSON>, 20)
        471(3680, 3825, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        471(3680, 3835, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        HOOP_SNAKE(3682, 3807, 0, <PERSON><PERSON><PERSON><PERSON>, 20)
        471(3686, 3823, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        471(3690, 3831, 0, SOUTH, 0)
        471(3692, 3816, 0, SOUTH, 0)
        471(3692, 3838, 0, SOUTH, 0)
        471(3693, 3823, 0, SOUTH, 0)
        HOOP_SNAKE(3695, 3802, 0, SOUTH, 20)
        7831(3696, 3796, 0, SOUTH, 0)
        HOOP_SNAKE(3699, 3799, 0, SOUTH, 20)
        7832(3704, 3808, 0, SOUTH, 0)
        7833(3705, 3827, 0, SOUTH, 0)
        SQUIRREL_7756(3705, 3838, 0, SOUTH, 2)
        SQUIRREL_7755(3708, 3836, 0, SOUTH, 2)
    }
}