package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14732Spawns : NPCSpawnsScript() {
    init {
        8014(3665, 8990, 1, SOUTH, 5)
        8014(3666, 8985, 1, S<PERSON>UT<PERSON>, 5)
        8014(3666, 8997, 1, SOUTH, 5)
        8014(3672, 8983, 1, SOUTH, 5)
        8014(3672, 8999, 1, <PERSON><PERSON>UT<PERSON>, 5)
        8014(3679, 8983, 1, <PERSON><PERSON>UTH, 5)
        8014(3682, 8984, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        8014(3687, 9000, 1, S<PERSON>UTH, 5)
        8014(3691, 8983, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        8014(3693, 8997, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        8014(3694, 8986, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
    }
}