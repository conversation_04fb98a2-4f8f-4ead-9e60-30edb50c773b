package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14733Spawns : NPCSpawnsScript() {
    init {
        7857(3649, 9058, 0, SOUTH, 5)
        8014(3649, 9078, 0, SOUTH, 5)
        7857(3650, 9066, 0, SOUTH, 5)
        8014(3653, 9049, 0, SOUTH, 5)
        8014(3654, 9034, 0, SOUTH, 5)
        8014(3656, 9044, 0, SOUTH, 5)
        7857(3656, 9078, 0, SOUTH, 5)
        8014(3657, 9085, 0, SOUTH, 5)
        8014(3658, 9053, 0, SOUT<PERSON>, 5)
        7857(3661, 9069, 0, <PERSON>O<PERSON><PERSON>, 5)
        8014(3662, 9035, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8014(3662, 9038, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8014(3663, 9049, 0, <PERSON>OUT<PERSON>, 5)
        7857(3666, 9065, 0, SOUTH, 5)
        8014(3667, 9043, 0, SOUTH, 5)
        8014(3668, 9034, 0, SOUTH, 5)
        7857(3668, 9076, 0, SOUTH, 5)
        8014(3670, 9052, 0, SOUTH, 5)
        7857(3670, 9084, 0, SOUTH, 5)
        8014(3672, 9039, 0, SOUTH, 5)
        8014(3672, 9047, 0, SOUTH, 5)
        7857(3679, 9080, 0, SOUTH, 5)
        7857(3679, 9086, 0, SOUTH, 5)
        8014(3682, 9067, 0, SOUTH, 5)
        8014(3682, 9081, 0, SOUTH, 5)
        8014(3683, 9073, 0, SOUTH, 5)
        8014(3686, 9073, 0, SOUTH, 5)
        8014(3687, 9063, 0, SOUTH, 5)
        8014(3691, 9068, 0, SOUTH, 5)
        8014(3692, 9079, 0, SOUTH, 5)
        8014(3695, 9063, 0, SOUTH, 5)
        8014(3697, 9072, 0, SOUTH, 5)
        8014(3697, 9082, 0, SOUTH, 5)
        8014(3700, 9065, 0, SOUTH, 5)
        8014(3701, 9077, 0, SOUTH, 5)
        8014(3655, 9033, 1, SOUTH, 5)
        8014(3655, 9043, 1, SOUTH, 5)
        8014(3656, 9054, 1, SOUTH, 5)
        8014(3659, 9044, 1, SOUTH, 5)
        8014(3659, 9061, 1, SOUTH, 5)
        8014(3662, 9054, 1, SOUTH, 5)
        8014(3663, 9033, 1, SOUTH, 5)
        8014(3666, 9026, 1, SOUTH, 5)
        8014(3666, 9044, 1, SOUTH, 5)
        8014(3669, 9041, 1, SOUTH, 5)
        8014(3670, 9043, 1, SOUTH, 5)
        8014(3670, 9054, 1, SOUTH, 5)
        8014(3672, 9034, 1, SOUTH, 5)
        8014(3674, 9069, 1, SOUTH, 5)
        8014(3681, 9072, 1, SOUTH, 5)
        8014(3681, 9080, 1, SOUTH, 5)
        8014(3682, 9063, 1, SOUTH, 5)
        8014(3689, 9066, 1, SOUTH, 5)
        8014(3691, 9065, 1, SOUTH, 5)
        8014(3691, 9080, 1, SOUTH, 5)
        8014(3692, 9069, 1, SOUTH, 5)
        8014(3692, 9076, 1, SOUTH, 5)
        8014(3702, 9065, 1, SOUTH, 5)
        8014(3702, 9073, 1, SOUTH, 5)
        8014(3702, 9079, 1, SOUTH, 5)
        8014(3709, 9076, 1, SOUTH, 5)
        8014(3660, 9029, 2, SOUTH, 5)
        8014(3660, 9056, 2, SOUTH, 5)
        8014(3660, 9060, 2, SOUTH, 5)
        8014(3663, 9037, 2, SOUTH, 5)
        8014(3663, 9049, 2, SOUTH, 5)
        8014(3663, 9060, 2, SOUTH, 5)
        8014(3664, 9027, 2, SOUTH, 5)
        8014(3665, 9030, 2, SOUTH, 5)
        8014(3665, 9057, 2, SOUTH, 5)
        8014(3675, 9071, 2, SOUTH, 5)
        8014(3677, 9075, 2, SOUTH, 5)
        8014(3678, 9070, 2, SOUTH, 5)
        8014(3685, 9072, 2, SOUTH, 5)
        8014(3697, 9072, 2, SOUTH, 5)
        8014(3704, 9075, 2, SOUTH, 5)
        8014(3705, 9070, 2, SOUTH, 5)
        8014(3708, 9072, 2, SOUTH, 5)
        8014(3708, 9075, 2, SOUTH, 5)
    }
}