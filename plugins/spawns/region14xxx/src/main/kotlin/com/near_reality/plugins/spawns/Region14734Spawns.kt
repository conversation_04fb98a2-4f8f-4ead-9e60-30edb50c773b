package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14734Spawns : NPCSpawnsScript() {
    init {
        7857(3649, 9119, 0, SOUTH, 5)
        8014(3650, 9097, 0, SOUTH, 5)
        8015(3650, 9115, 0, SOUTH, 5)
        7857(3651, 9110, 0, SOUTH, 5)
        8017(3653, 9112, 0, SOUTH, 5)
        8014(3653, 9137, 0, SOUTH, 5)
        8017(3654, 9117, 0, SOUTH, 5)
        8014(3654, 9122, 0, SOUTH, 5)
        7857(3655, 9119, 0, SOUTH, 5)
        8014(3656, 9132, 0, <PERSON>O<PERSON><PERSON>, 5)
        8014(3657, 9089, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7857(3657, 9096, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8014(3658, 9141, 0, <PERSON>OUT<PERSON>, 5)
        7857(3659, 9108, 0, SOUTH, 5)
        8018(3661, 9106, 0, SOUTH, 5)
        8014(3662, 9123, 0, SOUTH, 5)
        8014(3662, 9126, 0, SOUTH, 5)
        8014(3663, 9137, 0, SOUTH, 5)
        8016(3664, 9103, 0, SOUTH, 5)
        7857(3666, 9101, 0, SOUTH, 5)
        8014(3667, 9131, 0, SOUTH, 5)
        8014(3668, 9122, 0, SOUTH, 5)
        7857(3669, 9090, 0, SOUTH, 5)
        7857(3670, 9106, 0, SOUTH, 5)
        8014(3670, 9140, 0, SOUTH, 5)
        8014(3672, 9127, 0, SOUTH, 5)
        8014(3672, 9135, 0, SOUTH, 5)
        8016(3675, 9121, 0, SOUTH, 5)
        8015(3676, 9109, 0, SOUTH, 5)
        7857(3677, 9089, 0, SOUTH, 5)
        8014(3682, 9098, 0, SOUTH, 5)
        8014(3683, 9110, 0, SOUTH, 5)
        8014(3686, 9093, 0, SOUTH, 5)
        8014(3686, 9103, 0, SOUTH, 5)
        8015(3687, 9133, 0, SOUTH, 5)
        8014(3688, 9112, 0, SOUTH, 5)
        8014(3691, 9096, 0, SOUTH, 5)
        8014(3692, 9107, 0, SOUTH, 5)
        8017(3692, 9127, 0, SOUTH, 5)
        8014(3696, 9112, 0, SOUTH, 5)
        8014(3697, 9102, 0, SOUTH, 5)
        8014(3700, 9102, 0, SOUTH, 5)
        8014(3701, 9094, 0, SOUTH, 5)
        8014(3701, 9108, 0, SOUTH, 5)
        8014(3655, 9121, 1, SOUTH, 5)
        8014(3655, 9131, 1, SOUTH, 5)
        8014(3656, 9142, 1, SOUTH, 5)
        8014(3659, 9132, 1, SOUTH, 5)
        8014(3659, 9149, 1, SOUTH, 5)
        8014(3662, 9142, 1, SOUTH, 5)
        8014(3663, 9121, 1, SOUTH, 5)
        8014(3666, 9114, 1, SOUTH, 5)
        8014(3666, 9132, 1, SOUTH, 5)
        8014(3669, 9129, 1, SOUTH, 5)
        8014(3670, 9131, 1, SOUTH, 5)
        8014(3670, 9142, 1, SOUTH, 5)
        8014(3672, 9122, 1, SOUTH, 5)
        8014(3674, 9099, 1, SOUTH, 5)
        8014(3681, 9096, 1, SOUTH, 5)
        8014(3681, 9102, 1, SOUTH, 5)
        8014(3681, 9110, 1, SOUTH, 5)
        8014(3691, 9099, 1, SOUTH, 5)
        8014(3691, 9106, 1, SOUTH, 5)
        8014(3692, 9095, 1, SOUTH, 5)
        8014(3692, 9110, 1, SOUTH, 5)
        8014(3694, 9109, 1, SOUTH, 5)
        8014(3701, 9112, 1, SOUTH, 5)
        8014(3702, 9095, 1, SOUTH, 5)
        8014(3702, 9103, 1, SOUTH, 5)
        8014(3709, 9106, 1, SOUTH, 5)
        8014(3660, 9117, 2, SOUTH, 5)
        8014(3660, 9144, 2, SOUTH, 5)
        8014(3660, 9148, 2, SOUTH, 5)
        8014(3663, 9125, 2, SOUTH, 5)
        8014(3663, 9137, 2, SOUTH, 5)
        8014(3663, 9148, 2, SOUTH, 5)
        8014(3664, 9115, 2, SOUTH, 5)
        8014(3665, 9118, 2, SOUTH, 5)
        8014(3665, 9145, 2, SOUTH, 5)
        8014(3675, 9100, 2, SOUTH, 5)
        8014(3675, 9103, 2, SOUTH, 5)
        8014(3678, 9105, 2, SOUTH, 5)
        8014(3679, 9100, 2, SOUTH, 5)
        8014(3686, 9103, 2, SOUTH, 5)
        8014(3698, 9103, 2, SOUTH, 5)
        8014(3705, 9105, 2, SOUTH, 5)
        8014(3706, 9100, 2, SOUTH, 5)
        8014(3708, 9104, 2, SOUTH, 5)
    }
}