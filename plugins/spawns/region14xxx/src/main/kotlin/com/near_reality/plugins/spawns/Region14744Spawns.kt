package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14744Spawns : NPCSpawnsScript() {
    init {
        VAMPYRE_JUVINATE_9684(3665, 9755, 2, SOUTH, 5)
        SLAVE_9679(3666, 9745, 2, SOUTH, 5)
        SLAVE_9682(3666, 9761, 2, SOUTH, 5)
        SLAVE_9680(3667, 9773, 2, SOUTH, 5)
        VAMPYRE_JUVINATE_9683(3673, 9776, 2, SOUTH, 5)
        VAMPYRE_JUVINATE_9683(3679, 9755, 2, SOUT<PERSON>, 5)
        VAMPYRE_JUVINATE_9684(3682, 9765, 2, <PERSON><PERSON><PERSON><PERSON>, 5)
        SLAVE_9678(3682, 9772, 2, <PERSON>OUTH, 5)
        SLAVE_9681(3684, 9749, 2, <PERSON>OUTH, 5)
        NORANNA_TYTANIN(3692, 9764, 2, SOUTH, 5)
        SLAVE_9677(3697, 9752, 2, SOUTH, 5)
        VAMPYRE_JUVINATE_9683(3698, 9756, 2, SOUTH, 5)
    }
}