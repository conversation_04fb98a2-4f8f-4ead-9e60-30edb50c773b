package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14894Spawns : NPCSpawnsScript() {
    init {
        MONKEY_1038(3716, 3005, 0, SOUTH, 8)
        JUNGLE_HORROR_1046(3721, 3005, 0, SOUTH, 10)
        JUNGLE_HORROR_1045(3723, 2986, 0, SOUTH, 12)
        MONKEY_1038(3724, 2953, 0, <PERSON>OUT<PERSON>, 8)
        SNAKE(3725, 3005, 0, <PERSON>OUTH, 4)
        SNAKE(3730, 2954, 0, SOUTH, 4)
        JUNGLE_HORROR(3733, 2957, 0, <PERSON>O<PERSON><PERSON>, 10)
        SNAKE(3734, 2959, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        S<PERSON><PERSON>(3735, 2989, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MONKEY_1038(3738, 2953, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        <PERSON><PERSON><PERSON><PERSON>_HORROR_1044(3744, 2947, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        JUNGLE_H<PERSON><PERSON><PERSON>_1044(3744, 3002, 0, SOUTH, 11)
        S<PERSON>KE(3744, 3004, 0, SOUTH, 4)
        SNAKE(3746, 2950, 0, SOUTH, 4)
        CAVEY_DAVEY(3749, 2972, 0, <PERSON>OUTH, 3)
        MONKEY_1038(3750, 2975, 0, SOUTH, 8)
        <PERSON>NAKE(3752, 2946, 0, SOUTH, 4)
        GIANT_MOSQUITO(3753, 2972, 0, SOUTH, 6)
        GIANT_MOSQUITO(3754, 2967, 0, SOUTH, 6)
        CRAB(3754, 2976, 0, SOUTH, 5)
        GIANT_MOSQUITO(3755, 2965, 0, SOUTH, 6)
        GIANT_MOSQUITO(3755, 2970, 0, SOUTH, 6)
        GIANT_MOSQUITO(3755, 2973, 0, SOUTH, 6)
        GIANT_MOSQUITO(3756, 2976, 0, SOUTH, 6)
        SNAKE(3756, 2999, 0, SOUTH, 4)
        GIANT_MOSQUITO(3757, 2974, 0, SOUTH, 6)
        CRAB(3758, 2981, 0, SOUTH, 5)
        CRAB(3759, 2959, 0, SOUTH, 5)
        CRAB(3764, 2952, 0, SOUTH, 5)
        CRAB(3771, 2986, 0, SOUTH, 5)
        CRAB(3772, 2956, 0, SOUTH, 5)
        JUNGLE_HORROR_1043(3774, 3006, 0, SOUTH, 12)
    }
}