package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14898Spawns : NPCSpawnsScript() {
    init {
        GULL(3712, 3241, 0, SOUTH, 7)
        8272(3719, 3215, 0, SOUTH, 0)
        GULL(3724, 3202, 0, SOUTH, 7)
        GULL(3727, 3227, 0, SOUTH, 7)
        GULL(3732, 3211, 0, <PERSON>OUTH, 7)
        GULL(3745, 3213, 0, SOUTH, 7)
    }
}