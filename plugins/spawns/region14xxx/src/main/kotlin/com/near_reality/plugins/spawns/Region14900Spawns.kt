package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14900Spawns : NPCSpawnsScript() {
    init {
        VYREWATCH_8307(3713, 3368, 0, SOUTH, 4)
        VYREWATCH_8304(3719, 3373, 0, SOUTH, 6)
        VYREWATCH_8300(3722, 3369, 0, SOUTH, 9)
        WILL_O_THE_WISP_3483(3726, 3381, 0, SOUTH, 2)
        VYREWATCH_8305(3728, 3374, 0, SOUTH, 5)
        VYREWATCH_8301(3730, 3365, 0, SOUTH, 4)
        VYREWATCH_8306(3737, 3377, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        VYREWATCH_8307(3740, 3371, 0, <PERSON>O<PERSON><PERSON>, 4)
        WILL_O_THE_WISP_3483(3745, 3359, 0, SOUTH, 2)
        VYREWATCH_8302(3745, 3382, 0, SOUTH, 8)
        VYREWATCH_8303(3748, 3376, 0, SOUTH, 7)
        WILL_O_THE_WISP_3483(3749, 3365, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3750, 3354, 0, SOUTH, 2)
        VYREWATCH_8300(3750, 3370, 0, SOUTH, 9)
        SWAMPY_LOG(3752, 3358, 0, SOUTH, 0)
        SWAMPY_LOG(3754, 3355, 0, SOUTH, 0)
        SWAMPY_LOG(3754, 3365, 0, SOUTH, 0)
        SWAMPY_LOG(3756, 3354, 0, SOUTH, 0)
        SWAMPY_LOG(3756, 3367, 0, SOUTH, 0)
        SWAMPY_LOG(3757, 3357, 0, SOUTH, 0)
        WILL_O_THE_WISP_3483(3757, 3363, 0, SOUTH, 2)
        WILL_O_THE_WISP_3483(3763, 3337, 0, SOUTH, 2)
        VAMPYRE_JUVENILE_4437(3731, 3358, 0, SOUTH, 4)
        VAMPYRE_JUVENILE_4438(3732, 3356, 0, SOUTH, 3)
        VAMPYRE_JUVENILE_4436(3734, 3360, 0, SOUTH, 2)
        9825(3718, 3358, 1, SOUTH, 5)
        9826(3719, 3359, 1, SOUTH, 0)
        1033(3720, 3359, 1, SOUTH, 0)
        SLEEPWALKER(3746, 3334, 0, SOUTH, 5)
    }
}