package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14901Spawns : NPCSpawnsScript() {
    init {
        GIANT_BAT(3731, 3393, 0, SOUTH, 11)
        GIANT_BAT(3743, 3394, 0, SOUTH, 11)
        GIANT_BAT(3744, 3399, 0, SOUTH, 11)
        GIANT_BAT(3751, 3401, 0, SOUTH, 11)
        GIANT_BAT(3754, 3398, 0, SOUTH, 11)
        GIANT_BAT(3759, 3395, 0, SOUTH, 11)
    }
}