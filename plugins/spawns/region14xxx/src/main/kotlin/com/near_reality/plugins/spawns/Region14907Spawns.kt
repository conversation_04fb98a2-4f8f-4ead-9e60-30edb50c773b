package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14907Spawns : NPCSpawnsScript() {
    init {
        TOOL_LEPRECHAUN(3712, 3839, 0, SOUTH, 0)
        GULL(3714, 3798, 0, SOUTH, 7)
        PETER(3714, 3804, 0, NORTH, 0)
        SQUIRREL_7754(3715, 3832, 0, SOUTH, 2)
        7497(3720, 3812, 0, SOUTH, 2)
        SHOP_KEEPER_7769(3726, 3816, 0, SOUTH, 2)
        GULL(3728, 3785, 0, SOUTH, 7)
        JOHN_7790(3732, 3812, 0, EAST, 0)
        DAVID_7791(3735, 3815, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        FOSSIL_COLLECTOR(3736, 3805, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        CHARLES_7773(3737, 3824, 0, <PERSON>OUT<PERSON>, 5)
        DOG_7771(3739, 3805, 0, SOUTH, 0)
        JOHN_7774(3753, 3802, 0, SOUTH, 5)
        MATTIMEO(3761, 3828, 0, SOUTH, 4)
        CORMORANT(3761, 3835, 0, SOUTH, 0)
        PELICAN(3767, 3831, 0, SOUTH, 4)
        GULL(3773, 3838, 0, SOUTH, 7)
        7819(3723, 3784, 1, SOUTH, 0)
        7820(3723, 3785, 1, SOUTH, 0)
    }
}