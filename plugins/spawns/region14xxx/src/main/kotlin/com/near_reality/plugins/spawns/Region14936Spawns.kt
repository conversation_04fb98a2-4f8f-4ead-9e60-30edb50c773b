package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14936Spawns : NPCSpawnsScript() {
    init {
        DWARF_7721(3719, 5678, 0, SOUTH, 2)
        RUNITE_MINOR(3720, 5661, 0, SOUTH, 5)
        MINER_6565(3721, 5679, 0, SOUTH, 4)
        MINER_6567(3728, 5641, 0, SOUTH, 2)
        MINER_6645(3741, 5689, 0, SOUTH, 2)
        MINER_6568(3745, 5638, 0, SOUTH, 0)
        BABY_MOLE_5781(3745, 5666, 0, SOUTH, 4)
        MINER_6572(3755, 5688, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        PROSPECTOR_PERCY(3756, 5666, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        MINER_6569(3766, 5644, 0, <PERSON>OUTH, 0)
        MINER_6570(3768, 5663, 0, SOUTH, 2)
        MINER_6571(3768, 5681, 0, SOUTH, 2)
        MINER_5814(3738, 5685, 0, SOUTH, 6)
        MERCY(3752, 5677, 0, SOUTH, 0)
        MINER_5606(3758, 5673, 0, SOUTH, 5)
        MINER_5813(3764, 5668, 0, SOUTH, 2)
    }
}