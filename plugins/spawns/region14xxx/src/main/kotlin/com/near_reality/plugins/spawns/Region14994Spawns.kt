package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14994Spawns : NPCSpawnsScript() {
    init {
        ALBINO_BAT(3718, 9357, 0, SOUTH, 7)
        CAVE_HORROR_1048(3720, 9349, 0, SOUTH, 10)
        CAVE_HORROR_1049(3723, 9379, 0, SOUTH, 9)
        CAVE_HORROR_1049(3724, 9391, 0, <PERSON>OUTH, 9)
        ALBINO_BAT(3725, 9370, 0, SOUTH, 7)
        CAVE_HORROR_1048(3726, 9399, 0, SOUTH, 10)
        CAVE_HORROR_1049(3729, 9349, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        CAVE_HORROR(3732, 9390, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        CAVE_HORROR_1048(3733, 9353, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        CAVE_HORROR_1048(3733, 9367, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        ALBINO_BAT(3734, 9398, 0, SOUTH, 7)
        ALBINO_BAT(3739, 9385, 0, SOUTH, 7)
        CAVE_HORROR(3741, 9373, 0, SOUTH, 11)
        ALBINO_BAT(3747, 9398, 0, SOUTH, 7)
        CAVE_HORROR_1048(3754, 9385, 0, SOUTH, 10)
        ALBINO_BAT(3756, 9391, 0, SOUTH, 7)
        CAVE_HORROR_1048(3759, 9395, 0, SOUTH, 10)
        ALBINO_BAT(3761, 9405, 0, SOUTH, 7)
    }
}