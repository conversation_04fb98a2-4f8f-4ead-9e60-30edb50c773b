package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14995Spawns : NPCSpawnsScript() {
    init {
        CAVE_HORROR_1050(3717, 9423, 0, SOUTH, 12)
        ALBINO_BAT(3720, 9429, 0, SOUTH, 7)
        CAVE_HORROR(3721, 9418, 0, SOUTH, 11)
        CAVE_HORROR(3722, 9439, 0, <PERSON><PERSON>UTH, 11)
        ALBINO_BAT(3726, 9442, 0, <PERSON>OUTH, 7)
        CAVE_HORROR(3729, 9419, 0, SOUTH, 11)
        CAVE_HORROR_1050(3729, 9428, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        CAVE_HORROR_1049(3729, 9457, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        CAVE_HORROR_1050(3731, 9413, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        CAVE_HORROR_1050(3734, 9445, 0, SOUTH, 12)
        CAVE_HORROR(3734, 9461, 0, SOUTH, 11)
        CAVE_HORROR_1050(3737, 9412, 0, SOUTH, 12)
        CAVE_HORROR(3739, 9442, 0, SOUTH, 11)
        CAVE_HORROR_1049(3740, 9452, 0, SOUTH, 9)
        CAVE_HORROR(3741, 9418, 0, SOUTH, 11)
        CAVE_HORROR_1049(3741, 9426, 0, SOUTH, 9)
        CAVE_HORROR_1050(3744, 9463, 0, SOUTH, 12)
        ALBINO_BAT(3745, 9410, 0, SOUTH, 7)
        CAVE_HORROR_1050(3748, 9424, 0, SOUTH, 12)
        CAVE_HORROR_1049(3750, 9447, 0, SOUTH, 9)
        ALBINO_BAT(3751, 9425, 0, SOUTH, 7)
        CAVE_HORROR(3751, 9456, 0, SOUTH, 11)
        CAVE_HORROR(3753, 9460, 0, SOUTH, 11)
        CAVE_HORROR_1050(3754, 9439, 0, SOUTH, 12)
        CAVE_HORROR(3755, 9423, 0, SOUTH, 11)
        CAVE_HORROR(3756, 9432, 0, SOUTH, 11)
        CAVE_HORROR_1050(3762, 9451, 0, SOUTH, 12)
        ALBINO_BAT(3763, 9453, 0, SOUTH, 7)
        CAVE_HORROR(3764, 9424, 0, SOUTH, 11)
        CAVE_HORROR_1050(3764, 9434, 0, SOUTH, 8)
        CAVE_HORROR_1050(3764, 9463, 0, SOUTH, 12)
        CAVE_HORROR_1050(3769, 9421, 0, SOUTH, 12)
        CAVE_HORROR_1050(3769, 9465, 0, SOUTH, 12)
        CAVE_HORROR(3770, 9451, 0, SOUTH, 11)
        ALBINO_BAT(3773, 9461, 0, SOUTH, 7)
    }
}