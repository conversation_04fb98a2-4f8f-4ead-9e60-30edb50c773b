package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region14999Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(3733, 9710, 1, SOUTH, 14)
        RAT_2854(3738, 9709, 1, SOUTH, 14)
        RAT_2854(3744, 9711, 1, SOUTH, 14)
        RAT_2854(3750, 9710, 1, SOUTH, 14)
        9509(3754, 9711, 1, SOUTH, 5)
        RAT_2854(3757, 9711, 1, SOUTH, 14)
        9510(3759, 9708, 1, SOUTH, 5)
        RAT_2854(3760, 9709, 1, SOUTH, 14)
    }
}