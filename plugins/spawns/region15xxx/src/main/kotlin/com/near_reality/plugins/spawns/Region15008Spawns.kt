package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15008Spawns : NPCSpawnsScript() {
    init {
        8669(3727, 10251, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        8668(3727, 10252, 1, SOUT<PERSON>, 5)
        8673(3727, 10253, 1, SOUTH, 5)
        8674(3727, 10254, 1, SOUTH, 5)
        8670(3728, 10251, 1, <PERSON>OUTH, 5)
        8671(3728, 10252, 1, SOUTH, 5)
        8672(3728, 10253, 1, SOUTH, 5)
        FISH_4832(3728, 10266, 1, SOUTH, 2)
        CETO(3728, 10293, 1, <PERSON><PERSON><PERSON><PERSON>, 0)
        8669(3731, 10244, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        8670(3731, 10245, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        FISH(3731, 10271, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        8668(3732, 10244, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        8671(3732, 10245, 1, SOUTH, 5)
        8674(3732, 10246, 1, SOUTH, 5)
        TOOL_LEPRECHAUN_7757(3732, 10270, 1, WEST, 0)
        8673(3733, 10244, 1, SOUTH, 5)
        8672(3733, 10245, 1, SOUTH, 5)
        FISH_4838(3733, 10263, 1, SOUTH, 4)
        MERNIA(3735, 10271, 1, WEST, 0)
        FISH_SHOAL(3737, 10299, 1, SOUTH, 6)
        8672(3738, 10252, 1, SOUTH, 5)
        8670(3738, 10253, 1, SOUTH, 5)
        8668(3738, 10254, 1, SOUTH, 5)
        FISH_SHOAL(3738, 10297, 1, SOUTH, 6)
        FISH_SHOAL(3738, 10300, 1, SOUTH, 6)
        8673(3739, 10252, 1, SOUTH, 5)
        8671(3739, 10253, 1, SOUTH, 5)
        8669(3739, 10254, 1, SOUTH, 5)
        8674(3739, 10255, 1, SOUTH, 5)
        FISH_SHOAL(3739, 10292, 1, SOUTH, 6)
        FISH_SHOAL(3739, 10295, 1, SOUTH, 6)
        8672(3741, 10280, 1, SOUTH, 5)
        8673(3741, 10281, 1, SOUTH, 5)
        FISH_SHOAL(3741, 10292, 1, SOUTH, 6)
        FISH_SHOAL(3741, 10299, 1, SOUTH, 6)
        8669(3742, 10272, 1, SOUTH, 5)
        8668(3742, 10273, 1, SOUTH, 5)
        8674(3742, 10279, 1, SOUTH, 5)
        8670(3742, 10280, 1, SOUTH, 5)
        8668(3742, 10281, 1, SOUTH, 5)
        FISH_SHOAL(3742, 10290, 1, SOUTH, 6)
        8671(3743, 10272, 1, SOUTH, 5)
        8670(3743, 10273, 1, SOUTH, 5)
        8674(3743, 10274, 1, SOUTH, 5)
        8671(3743, 10280, 1, SOUTH, 5)
        8669(3743, 10281, 1, SOUTH, 5)
        8672(3744, 10272, 1, SOUTH, 5)
        8673(3744, 10273, 1, SOUTH, 5)
        FISH_SHOAL(3744, 10300, 1, SOUTH, 6)
        FISH_SHOAL(3747, 10300, 1, SOUTH, 6)
        FISH_SHOAL(3748, 10298, 1, SOUTH, 6)
        8673(3751, 10284, 1, SOUTH, 5)
        8672(3751, 10285, 1, SOUTH, 5)
        8670(3752, 10284, 1, SOUTH, 5)
        8668(3752, 10285, 1, SOUTH, 5)
        8674(3752, 10286, 1, SOUTH, 5)
        8671(3753, 10284, 1, SOUTH, 5)
        8669(3753, 10285, 1, SOUTH, 5)
        8670(3754, 10247, 1, SOUTH, 5)
        8669(3754, 10248, 1, SOUTH, 5)
        8671(3755, 10247, 1, SOUTH, 5)
        8668(3755, 10248, 1, SOUTH, 5)
        8674(3755, 10249, 1, SOUTH, 5)
        8673(3756, 10247, 1, SOUTH, 5)
        8672(3756, 10248, 1, SOUTH, 5)
        MAIRIN(3765, 10290, 1, SOUTH_WEST, 0)
        8669(3768, 10271, 1, SOUTH, 5)
        8674(3769, 10252, 1, SOUTH, 5)
        8668(3769, 10271, 1, SOUTH, 5)
        8673(3770, 10252, 1, SOUTH, 5)
        8673(3770, 10269, 1, SOUTH, 5)
        8671(3770, 10270, 1, SOUTH, 5)
        8670(3770, 10271, 1, SOUTH, 5)
        8674(3770, 10295, 1, SOUTH, 5)
        8672(3771, 10252, 1, SOUTH, 5)
        8674(3771, 10269, 1, SOUTH, 5)
        8672(3771, 10270, 1, SOUTH, 5)
        8674(3771, 10286, 1, SOUTH, 5)
        8673(3771, 10294, 1, SOUTH, 5)
        8670(3771, 10295, 1, SOUTH, 5)
        8668(3771, 10296, 1, SOUTH, 5)
        8669(3772, 10252, 1, SOUTH, 5)
        8668(3772, 10253, 1, SOUTH, 5)
        8670(3772, 10254, 1, SOUTH, 5)
        8668(3772, 10285, 1, SOUTH, 5)
        8670(3772, 10286, 1, SOUTH, 5)
        8672(3772, 10287, 1, SOUTH, 5)
        8672(3772, 10294, 1, SOUTH, 5)
        8671(3772, 10295, 1, SOUTH, 5)
        8669(3772, 10296, 1, SOUTH, 5)
        8671(3773, 10254, 1, SOUTH, 5)
        8669(3773, 10285, 1, SOUTH, 5)
        8671(3773, 10286, 1, SOUTH, 5)
        8673(3773, 10287, 1, SOUTH, 5)
    }
}