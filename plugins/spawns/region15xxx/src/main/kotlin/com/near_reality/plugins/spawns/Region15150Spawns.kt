package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15150Spawns : NPCSpawnsScript() {
    init {
        MONKEY_1817(3785, 2974, 0, SOUTH, 0)
        MONKEY_1817(3806, 2975, 0, SOUTH, 0)
        MONKEY_1817(3810, 2972, 0, SOUTH, 0)
        MONKEY_1817(3812, 2976, 0, SOUTH, 0)
        PARROT(3822, 2954, 0, SOUTH, 0)
        PARROT_1828(3822, 3005, 0, SOUTH, 0)
        MONKEY_1817(3832, 2974, 0, SOUTH, 0)
    }
}