package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15151Spawns : NPCSpawnsScript() {
    init {
        CRAB(3777, 3056, 0, <PERSON>OUT<PERSON>, 5)
        SNAKE(3779, 3011, 0, SOUTH, 4)
        JUNGLE_HORROR_1044(3780, 3021, 0, SOUTH, 11)
        SNAKE(3781, 3030, 0, SOUTH, 4)
        JUNGLE_HORROR_1046(3782, 3010, 0, SOUTH, 10)
        CRAB(3785, 3053, 0, SOUT<PERSON>, 5)
        JUNGLE_HORROR_1045(3786, 3022, 0, SOUTH, 12)
        JUNGLE_HORROR_1043(3786, 3037, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        MONKEY_1038(3789, 3027, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        JUNGLE_HORROR_1044(3790, 3019, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        CRAB(3791, 3042, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        CRAB(3791, 3048, 0, SOUTH, 5)
        SNAKE(3792, 3033, 0, SOUTH, 4)
        CRAB(3798, 3051, 0, SOUTH, 5)
        FANCY_DAN(3801, 3015, 0, SOUTH, 2)
        CRAB(3803, 3049, 0, SOUTH, 5)
        SNAKE(3805, 3028, 0, <PERSON>OUTH, 4)
        CRAB(3808, 3045, 0, SOUTH, 5)
        CRAB(3809, 3038, 0, SOUTH, 5)
        HONEST_JIMMY(3812, 3021, 0, SOUTH, 2)
        CRAB(3812, 3057, 0, SOUTH, 5)
        CRAB(3816, 3062, 0, SOUTH, 5)
        CRAB(3817, 3043, 0, SOUTH, 5)
        SNAKE(3821, 3033, 0, SOUTH, 4)
        SAN_FAN(3824, 3015, 0, SOUTH, 2)
        CRAB(3827, 3038, 0, SOUTH, 5)
        CRAB(3828, 3051, 0, SOUTH, 5)
        SNAKE(3828, 3058, 0, SOUTH, 4)
        CRAB(3830, 3066, 0, SOUTH, 5)
        CRAB(3831, 3023, 0, SOUTH, 5)
        CRAB(3831, 3030, 0, SOUTH, 5)
        MONKEY_1038(3832, 3056, 0, SOUTH, 8)
        MONKEY_1038(3834, 3021, 0, SOUTH, 8)
        CRAB(3834, 3057, 0, SOUTH, 5)
        SNAKE(3836, 3014, 0, SOUTH, 4)
        CRAB(3838, 3029, 0, SOUTH, 5)
    }
}