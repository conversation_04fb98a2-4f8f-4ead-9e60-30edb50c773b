package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15162Spawns : NPCSpawnsScript() {
    init {
        FOSSIL_ROCK(3800, 3752, 0, SOUTH, 2)
        CRAB_1553(3801, 3754, 0, SOUTH, 4)
        FOSSIL_ROCK(3802, 3756, 0, SOUTH, 2)
        FOSSIL_ROCK(3806, 3755, 0, SOUTH, 2)
        CRAB_1553(3817, 3755, 0, SOUTH, 4)
        FOSSIL_ROCK(3817, 3760, 0, SOUTH, 2)
    }
}