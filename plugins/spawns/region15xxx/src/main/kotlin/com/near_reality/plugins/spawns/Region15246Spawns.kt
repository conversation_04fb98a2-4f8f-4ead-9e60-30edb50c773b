package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15246Spawns : NPCSpawnsScript() {
    init {
        7856(3786, 9098, 0, SOUTH, 5)
        7856(3787, 9110, 0, SOUTH, 5)
        7856(3790, 9093, 0, SOUTH, 5)
        7856(3790, 9103, 0, SOUTH, 5)
        7856(3792, 9112, 0, SOUTH, 5)
        7857(3793, 9129, 0, SOUTH, 5)
        7944(3793, 9134, 0, SOUTH, 5)
        7856(3795, 9096, 0, SOUTH, 5)
        7856(3796, 9107, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7944(3799, 9128, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7856(3800, 9112, 0, SOUT<PERSON>, 5)
        7856(3801, 9102, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7856(3804, 9102, 0, SOUT<PERSON>, 5)
        7856(3805, 9094, 0, SOUTH, 5)
        7856(3805, 9108, 0, SOUTH, 5)
        7857(3808, 9089, 0, SOUTH, 5)
        7857(3808, 9095, 0, SOUTH, 5)
        7857(3814, 9110, 0, SOUTH, 5)
        7856(3815, 9128, 0, SOUTH, 5)
        7856(3815, 9136, 0, SOUTH, 5)
        7856(3816, 9096, 0, SOUTH, 5)
        7857(3817, 9091, 0, SOUTH, 5)
        7856(3817, 9123, 0, SOUTH, 5)
        7857(3819, 9099, 0, SOUTH, 5)
        7856(3819, 9107, 0, SOUTH, 5)
        7856(3819, 9141, 0, SOUTH, 5)
        7856(3820, 9132, 0, SOUTH, 5)
        7857(3821, 9110, 0, SOUTH, 5)
        7856(3824, 9126, 0, SOUTH, 5)
        7856(3825, 9137, 0, SOUTH, 5)
        7856(3825, 9140, 0, SOUTH, 5)
        7857(3826, 9106, 0, SOUTH, 5)
        7856(3829, 9122, 0, SOUTH, 5)
        7944(3830, 9088, 0, SOUTH, 5)
        7856(3831, 9131, 0, SOUTH, 5)
        7856(3833, 9109, 0, SOUTH, 5)
        7856(3833, 9141, 0, SOUTH, 5)
        7856(3834, 9126, 0, SOUTH, 5)
        7857(3837, 9109, 0, SOUTH, 5)
        7857(3838, 9117, 0, SOUTH, 5)
        7856(3838, 9123, 0, SOUTH, 5)
        7856(3778, 9099, 1, SOUTH, 5)
        7856(3785, 9096, 1, SOUTH, 5)
        7856(3785, 9102, 1, SOUTH, 5)
        7856(3785, 9110, 1, SOUTH, 5)
        7856(3795, 9099, 1, SOUTH, 5)
        7856(3795, 9106, 1, SOUTH, 5)
        7856(3796, 9095, 1, SOUTH, 5)
        7856(3796, 9110, 1, SOUTH, 5)
        7856(3798, 9109, 1, SOUTH, 5)
        7856(3805, 9112, 1, SOUTH, 5)
        7856(3806, 9095, 1, SOUTH, 5)
        7856(3806, 9103, 1, SOUTH, 5)
        7856(3813, 9106, 1, SOUTH, 5)
        7856(3815, 9141, 1, SOUTH, 5)
        7856(3817, 9121, 1, SOUTH, 5)
        7856(3817, 9132, 1, SOUTH, 5)
        7856(3818, 9134, 1, SOUTH, 5)
        7856(3821, 9131, 1, SOUTH, 5)
        7856(3821, 9149, 1, SOUTH, 5)
        7856(3824, 9142, 1, SOUTH, 5)
        7856(3825, 9121, 1, SOUTH, 5)
        7856(3828, 9114, 1, SOUTH, 5)
        7856(3828, 9131, 1, SOUTH, 5)
        7856(3831, 9121, 1, SOUTH, 5)
        7856(3832, 9132, 1, SOUTH, 5)
        7856(3832, 9142, 1, SOUTH, 5)
        7856(3779, 9100, 2, SOUTH, 5)
        7856(3779, 9103, 2, SOUTH, 5)
        7856(3782, 9105, 2, SOUTH, 5)
        7856(3783, 9100, 2, SOUTH, 5)
        7856(3790, 9103, 2, SOUTH, 5)
        7856(3802, 9103, 2, SOUTH, 5)
        7856(3809, 9105, 2, SOUTH, 5)
        7856(3810, 9100, 2, SOUTH, 5)
        7856(3812, 9104, 2, SOUTH, 5)
        7856(3822, 9118, 2, SOUTH, 5)
        7856(3822, 9145, 2, SOUTH, 5)
        7856(3823, 9148, 2, SOUTH, 5)
        7856(3824, 9115, 2, SOUTH, 5)
        7856(3824, 9126, 2, SOUTH, 5)
        7856(3824, 9138, 2, SOUTH, 5)
        7856(3827, 9115, 2, SOUTH, 5)
        7856(3827, 9119, 2, SOUTH, 5)
        7856(3827, 9146, 2, SOUTH, 5)
    }
}