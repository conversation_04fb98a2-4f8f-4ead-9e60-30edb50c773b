package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15248Spawns : NPCSpawnsScript() {
    init {
        1962(3783, 9223, 0, <PERSON>OUTH, 5)
        1955(3784, 9225, 0, SOUTH, 5)
        1960(3784, 9227, 0, SOUTH, 5)
        1964(3786, 9224, 0, SOUTH, 5)
        1961(3786, 9226, 0, SOUTH, 5)
        1963(3787, 9222, 0, <PERSON>OUTH, 5)
        ZOMBIE_PIRATE_583(3792, 9254, 1, SOUTH, 5)
        ZOMBIE_PIRATE_564(3797, 9253, 1, SOUT<PERSON>, 5)
        ZOMBIE_PIRATE_587(3798, 9258, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        ZOMBIE_PIRATE_589(3799, 9247, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        ZOMBIE_PIRATE_570(3803, 9249, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        ZOMBIE_PIRATE_596(3805, 9255, 1, SOUTH, 5)
        ZOMBIE_PIRATE_585(3809, 9253, 1, SOUTH, 5)
        ZOMBIE_PIRATE_591(3814, 9250, 1, SOUTH, 5)
        ZOMBIE_PIRATE_594(3817, 9259, 1, <PERSON>OUTH, 5)
        ZOMBIE_<PERSON><PERSON>ATE_566(3819, 9253, 1, <PERSON>OUTH, 5)
        ZOMBIE_PIRATE_598(3823, 9254, 1, SOUTH, 5)
    }
}