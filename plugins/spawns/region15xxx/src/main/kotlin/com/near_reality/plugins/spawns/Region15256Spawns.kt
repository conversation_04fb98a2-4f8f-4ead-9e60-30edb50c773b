package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15256Spawns : NPCSpawnsScript() {
    init {
        SPIDER_3019(3784, 9747, 1, SOUT<PERSON>, 8)
        SPIDER_3019(3786, 9777, 1, SOUTH, 8)
        LITHIL(3790, 9760, 1, SOUTH, 5)
        SPIDER_3019(3798, 9732, 1, SOUTH, 8)
        OATHBREAKER_EPIWS(3806, 9734, 1, SOUTH, 5)
        9459(3805, 9745, 1, EAST, 0)
        OATHBREAKER_BATS(3807, 9732, 1, SOUTH, 5)
        SISTER_ASERET(3808, 9728, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        OATH_LORD_DROWS(3809, 9735, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        OATHBREAKER_MALS(3810, 9733, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        9473(3811, 9777, 1, WEST, 0)
        SPIDER_3019(3816, 9732, 1, SO<PERSON>H, 8)
        LUMIERE(3826, 9760, 1, SOUTH, 5)
        SPIDER_3019(3826, 9790, 1, SOUTH, 8)
        SPIDER_3019(3831, 9747, 1, SOUTH, 8)
        SPIDER_3019(3832, 9759, 1, SOUTH, 8)
        DAER_K<PERSON>ND(3821, 9778, 2, SOUTH, 5)
    }
}