package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15257Spawns : NPCSpawnsScript() {
    init {
        STRONG_RONNY(3784, 9793, 1, SOUTH, 5)
        SISTER_NAOJ(3798, 9817, 1, SOUTH, 5)
        GREATER_DEMON(3804, 9829, 1, SOUTH, 2)
        GREATER_DEMON_2029(3805, 9825, 1, <PERSON>OUT<PERSON>, 7)
        GREATER_DEMON_2026(3810, 9825, 1, SOUT<PERSON>, 4)
        GREATER_DEMON_2028(3811, 9830, 1, <PERSON><PERSON>UTH, 4)
    }
}