package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15264Spawns : NPCSpawnsScript() {
    init {
        LOBSTROSITY(3812, 10251, 1, SOUTH, 2)
        LOBSTROSITY(3817, 10254, 1, SOUTH, 2)
        LOBSTROSITY(3821, 10250, 1, SOUTH, 2)
        LOBSTROSITY(3824, 10254, 1, SOUTH, 2)
        LOBSTROSITY(3825, 10248, 1, SOUTH, 2)
        LOBSTROSITY(3826, 10261, 1, SOUT<PERSON>, 2)
        LOBSTROSITY(3829, 10256, 1, SOUT<PERSON>, 2)
        LOBSTROSITY(3830, 10260, 1, SOUTH, 2)
        LOBSTROSITY(3832, 10245, 1, <PERSON><PERSON><PERSON><PERSON>, 2)
    }
}