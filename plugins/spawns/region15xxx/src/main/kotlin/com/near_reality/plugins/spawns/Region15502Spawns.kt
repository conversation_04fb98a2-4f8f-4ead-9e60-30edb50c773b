package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region15502Spawns : NPCSpawnsScript() {
    init {
        7944(3841, 9097, 0, SOUTH, 5)
        7857(3841, 9119, 0, SOUTH, 5)
        7857(3843, 9110, 0, SOUTH, 5)
        7856(3845, 9137, 0, SOUTH, 5)
        7856(3846, 9122, 0, SOUTH, 5)
        7857(3847, 9119, 0, SOUTH, 5)
        7856(3848, 9111, 0, SOUTH, 5)
        7856(3848, 9132, 0, SOUTH, 5)
        7944(3849, 9088, 0, SOUT<PERSON>, 5)
        7857(3849, 9096, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7856(3850, 9141, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7857(3851, 9108, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7856(3854, 9123, 0, SOUT<PERSON>, 5)
        7856(3854, 9126, 0, SO<PERSON>H, 5)
        7856(3855, 9137, 0, SOUTH, 5)
        7857(3858, 9101, 0, SOUTH, 5)
        7856(3859, 9108, 0, SOUTH, 5)
        7856(3859, 9131, 0, SOUTH, 5)
        7856(3860, 9122, 0, SOUTH, 5)
        7857(3861, 9090, 0, SOUTH, 5)
        7856(3861, 9094, 0, SOUTH, 5)
        7857(3862, 9106, 0, SOUTH, 5)
        7857(3862, 9113, 0, SOUTH, 5)
        7856(3862, 9140, 0, SOUTH, 5)
        7856(3864, 9127, 0, SOUTH, 5)
        7856(3864, 9135, 0, SOUTH, 5)
        7857(3869, 9089, 0, SOUTH, 5)
        7856(3874, 9098, 0, SOUTH, 5)
        7856(3875, 9089, 0, SOUTH, 5)
        7856(3875, 9110, 0, SOUTH, 5)
        7856(3878, 9093, 0, SOUTH, 5)
        7856(3878, 9103, 0, SOUTH, 5)
        7856(3880, 9112, 0, SOUTH, 5)
        7944(3880, 9128, 0, SOUTH, 5)
        7857(3881, 9134, 0, SOUTH, 5)
        7856(3883, 9096, 0, SOUTH, 5)
        7856(3884, 9107, 0, SOUTH, 5)
        7944(3885, 9132, 0, SOUTH, 5)
        7856(3888, 9112, 0, SOUTH, 5)
        7856(3889, 9102, 0, SOUTH, 5)
        7856(3892, 9102, 0, SOUTH, 5)
        7856(3893, 9094, 0, SOUTH, 5)
        7856(3893, 9108, 0, SOUTH, 5)
        7856(3847, 9121, 1, SOUTH, 5)
        7856(3847, 9131, 1, SOUTH, 5)
        7856(3848, 9142, 1, SOUTH, 5)
        7856(3851, 9132, 1, SOUTH, 5)
        7856(3851, 9149, 1, SOUTH, 5)
        7856(3854, 9142, 1, SOUTH, 5)
        7856(3855, 9121, 1, SOUTH, 5)
        7856(3858, 9114, 1, SOUTH, 5)
        7856(3858, 9132, 1, SOUTH, 5)
        7856(3861, 9129, 1, SOUTH, 5)
        7856(3862, 9131, 1, SOUTH, 5)
        7856(3862, 9142, 1, SOUTH, 5)
        7856(3864, 9122, 1, SOUTH, 5)
        7856(3866, 9099, 1, SOUTH, 5)
        7856(3873, 9096, 1, SOUTH, 5)
        7856(3873, 9102, 1, SOUTH, 5)
        7856(3873, 9110, 1, SOUTH, 5)
        7856(3883, 9099, 1, SOUTH, 5)
        7856(3883, 9106, 1, SOUTH, 5)
        7856(3884, 9095, 1, SOUTH, 5)
        7856(3884, 9110, 1, SOUTH, 5)
        7856(3886, 9109, 1, SOUTH, 5)
        7856(3893, 9112, 1, SOUTH, 5)
        7856(3894, 9095, 1, SOUTH, 5)
        7856(3894, 9103, 1, SOUTH, 5)
        7856(3901, 9106, 1, SOUTH, 5)
        7856(3852, 9117, 2, SOUTH, 5)
        7856(3852, 9144, 2, SOUTH, 5)
        7856(3852, 9148, 2, SOUTH, 5)
        7856(3855, 9125, 2, SOUTH, 5)
        7856(3855, 9137, 2, SOUTH, 5)
        7856(3855, 9148, 2, SOUTH, 5)
        7856(3856, 9115, 2, SOUTH, 5)
        7856(3857, 9118, 2, SOUTH, 5)
        7856(3857, 9145, 2, SOUTH, 5)
        7856(3867, 9100, 2, SOUTH, 5)
        7856(3867, 9103, 2, SOUTH, 5)
        7856(3870, 9105, 2, SOUTH, 5)
        7856(3871, 9100, 2, SOUTH, 5)
        7856(3878, 9103, 2, SOUTH, 5)
        7856(3890, 9103, 2, SOUTH, 5)
        7856(3897, 9105, 2, SOUTH, 5)
        7856(3898, 9100, 2, SOUTH, 5)
        7856(3900, 9104, 2, SOUTH, 5)
    }
}