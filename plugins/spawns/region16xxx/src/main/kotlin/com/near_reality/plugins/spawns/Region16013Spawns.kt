package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region16013Spawns : NPCSpawnsScript() {
    init {
        8489(3971, 9059, 0, SOUTH, 5)
        7857(3974, 9033, 0, SOUTH, 5)
        7857(3975, 9032, 0, SOUTH, 5)
        8488(3976, 9032, 0, SOUTH, 5)
        7857(3983, 9051, 0, SOUTH, 5)
        8489(3987, 9086, 0, SOUTH, 5)
        7857(3994, 9040, 0, SOUTH, 5)
        7857(4002, 9056, 0, SOUTH, 5)
        7857(4006, 9080, 0, SOUTH, 5)
        8488(4009, 9076, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7857(4010, 9066, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8490(4012, 9065, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7857(4024, 9025, 0, SOUTH, 5)
        8488(4028, 9064, 0, SO<PERSON>H, 5)
        7857(4030, 9050, 0, SOUTH, 5)
    }
}