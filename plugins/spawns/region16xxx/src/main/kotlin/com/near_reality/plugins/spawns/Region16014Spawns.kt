package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region16014Spawns : NPCSpawnsScript() {
    init {
        8489(3971, 9135, 0, SOUTH, 5)
        8490(3973, 9090, 0, SOUTH, 5)
        8489(3981, 9116, 0, SOUTH, 5)
        7857(4001, 9089, 0, SOUTH, 5)
        7857(4004, 9114, 0, SOUTH, 5)
        8489(4004, 9140, 0, SOUTH, 5)
        7857(4006, 9094, 0, SOUTH, 5)
        8488(4007, 9093, 0, SOUTH, 5)
        7857(4010, 9099, 0, <PERSON>O<PERSON><PERSON>, 5)
        7857(4018, 9105, 0, SO<PERSON><PERSON>, 5)
        8488(4019, 9109, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7857(4025, 9114, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7857(4029, 9118, 0, SOUTH, 5)
        8490(4030, 9112, 0, SO<PERSON>H, 5)
        8488(4030, 9121, 0, SOUTH, 5)
    }
}