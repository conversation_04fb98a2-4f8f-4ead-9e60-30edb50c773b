package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region16269Spawns : NPCSpawnsScript() {
    init {
        8489(4032, 9049, 0, SOUT<PERSON>, 5)
        7857(4038, 9061, 0, SOUTH, 5)
        8488(4043, 9065, 0, SOUTH, 5)
        7857(4045, 9070, 0, SOUTH, 5)
        7857(4053, 9076, 0, SOUTH, 5)
        8489(4056, 9085, 0, SOUTH, 5)
        7857(4058, 9044, 0, SOUTH, 5)
        7857(4059, 9026, 0, SOUTH, 5)
        8488(4083, 9057, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7857(4088, 9032, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7857(4088, 9058, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        7857(4093, 9081, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
    }
}