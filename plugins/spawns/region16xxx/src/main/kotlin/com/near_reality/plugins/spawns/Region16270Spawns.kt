package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region16270Spawns : NPCSpawnsScript() {
    init {
        7857(4033, 9121, 0, SOUTH, 5)
        7857(4035, 9147, 0, SOUTH, 5)
        8490(4040, 9110, 0, SOUTH, 5)
        7857(4043, 9109, 0, SOUTH, 5)
        7857(4049, 9101, 0, SOUTH, 5)
        8488(4054, 9099, 0, SOUTH, 5)
        7857(4058, 9094, 0, SOUTH, 5)
        8490(4060, 9116, 0, SOUTH, 5)
        7857(4061, 9089, 0, SOUTH, 5)
        8488(4075, 9088, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8489(4083, 9117, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
    }
}