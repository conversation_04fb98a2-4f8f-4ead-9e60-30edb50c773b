package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region16452Spawns : NPCSpawnsScript() {
    init {
        TORRMENTED_DEMON(4132, 4364, 0, SOUTH, 5)
        TORRMENTED_DEMON(4146, 4368, 0, SOUTH, 5)
        TORRMENTED_DEMON(4137, 4373, 0, SOUTH, 5)
        TORRMENTED_DEMON(4122, 4382, 0, SOUTH, 5)
        TORRMENTED_DEMON(4152, 4382, 0, SOUTH, 5)
        TORRMENTED_DEMON(4118, 4387, 0, SOUTH, 5)
        TORRMENTED_DEMON(4141, 4393, 0, <PERSON>OUT<PERSON>, 5)
        TORRMENTED_DEMON(4134, 4399, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        
    }
}