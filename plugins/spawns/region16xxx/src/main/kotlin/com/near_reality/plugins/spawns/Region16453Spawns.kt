package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region16453Spawns : NPCSpawnsScript() {
    init {
        TORRMENTED_DEMON(4104, 4468, 0, SOUTH, 5)
        TORRMENTED_DEMON(4111, 4466, 0, SOUTH, 5)
        
        TORRMENTED_DEMON(4134, 4466, 0, SOUTH, 5)
        TORRMENTED_DEMON(4141, 4465, 0, SOUTH, 5)
        
        TORRMENTED_DEMON(4138, 4451, 0, SOUTH, 5)
        TORRMENTED_DEMON(4146, 4452, 0, SOUTH, 5)
        
        TORRMENTED_DEMON(4149, 4431, 0, SOUTH, 5)
        TORRMENTED_DEMON(4151, 4427, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        
    }
}