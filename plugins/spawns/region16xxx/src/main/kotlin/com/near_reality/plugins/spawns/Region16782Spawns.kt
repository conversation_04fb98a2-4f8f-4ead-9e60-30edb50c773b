package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region16782Spawns : NPCSpawnsScript() {
    init {
        8395(4165, 9100, 0, SOUTH, 5)
        8396(4170, 9096, 0, SOUTH, 5)
        8395(4170, 9106, 0, SOUTH, 5)
        8397(4173, 9112, 0, SOUTH, 5)
        8397(4174, 9093, 0, SOUTH, 5)
        8396(4179, 9106, 0, SOUTH, 5)
        8395(4180, 9093, 0, SOUTH, 5)
        8395(4184, 9112, 0, SOUTH, 5)
        8396(4185, 9103, 0, <PERSON>OUT<PERSON>, 5)
        8397(4189, 9097, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8395(4189, 9105, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8394(4193, 9090, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8392(4194, 9093, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8396(4194, 9101, 0, SOUTH, 5)
        8397(4199, 9125, 0, SOUTH, 5)
        8395(4199, 9136, 0, SOUTH, 5)
        8398(4202, 9098, 0, SOUTH, 5)
        8395(4205, 9122, 0, SOUTH, 5)
        8396(4205, 9131, 0, SOUTH, 5)
        8395(4206, 9141, 0, SOUTH, 5)
        8396(4208, 9137, 0, SOUTH, 5)
        8396(4210, 9146, 0, SOUTH, 5)
        8395(4211, 9117, 0, SOUTH, 5)
        8398(4213, 9109, 0, SOUTH, 5)
        8397(4214, 9141, 0, SOUTH, 5)
        8393(4215, 9115, 0, SOUTH, 5)
        8396(4215, 9122, 0, SOUTH, 5)
        8392(4218, 9117, 0, SOUTH, 5)
        8397(4218, 9126, 0, SOUTH, 5)
        8395(4218, 9132, 0, SOUTH, 5)
        8397(4162, 9099, 1, SOUTH, 5)
        8395(4166, 9106, 1, SOUTH, 5)
        8396(4170, 9095, 1, SOUTH, 5)
        8397(4170, 9110, 1, SOUTH, 5)
        8397(4174, 9099, 1, SOUTH, 5)
        8395(4177, 9098, 1, SOUTH, 5)
        8396(4177, 9109, 1, SOUTH, 5)
        8399(4178, 9127, 1, SOUTH, 5)
        8399(4179, 9133, 1, SOUTH, 5)
        8396(4182, 9102, 1, SOUTH, 5)
        8395(4182, 9110, 1, SOUTH, 5)
        8399(4182, 9127, 1, SOUTH, 5)
        8399(4184, 9130, 1, SOUTH, 5)
        8395(4189, 9095, 1, SOUTH, 5)
        8397(4189, 9111, 1, SOUTH, 5)
        8397(4190, 9099, 1, SOUTH, 5)
        8396(4194, 9106, 1, SOUTH, 5)
        8396(4198, 9100, 1, SOUTH, 5)
        8397(4200, 9141, 1, SOUTH, 5)
        8397(4201, 9122, 1, SOUTH, 5)
        8395(4201, 9134, 1, SOUTH, 5)
        8396(4202, 9129, 1, SOUTH, 5)
        8395(4205, 9118, 1, SOUTH, 5)
        8396(4205, 9146, 1, SOUTH, 5)
        8396(4209, 9134, 1, SOUTH, 5)
        8396(4211, 9150, 1, SOUTH, 5)
        8397(4212, 9114, 1, SOUTH, 5)
        8397(4212, 9126, 1, SOUTH, 5)
        8397(4212, 9142, 1, SOUTH, 5)
        8395(4213, 9129, 1, SOUTH, 5)
        8396(4216, 9122, 1, SOUTH, 5)
        8395(4216, 9141, 1, SOUTH, 5)
        8396(4164, 9100, 2, SOUTH, 5)
        8395(4166, 9103, 2, SOUTH, 5)
        8396(4174, 9103, 2, SOUTH, 5)
        8397(4185, 9103, 2, SOUTH, 5)
        8397(4192, 9101, 2, SOUTH, 5)
        8396(4194, 9105, 2, SOUTH, 5)
        8396(4206, 9146, 2, SOUTH, 5)
        8395(4208, 9118, 2, SOUTH, 5)
        8396(4208, 9126, 2, SOUTH, 5)
        8397(4208, 9137, 2, SOUTH, 5)
        8397(4210, 9144, 2, SOUTH, 5)
        8396(4211, 9116, 2, SOUTH, 5)
    }
}