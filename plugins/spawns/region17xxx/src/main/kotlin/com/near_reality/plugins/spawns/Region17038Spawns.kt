package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region17038Spawns : NPCSpawnsScript() {
    init {
        8395(4229, 9131, 0, SOUT<PERSON>, 5)
        8397(4229, 9137, 0, SOUTH, 5)
        8392(4230, 9115, 0, SOUTH, 5)
        8396(4232, 9141, 0, SOUTH, 5)
        8397(4233, 9122, 0, SOUTH, 5)
        8398(4234, 9109, 0, SOUTH, 5)
        8395(4236, 9146, 0, SOUTH, 5)
        8396(4237, 9117, 0, SOUTH, 5)
        8396(4239, 9126, 0, SOUT<PERSON>, 5)
        8395(4241, 9122, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8396(4242, 9132, 0, <PERSON>O<PERSON><PERSON>, 5)
        8395(4242, 9141, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8398(4245, 9098, 0, <PERSON><PERSON>UT<PERSON>, 5)
        8395(4248, 9127, 0, S<PERSON><PERSON><PERSON>, 5)
        8397(4248, 9138, 0, SOUTH, 5)
        8394(4249, 9092, 0, SOUTH, 5)
        8395(4253, 9100, 0, SOUTH, 5)
        8393(4254, 9093, 0, SOUTH, 5)
        8392(4254, 9122, 0, SOUTH, 5)
        8396(4258, 9096, 0, SOUTH, 5)
        8395(4258, 9106, 0, SOUTH, 5)
        8393(4258, 9117, 0, SOUTH, 5)
        8397(4261, 9112, 0, SOUTH, 5)
        8397(4262, 9093, 0, SOUTH, 5)
        8396(4267, 9106, 0, SOUTH, 5)
        8395(4268, 9093, 0, SOUTH, 5)
        8395(4272, 9112, 0, SOUTH, 5)
        8396(4273, 9103, 0, SOUTH, 5)
        8397(4277, 9097, 0, SOUTH, 5)
        8395(4277, 9105, 0, SOUTH, 5)
        8396(4282, 9101, 0, SOUTH, 5)
        8395(4231, 9122, 1, SOUTH, 5)
        8396(4231, 9141, 1, SOUTH, 5)
        8395(4234, 9134, 1, SOUTH, 5)
        8397(4235, 9121, 1, SOUTH, 5)
        8397(4235, 9137, 1, SOUTH, 5)
        8397(4235, 9149, 1, SOUTH, 5)
        8396(4236, 9113, 1, SOUTH, 5)
        8396(4238, 9129, 1, SOUTH, 5)
        8396(4242, 9117, 1, SOUTH, 5)
        8395(4242, 9145, 1, SOUTH, 5)
        8396(4245, 9134, 1, SOUTH, 5)
        8395(4246, 9129, 1, SOUTH, 5)
        8397(4246, 9141, 1, SOUTH, 5)
        8397(4247, 9122, 1, SOUTH, 5)
        8397(4250, 9099, 1, SOUTH, 5)
        8395(4254, 9106, 1, SOUTH, 5)
        8396(4258, 9095, 1, SOUTH, 5)
        8397(4258, 9110, 1, SOUTH, 5)
        8397(4262, 9099, 1, SOUTH, 5)
        8399(4263, 9130, 1, SOUTH, 5)
        8395(4265, 9098, 1, SOUTH, 5)
        8396(4265, 9109, 1, SOUTH, 5)
        8399(4265, 9127, 1, SOUTH, 5)
        8399(4266, 9133, 1, SOUTH, 5)
        8399(4269, 9130, 1, SOUTH, 5)
        8396(4270, 9102, 1, SOUTH, 5)
        8395(4270, 9110, 1, SOUTH, 5)
        8395(4277, 9095, 1, SOUTH, 5)
        8397(4277, 9111, 1, SOUTH, 5)
        8397(4278, 9099, 1, SOUTH, 5)
        8396(4282, 9106, 1, SOUTH, 5)
        8396(4286, 9100, 1, SOUTH, 5)
        8396(4236, 9147, 2, SOUTH, 5)
        8397(4237, 9119, 2, SOUTH, 5)
        8397(4239, 9126, 2, SOUTH, 5)
        8396(4239, 9137, 2, SOUTH, 5)
        8395(4239, 9145, 2, SOUTH, 5)
        8396(4241, 9117, 2, SOUTH, 5)
        8396(4252, 9100, 2, SOUTH, 5)
        8395(4254, 9103, 2, SOUTH, 5)
        8396(4262, 9103, 2, SOUTH, 5)
        8397(4273, 9103, 2, SOUTH, 5)
        8397(4280, 9101, 2, SOUTH, 5)
        8396(4282, 9105, 2, SOUTH, 5)
    }
}