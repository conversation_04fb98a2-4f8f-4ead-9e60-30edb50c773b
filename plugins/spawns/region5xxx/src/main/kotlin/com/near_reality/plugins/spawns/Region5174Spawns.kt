package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5174Spawns : NPCSpawnsScript() {
    init {
        FROG_479(1280, 3501, 0, SOUTH, 2)
        FROG_479(1282, 3499, 0, SOUTH, 2)
        GOBLIN_3035(1303, 3486, 0, SOUTH, 11)
        GOBLIN_3033(1304, 3479, 0, SOUTH, 12)
        BIG_FROG(1308, 3494, 0, SOUTH, 3)
        BIG_FROG(1310, 3510, 0, SOUTH, 3)
        BIG_FROG(1311, 3498, 0, SOUTH, 3)
        BIG_FROG(1311, 3519, 0, <PERSON>OUT<PERSON>, 3)
        BIG_FROG(1313, 3504, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        GOBLIN_3035(1314, 3477, 0, <PERSON><PERSON><PERSON><PERSON>, 11)
        GOBLIN_3030(1315, 3485, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        RABBIT_3664(1320, 3462, 0, SOUTH, 5)
        GOBLIN_3032(1324, 3484, 0, SOUTH, 14)
        GOBLIN_3034(1326, 3491, 0, SOUTH, 14)
        RABBIT_3665(1328, 3469, 0, SOUTH, 4)
        RAT_2854(1336, 3485, 0, SOUTH, 14)
        RABBIT_3665(1338, 3472, 0, SOUTH, 4)
        RABBIT_3664(1343, 3475, 0, SOUTH, 5)
    }
}