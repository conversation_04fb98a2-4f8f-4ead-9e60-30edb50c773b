package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5175Spawns : NPCSpawnsScript() {
    init {
        LIZARDMAN_BRUTE_6919(1306, 3555, 0, SOUTH, 15)
        BIG_FROG(1307, 3529, 0, SOUTH, 3)
        LIZARDMAN_6915(1307, 3538, 0, SOUTH, 12)
        LIZARDMAN_6915(1312, 3553, 0, SOUTH, 12)
        LIZARDMAN_6917(1315, 3543, 0, SOUTH, 10)
        LIZARDMAN_BRUTE_6919(1317, 3562, 0, SOUTH, 15)
        LIZARDMAN_6917(1321, 3555, 0, SOUTH, 10)
        LIZARDMAN_BRUTE_6919(1325, 3583, 0, SOUTH, 15)
        LIZARDMAN_BRUTE_6919(1326, 3554, 0, <PERSON><PERSON><PERSON><PERSON>, 15)
        LIZARDMAN_BRUTE_6919(1340, 3541, 0, SOUTH, 15)
        LIZARDMAN_BRUTE_6919(1342, 3553, 0, SOUTH, 15)
    }
}