package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5176Spawns : NPCSpawnsScript() {
    init {
        LIZARDMAN_BRUTE_6919(1280, 3615, 0, SOUTH, 15)
        LIZARDMAN_6915(1280, 3619, 0, SOUTH, 12)
        LIZARDMAN_6917(1281, 3617, 0, SOUTH, 10)
        LIZARDMAN_6915(1285, 3599, 0, SOUTH, 12)
        LIZARDMAN_6915(1292, 3616, 0, SOUTH, 12)
        LIZARDMAN_6915(1294, 3632, 0, SOUTH, 12)
        LIZARDMAN_6917(1296, 3630, 0, SOUTH, 10)
        LIZARDMAN_BRUTE_6919(1298, 3630, 0, <PERSON>OUTH, 15)
        SWAMP_PRIEST(1305, 3618, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        LIZARDMAN_6917(1314, 3633, 0, <PERSON>OUTH, 10)
        LIZARDMAN_BRUTE_6919(1318, 3632, 0, SOUTH, 15)
        LIZARDMAN_BRUTE_6919(1323, 3586, 0, SOUTH, 15)
        LIZARDMAN_BRUTE_6919(1323, 3602, 0, SOUTH, 15)
        LIZARDMAN_BRUTE_6919(1316, 3603, 0, SOUTH, 15)
        LIZARDMAN_BRUTE_6919(1323, 3596, 0, SOUTH, 15)
    }
}