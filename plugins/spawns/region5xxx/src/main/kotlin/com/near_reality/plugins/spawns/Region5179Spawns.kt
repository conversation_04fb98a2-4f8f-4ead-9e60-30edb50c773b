package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5179Spawns : NPCSpawnsScript() {
    init {
        HALDOR_QUO_KERAN(1280, 3806, 0, SOUTH, 0)
        GIANT_BAT(1280, 3837, 0, SOUTH, 11)
        WENGRA_QUO_SIHAR(1291, 3788, 0, SOUTH, 0)
        MARMOR_QUO_NARGA(1291, 3808, 0, SOUTH, 0)
        LEKE_QUO_KERAN(1297, 3796, 0, SOUTH, 1)
        DARCOR_QUO_NARGA(1300, 3828, 0, SOUTH, 2)
        FORNEK_QUO_MATEN(1301, 3820, 0, SOUTH, 3)
        GIANT_BAT(1301, 3837, 0, <PERSON>O<PERSON><PERSON>, 11)
        CORKAT_QUO_SIHAR(1302, 3806, 0, SOUTH, 4)
        KONAR_QUO_MATEN(1308, 3784, 0, SOUTH, 0)
        XORRAH_QUO_SIHAR(1309, 3819, 0, EAST, 0)
        VORNAS_QUO_MATEN(1311, 3833, 0, EAST, 0)
        UURRAK_QUO_NARGA(1312, 3827, 0, SOUTH, 3)
        GIANT_BAT(1316, 3839, 0, SOUTH, 11)
        GORHAK_QUO_NARGA(1322, 3821, 0, SOUTH, 0)
        LOKRAA_QUO_SIHAR(1324, 3798, 0, SOUTH, 0)
        VORTAS_QUO_KERAN(1326, 3812, 0, SOUTH, 2)
        LORNOR_QUO_NARGA(1332, 3822, 0, SOUTH, 0)
        MALLAK_QUO_KERAN(1339, 3801, 0, SOUTH, 5)
    }
}