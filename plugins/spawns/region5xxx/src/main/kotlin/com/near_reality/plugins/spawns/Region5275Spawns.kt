package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5275Spawns : NPCSpawnsScript() {
    init {
        LIZARDMAN_SHAMAN_7744(1286, 9958, 0, SOUTH, 4)
        LIZARDMAN_SHAMAN_7745(1290, 9952, 0, SOUTH, 4)
        SOLDIER_7743(1302, 9970, 0, SOUTH, 3)
        LIZARDMAN_SHAMAN_7745(1303, 9945, 0, SOUTH, 4)
        LIZARDMAN_SHAMAN_7745(1307, 9951, 0, SOUTH, 4)
        CAPTAIN_CLEIVE(1307, 9972, 0, SOUTH, 0)
        SOLDIER_7743(1311, 9969, 0, SOUTH, 3)
        LIZARDMAN_SHAMAN_7744(1319, 9949, 0, <PERSON>O<PERSON><PERSON>, 4)
        LIZARDMAN_SHAMAN_7745(1324, 9954, 0, SOUTH, 4)
        LIZARDMAN_SHAMAN_7744(1327, 9970, 0, SOUTH, 4)
        LIZARDMAN_SHAMAN_7744(1330, 9964, 0, SOUTH, 4)
    }
}