package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5277Spawns : NPCSpawnsScript() {
    init {
        LIZARDMAN_SHAMAN_8565(1290, 10098, 0, SOUTH, 8)
        LIZARDMAN_SHAMAN_8565(1294, 10094, 0, SOUTH, 8)
        SHAYZIEN_INFILTRATOR(1298, 10089, 0, SOUTH, 0)
        LIZARDMAN_SHAMAN_8565(1312, 10094, 0, SOUTH, 8)
        LIZARDMAN_SHAMAN_8565(1312, 10098, 0, SOUTH, 8)
        LIZARDMAN_SHAMAN_8565(1327, 10094, 0, SOUTH, 8)
        11149(1330, 10084, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        LIZARDMAN_SHAMAN_8565(1331, 10098, 0, <PERSON>OUTH, 8)
    }
}