package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5432Spawns : NPCSpawnsScript() {
    init {
        ALRY_THE_ANGLER(1367, 3632, 0, SOUTH, 2)
        // DONT SPAWN THESE, SPAWNS ARE HANDLED BY LakeMolchArea.java
        //FISHING_SPOT_8523(1355, 3638, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1357, 3630, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1359, 3619, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1355, 3621, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1357, 3631, 0, <PERSON>O<PERSON><PERSON>, 0)
        //FISHING_SPOT_8523(1354, 3633, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1362, 3624, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1355, 3627, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1382, 3633, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1381, 3629, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1358, 3631, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1360, 3619, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1355, 3626, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1355, 3634, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1363, 3623, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1358, 3640, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1381, 3631, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1356, 3628, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1360, 3624, 0, SOUTH, 0)
        //FISHING_SPOT_8523(1354, 3640, 0, SOUTH, 0)
    }
}