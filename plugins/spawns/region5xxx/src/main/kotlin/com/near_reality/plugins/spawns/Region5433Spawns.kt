package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5433Spawns : NPCSpawnsScript() {
    init {
        LIZARDMAN_8563(1344, 3702, 0, SOUTH, 13)
        LIZARDMAN_8563(1346, 3696, 0, SOUTH, 13)
        LIZARDMAN_BRUTE_8564(1346, 3704, 0, SOUTH, 13)
        LIZARDMAN_8563(1348, 3705, 0, SOUTH, 13)
        LIZARDMAN_BRUTE_8564(1349, 3697, 0, SOUTH, 13)
        LIZARDMAN_8563(1350, 3699, 0, SOUTH, 13)
        LIZARDMAN_8563(1353, 3704, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        LIZARDMAN_BRUTE_8564(1354, 3710, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        LIZARDMAN_8563(1355, 3708, 0, SOUTH, 13)
        LIZARDMAN_BRUTE_8564(1358, 3708, 0, SOUTH, 13)
        LIZARDMAN_8563(1359, 3696, 0, SOUTH, 13)
        LIZARDMAN_BRUTE_8564(1359, 3703, 0, SOUTH, 13)
        LIZARDMAN_BRUTE_8564(1363, 3706, 0, SOUTH, 13)
        SOLDIER_8568(1365, 3695, 0, SOUTH, 5)
        RANGER_8566(1365, 3697, 0, SOUTH, 17)
        SOLDIER_8567(1365, 3700, 0, SOUTH, 5)
        LIZARDMAN_8563(1367, 3705, 0, SOUTH, 13)
        SOLDIER_8568(1368, 3700, 0, SOUTH, 5)
        RANGER_8566(1368, 3701, 0, SOUTH, 17)
        SOLDIER_8567(1369, 3701, 0, SOUTH, 5)
        LIZARDMAN_8563(1369, 3709, 0, SOUTH, 13)
        SOLDIER_8568(1370, 3690, 0, SOUTH, 5)
        SOLDIER_8567(1370, 3692, 0, SOUTH, 5)
        DOYEN_8570(1371, 3693, 0, SOUTH, 5)
        RANGER_8566(1371, 3700, 0, SOUTH, 17)
        SOLDIER_8568(1372, 3692, 0, SOUTH, 5)
        LIZARDMAN_8563(1372, 3707, 0, SOUTH, 13)
        SOLDIER_8567(1373, 3689, 0, SOUTH, 5)
        LOVAKENGJ_ENGINEER(1378, 3700, 0, SOUTH, 0)
        SOLDIER_8567(1378, 3707, 0, SOUTH, 5)
        SOLDIER_8568(1379, 3692, 0, SOUTH, 5)
        RANGER_8566(1379, 3706, 0, SOUTH, 17)
        DOYEN(1380, 3691, 0, SOUTH, 5)
        SOLDIER_8568(1381, 3707, 0, SOUTH, 5)
        SOLDIER_8567(1383, 3707, 0, SOUTH, 5)
        SOLDIER_8567(1384, 3676, 0, SOUTH, 5)
        SOLDIER_8568(1384, 3697, 0, SOUTH, 5)
        RANGER_8566(1384, 3707, 0, SOUTH, 17)
        RANGER_8566(1385, 3701, 0, SOUTH, 17)
        SOLDIER_8568(1385, 3703, 0, SOUTH, 5)
        SOLDIER_8567(1385, 3705, 0, SOUTH, 5)
        SOLDIER_8568(1386, 3687, 0, SOUTH, 5)
        SOLDIER_8568(1386, 3694, 0, SOUTH, 5)
        SOLDIER_8567(1388, 3692, 0, SOUTH, 5)
        SOLDIER_8567(1389, 3684, 0, SOUTH, 5)
        SOLDIER_8568(1389, 3689, 0, SOUTH, 5)
        SOLDIER_8567(1391, 3678, 0, SOUTH, 5)
        SOLDIER_8567(1392, 3689, 0, SOUTH, 5)
        DOYEN_8570(1393, 3682, 0, SOUTH, 5)
        SOLDIER_8567(1397, 3673, 0, SOUTH, 5)
        SOLDIER_8567(1397, 3678, 0, SOUTH, 5)
    }
}