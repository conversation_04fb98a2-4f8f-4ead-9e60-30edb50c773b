package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5434Spawns : NPCSpawnsScript() {
    init {
        DOYEN_8570(1359, 3713, 0, SOUTH, 5)
        LIZARDMAN_BRUTE_8564(1360, 3712, 0, SOUTH, 13)
        LIZARDMAN_BRUTE_8564(1361, 3717, 0, SOUTH, 13)
        LIZARDMAN_8563(1363, 3714, 0, SOUTH, 13)
        SOLDIER_8568(1363, 3717, 0, SOUTH, 5)
        SOLDIER_8567(1363, 3722, 0, SOUTH, 5)
        SOLDIER_8568(1365, 3712, 0, <PERSON><PERSON>UT<PERSON>, 5)
        LIZARDMAN_8563(1365, 3716, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        <PERSON>IZARDMAN_BRUTE_8564(1366, 3713, 0, <PERSON><PERSON><PERSON><PERSON>, 13)
        SOLDIER_8568(1366, 3724, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RANGER_8566(1368, 3735, 0, SOUTH, 17)
        SOLDIER_8567(1369, 3737, 0, SOUTH, 5)
        LIZARDMAN_BRUTE_8564(1370, 3715, 0, SOUTH, 13)
        SOLDIER_8568(1370, 3723, 0, SOUTH, 5)
        SOLDIER_8567(1370, 3746, 0, SOUTH, 5)
        WOUNDED_SOLDIER_8573(1370, 3761, 0, NORTH, 0)
        RANGER_8566(1371, 3731, 0, SOUTH, 17)
        SOLDIER_8567(1372, 3718, 0, SOUTH, 5)
        DOYEN(1372, 3749, 0, SOUTH, 5)
        WOUNDED_SOLDIER_8574(1372, 3759, 0, WEST, 0)
        WOUNDED_SOLDIER_8572(1372, 3763, 0, SOUTH, 0)
        LIZARDMAN_8563(1374, 3713, 0, SOUTH, 13)
        LIZARDMAN_BRUTE_8564(1374, 3716, 0, SOUTH, 13)
        LIZARDMAN_8563(1374, 3718, 0, SOUTH, 13)
        SOLDIER_8567(1374, 3732, 0, SOUTH, 5)
        SOLDIER_8568(1374, 3741, 0, SOUTH, 5)
        SOLDIER_8568(1374, 3742, 0, SOUTH, 5)
        SOLDIER_8568(1374, 3751, 0, SOUTH, 5)
        NURSE_EMMA_GENTSY(1374, 3762, 0, SOUTH, 4)
        SOLDIER_8567(1375, 3740, 0, SOUTH, 5)
        DOYEN(1376, 3712, 0, SOUTH, 5)
        LIZARDMAN_8563(1377, 3716, 0, SOUTH, 13)
        SOLDIER_8567(1377, 3720, 0, SOUTH, 5)
        SOLDIER_8568(1377, 3736, 0, SOUTH, 5)
        LOVAKENGJ_ENGINEER(1377, 3755, 0, SOUTH, 0)
        SOLDIER_8568(1378, 3713, 0, SOUTH, 5)
        LOVAKENGJ_ENGINEER(1381, 3744, 0, SOUTH, 0)
        SOLDIER_8575(1382, 3760, 0, SOUTH, 0)
        GENERAL_VIR(1382, 3765, 0, SOUTH, 0)
        LIZARDMAN_8563(1384, 3720, 0, SOUTH, 13)
        LIZARDMAN_BRUTE_8564(1384, 3723, 0, SOUTH, 13)
        SOLDIER_8575(1384, 3744, 0, SOUTH, 0)
        LIZARDMAN_BRUTE_8564(1385, 3715, 0, SOUTH, 13)
        RANGER_8566(1387, 3731, 0, SOUTH, 17)
        SOLDIER_8568(1387, 3735, 0, SOUTH, 5)
        SOLDIER_8568(1389, 3754, 0, SOUTH, 5)
        LIZARDMAN_8563(1390, 3718, 0, SOUTH, 13)
        LIZARDMAN_8563(1390, 3722, 0, SOUTH, 13)
        SOLDIER_8567(1390, 3732, 0, SOUTH, 5)
        LIZARDMAN_BRUTE_8564(1393, 3721, 0, SOUTH, 13)
        SOLDIER_8568(1393, 3738, 0, SOUTH, 5)
        SOLDIER_8568(1393, 3747, 0, SOUTH, 5)
        RANGER_8566(1394, 3734, 0, SOUTH, 17)
        SOLDIER_8568(1398, 3723, 0, SOUTH, 5)
        SOLDIER_8568(1399, 3719, 0, SOUTH, 5)
        SOLDIER_8567(1401, 3715, 0, SOUTH, 5)
        LOVAKENGJ_ENGINEER(1402, 3757, 0, SOUTH, 0)
        DOYEN(1405, 3718, 0, SOUTH, 5)
        SOLDIER_8567(1405, 3720, 0, SOUTH, 5)
    }
}