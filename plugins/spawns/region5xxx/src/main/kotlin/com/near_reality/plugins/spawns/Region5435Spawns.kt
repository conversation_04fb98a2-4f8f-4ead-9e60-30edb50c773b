package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5435Spawns : NPCSpawnsScript() {
    init {
        GIANT_BAT(1348, 3788, 0, SOUTH, 11)
        GIANT_BAT(1349, 3783, 0, SOUTH, 11)
        GIANT_BAT(1351, 3786, 0, SOUTH, 11)
        GIANT_BAT(1353, 3788, 0, SOUTH, 11)
    }
}