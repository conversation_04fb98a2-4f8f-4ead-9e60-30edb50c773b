package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5686Spawns : NPCSpawnsScript() {
    init {
        SKELETON_78(1425, 3515, 0, SOUTH, 8)
        SKELETON_77(1427, 3517, 0, SOUTH, 7)
        SKELETON_79(1428, 3513, 0, SOUTH, 8)
        SKELETON_81(1431, 3515, 0, SOUTH, 6)
        SKELETON_80(1431, 3517, 0, SOUTH, 8)
    }
}