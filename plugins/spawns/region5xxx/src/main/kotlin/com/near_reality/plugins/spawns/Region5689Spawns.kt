package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5689Spawns : NPCSpawnsScript() {
    init {
        SOLDIER_8567(1409, 3707, 0, SOUTH, 5)
        SOLDIER_8568(1411, 3704, 0, SOUTH, 5)
        SOLDIER_8568(1411, 3710, 0, SOUTH, 5)
        SOLDIER_8567(1412, 3707, 0, SOUTH, 5)
        LIZARDMAN_SHAMAN(1424, 3700, 0, SOUTH, 11)
        IMP_5007(1429, 3663, 0, SOUTH, 100)
        IMP_5007(1431, 3675, 0, SOUTH, 100)
        IMP_5007(1432, 3672, 0, SOUTH, 100)
        LIZARDMAN_SHAMAN_6767(1434, 3711, 0, <PERSON>O<PERSON><PERSON>, 11)
        IMP_5007(1435, 3660, 0, SOUTH, 100)
        DISCIPLE_OF_YAMA(1439, 3667, 0, SOUTH, 2)
        IMP_5007(1439, 3680, 0, SOUTH, 100)
        LIZARDMAN_6915(1440, 3701, 0, SOUTH, 12)
        IMP_5007(1442, 3672, 0, SOUTH, 100)
        LIZARDMAN_6917(1444, 3704, 0, SOUTH, 10)
        LIZARDMAN(1446, 3696, 0, SOUTH, 6)
        LIZARDMAN_6916(1447, 3698, 0, SOUTH, 12)
        SOLDIER_6869(1448, 3696, 0, SOUTH, 17)
        LIZARDMAN_BRUTE(1448, 3701, 0, SOUTH, 10)
        SOLDIER_6871(1449, 3697, 0, SOUTH, 5)
        SOLDIER_6870(1449, 3698, 0, SOUTH, 2)
        SOLDIER_6868(1449, 3699, 0, SOUTH, 6)
        SERGEANT_11083(1464, 3687, 0, SOUTH, 5)
        SOLDIER_11076(1465, 3686, 0, SOUTH, 5)
        SERGEANT_11073(1467, 3685, 0, SOUTH, 5)
        SOLDIER_6877(1468, 3682, 0, WEST, 0)
        SOLDIER_11086(1468, 3687, 0, SOUTH, 5)
        SOLDIER_11080(1471, 3648, 0, SOUTH, 5)
        SOLDIER_11072(1465, 3689, 0, SOUTH, 5)
        SOLDIER_11076(1465, 3711, 0, SOUTH, 5)
        SERGEANT_11073(1466, 3694, 0, SOUTH, 5)
        SOLDIER_11082(1466, 3708, 0, SOUTH, 5)
        RANGER_11089(1467, 3703, 0, SOUTH, 5)
        RANGER_11089(1467, 3706, 0, SOUTH, 5)
        SOLDIER_11074(1467, 3710, 0, SOUTH, 5)
    }
}