package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5690Spawns : NPCSpawnsScript() {
    init {
        SOLDIER_8567(1411, 3719, 0, SOUT<PERSON>, 5)
        SOLDIER_8567(1412, 3712, 0, SOUTH, 5)
        SOLDIER_8567(1412, 3715, 0, SOUTH, 5)
        LIZARDMAN_SHAMAN(1424, 3718, 0, <PERSON>OUTH, 11)
        SKELETON_83(1424, 3775, 0, <PERSON><PERSON>UTH, 6)
        SKELETON_82(1428, 3774, 0, SOUT<PERSON>, 7)
        SPIDER_3019(1442, 3771, 0, SOUTH, 8)
        GIANT_RAT_2863(1443, 3749, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        GIANT_RAT_2862(1446, 3746, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        RAT_2854(1446, 3750, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        SPIDER_3019(1453, 3744, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        ARMOURER_TIER_1(1453, 3746, 0, SOUTH, 2)
        SPIDER_3019(1453, 3771, 0, SOUTH, 8)
        ARMOURER_TIER_4(1460, 3761, 0, SOUTH, 2)
        SPIDER_3019(1462, 3765, 0, SOUTH, 8)
        SOLDIER_11072(1465, 3738, 0, SOUTH, 5)
        SOLDIER_11082(1467, 3739, 0, SOUTH, 5)
        SOLDIER_11082(1464, 3728, 0, SOUTH, 5)
        SOLDIER_11074(1465, 3731, 0, SOUTH, 5)
        SOLDIER_11086(1466, 3713, 0, SOUTH, 5)
        SERGEANT_11075(1466, 3730, 0, SOUTH, 5)
        SOLDIER_11072(1467, 3712, 0, SOUTH, 5)
        RANGER_11089(1467, 3717, 0, SOUTH, 5)
        RANGER_11089(1467, 3720, 0, SOUTH, 5)
        SOLDIER_11076(1467, 3731, 0, SOUTH, 5)
    }
}