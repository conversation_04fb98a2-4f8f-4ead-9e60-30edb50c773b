package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5691Spawns : NPCSpawnsScript() {
    init {
        OLD_DWARF(1410, 3811, 0, SOUTH, 5)
        RAT_2854(1423, 3819, 0, SOUTH, 14)
        MINE_SUPERVISOR_7076(1424, 3824, 0, SOUTH, 4)
        RAT_2854(1424, 3829, 0, SOUTH, 14)
        KING_SCORPION(1427, 3822, 0, SOUTH, 8)
        ARMOURER_TIER_3(1429, 3795, 0, SOUTH, 2)
        RAT_2854(1430, 3786, 0, SOUTH, 14)
        RAT_2854(1432, 3787, 0, <PERSON>OUT<PERSON>, 14)
        MINE_SUPERVISOR(1432, 3812, 0, <PERSON>O<PERSON><PERSON>, 2)
        RAT_2854(1433, 3785, 0, <PERSON><PERSON>UT<PERSON>, 14)
        BANKER_7079(1434, 3822, 0, EAST, 0)
        BANKER_7081(1434, 3824, 0, EAST, 0)
        BANKER_7079(1434, 3832, 0, EAST, 0)
        BANKER_7081(1434, 3834, 0, EAST, 0)
        ARMOURER_TIER_2(1440, 3785, 0, SOUTH, 2)
        BANKER_7077(1440, 3822, 0, WEST, 0)
        BANKER_7077(1440, 3828, 0, WEST, 0)
        BANKER_7077(1440, 3834, 0, WEST, 0)
        RAT_2854(1442, 3825, 0, SOUTH, 14)
        RAT_2854(1442, 3839, 0, SOUTH, 14)
        KING_SCORPION(1443, 3805, 0, SOUTH, 8)
        RAT_2854(1443, 3822, 0, SOUTH, 14)
        MINE_SUPERVISOR(1443, 3832, 0, SOUTH, 2)
        KING_SCORPION(1447, 3796, 0, SOUTH, 8)
        KING_SCORPION(1447, 3801, 0, SOUTH, 8)
        KING_SCORPION(1451, 3803, 0, SOUTH, 8)
        DWARF_1405(1429, 3796, 1, SOUTH, 6)
        DWARF_1406(1444, 3784, 1, SOUTH, 3)
    }
}