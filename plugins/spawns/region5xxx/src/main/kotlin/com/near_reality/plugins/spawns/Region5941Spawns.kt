package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5941Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_7459(1485, 3431, 0, SOUTH, 0)
        FISHING_SPOT_7459(1485, 3432, 0, SOUTH, 0)
        FISHING_SPOT_7461(1485, 3433, 0, SOUTH, 0)
        FISHING_SPOT_7460(1493, 3442, 0, SOUTH, 0)
        FISHING_SPOT_7459(1494, 3443, 0, SOUTH, 0)
        LAN_THE_BUTCHER(1497, 3422, 0, SOUTH, 2)
        GRIZZLY_BEAR(1499, 3449, 0, <PERSON>OUT<PERSON>, 11)
        BEAR_CUB(1502, 3452, 0, <PERSON>O<PERSON><PERSON>, 9)
        BEAR_CUB(1503, 3448, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        HUGOR(1510, 3420, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        ROBERT_BOSS(1529, 3422, 0, SOUTH, 2)
        CAPTAIN_MAGORO(1504, 3401, 0, SOUTH, 2)
    }
}