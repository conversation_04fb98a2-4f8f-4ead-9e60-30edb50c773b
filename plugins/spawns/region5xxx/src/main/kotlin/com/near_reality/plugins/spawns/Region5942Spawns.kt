package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5942Spawns : NPCSpawnsScript() {
    init {
        SHEEPDOG(1474, 3473, 0, SOUTH, 0)
        CHINCHOMPA(1480, 3503, 0, SOUTH, 7)
        CHINCHOMPA(1483, 3508, 0, SOUTH, 7)
        CHINCHOMPA(1487, 3501, 0, SOUTH, 7)
        CHINCHOMPA(1487, 3506, 0, <PERSON>OUT<PERSON>, 7)
        GIANT_RAT_2863(1492, 3461, 0, SOUTH, 6)
        GIANT_RAT_2856(1494, 3458, 0, SOUTH, 6)
        GIANT_RAT_2856(1494, 3465, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        GIANT_RAT_2864(1497, 3461, 0, <PERSON>OUT<PERSON>, 6)
        GIANT_RAT_2863(1498, 3456, 0, <PERSON><PERSON>UTH, 6)
        SQUIRREL_1418(1499, 3484, 0, <PERSON><PERSON>UT<PERSON>, 10)
        GIANT_RAT_2864(1501, 3459, 0, SOUTH, 6)
        CHICKEN(1502, 3469, 0, SOUTH, 2)
        SQUIRREL_1418(1503, 3479, 0, SOUTH, 10)
        RUBY_HARVEST(1503, 3484, 0, SOUTH, 6)
        CHICKEN(1504, 3465, 0, SOUTH, 2)
        DUCK_7473(1504, 3467, 0, SOUTH, 5)
        ROY_JR(1506, 3465, 0, SOUTH, 2)
        REGINALD(1506, 3469, 0, SOUTH, 0)
        RUBY_HARVEST(1506, 3475, 0, SOUTH, 6)
        CHICKEN(1508, 3465, 0, SOUTH, 2)
        CHICKEN(1508, 3470, 0, SOUTH, 2)
        SQUIRREL_1418(1509, 3484, 0, SOUTH, 10)
        CHICKEN(1511, 3468, 0, SOUTH, 2)
        MOSS_GIANT(1513, 3505, 0, SOUTH, 4)
        MOSS_GIANT_2092(1516, 3501, 0, SOUTH, 3)
        RUBY_HARVEST(1517, 3480, 0, SOUTH, 6)
        MOSS_GIANT_2091(1519, 3508, 0, SOUTH, 4)
        SQUIRREL(1522, 3474, 0, SOUTH, 8)
        MOSS_GIANT_2093(1522, 3504, 0, SOUTH, 4)
        RUBY_HARVEST(1524, 3473, 0, SOUTH, 6)
        RUBY_HARVEST(1525, 3480, 0, SOUTH, 6)
        SQUIRREL(1528, 3480, 0, SOUTH, 8)
    }
}