package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5943Spawns : NPCSpawnsScript() {
    init {
        GUARD_11104(1472, 3557, 0, SOUTH, 5)
        ZOMBIE_7485(1478, 3531, 0, SOUTH, 6)
        DARK_WARRIOR_11109(1478, 3534, 0, SOUTH, 5)
        DARK_WARRIOR_11111(1480, 3530, 0, SOUTH, 5)
        NECROMANCER_11088(1480, 3533, 0, SOUTH, 5)
        ZOMBIE_7486(1480, 3536, 0, SOUTH, 6)
        BAT_10888(1480, 3559, 0, SOUT<PERSON>, 5)
        ZOMBIE_7488(1482, 3530, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        BAT_10888(1482, 3570, 0, SOUTH, 5)
        ZOMBIE_7487(1483, 3534, 0, SOUTH, 5)
        BAT_10888(1490, 3557, 0, SOUTH, 5)
        GUARD_11094(1496, 3541, 0, SOUTH, 5)
        11141(1496, 3557, 0, SOUTH, 5)
        FATHER_EYSSERIC(1497, 3563, 0, SOUTH, 5)
        MIA(1498, 3567, 0, SOUTH, 5)
        GUARD_11106(1500, 3540, 0, SOUTH, 5)
        NEW_RECRUIT_TONY(1504, 3554, 0, SOUTH, 6)
        MAN_6988(1509, 3563, 0, SOUTH, 4)
        OLD_MAN_11056(1510, 3551, 0, SOUTH, 5)
        GUARD_11092(1511, 3583, 0, SOUTH, 5)
        COMMISSIONER_ANWAR(1520, 3558, 0, SOUTH, 5)
        GUARD_11102(1520, 3581, 0, SOUTH, 5)
        GUARD_11096(1524, 3551, 0, SOUTH, 5)
        GUARD_11104(1524, 3561, 0, SOUTH, 5)
        GUARD_11098(1525, 3567, 0, SOUTH, 5)
        WOMAN_6991(1526, 3574, 0, SOUTH, 5)
        WOMAN_6990(1528, 3550, 0, SOUTH, 5)
        GUARD_11092(1529, 3559, 0, SOUTH, 5)
        CAPTAIN_BRUCE(1530, 3567, 0, SOUTH, 5)
        6790(1532, 3545, 0, SOUTH, 2)
        GUARD_11094(1520, 3557, 1, SOUTH, 5)
        GUARD_11102(1522, 3565, 1, SOUTH, 5)
        GUARD_11092(1528, 3560, 1, SOUTH, 5)
        GUARD_11106(1529, 3568, 1, SOUTH, 5)
        GUARD_11094(1519, 3559, 2, SOUTH, 5)
        GUARD_11098(1522, 3568, 2, SOUTH, 5)
        SERGEANT_RICARDO(1529, 3567, 2, SOUTH, 5)
        GUARD_11104(1530, 3557, 2, SOUTH, 5)
    }
}