package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5944Spawns : NPCSpawnsScript() {
    init {
        GENERAL_BABACUS(1482, 3636, 0, EAST, 0)
        GENERAL_KILIAN(1484, 3638, 0, SOUTH, 2)
        GENERAL_SALARA(1485, 3635, 0, NORTH, 0)
        BANKER_6860(1486, 3591, 0, EAST, 0)
        BANKER_6863(1486, 3592, 0, EAST, 0)
        BANKER_6860(1486, 3593, 0, EAST, 0)
        SOLDIER_11072(1492, 3623, 0, SOUTH, 5)
        GUARD_11106(1493, 3586, 0, <PERSON>O<PERSON><PERSON>, 5)
        SOLDIER_11074(1493, 3621, 0, SOUTH, 5)
        SOLDIER_11084(1494, 3620, 0, <PERSON><PERSON>UTH, 5)
        SERGEANT_11075(1494, 3625, 0, SOUTH, 5)
        GUARD_11104(1495, 3596, 0, SOUTH, 5)
        SOLDIER_11080(1495, 3623, 0, SOUTH, 5)
        SERGEANT_11087(1495, 3646, 0, SOUTH, 5)
        KASTON(1497, 3593, 0, SOUTH, 5)
        GUARD_11092(1497, 3598, 0, SOUTH, 5)
        HEAD_GUARD(1497, 3601, 0, SOUTH, 5)
        SOLDIER_11086(1497, 3626, 0, SOUTH, 5)
        SOLDIER_11074(1497, 3639, 0, SOUTH, 5)
        GUARD_11100(1498, 3603, 0, SOUTH, 5)
        SOLDIER_11082(1498, 3634, 0, SOUTH, 5)
        WOMAN_6992(1499, 3588, 0, SOUTH, 3)
        GUARD_11094(1499, 3596, 0, SOUTH, 5)
        GUARD_11096(1499, 3600, 0, SOUTH, 5)
        SOLDIER_11076(1500, 3628, 0, SOUTH, 5)
        SOLDIER_6888(1503, 3639, 0, SOUTH, 0)
        SOLDIER_6886(1503, 3641, 0, SOUTH, 0)
        SOLDIER_6892(1503, 3643, 0, SOUTH, 0)
        SOLDIER_6890(1503, 3645, 0, SOUTH, 0)
        CAPTAIN_GINEA(1504, 3632, 0, NORTH, 0)
        SOLDIER_6886(1505, 3639, 0, SOUTH, 0)
        SOLDIER_6889(1505, 3643, 0, SOUTH, 0)
        SOLDIER_6891(1505, 3645, 0, SOUTH, 0)
        SOLDIER_11078(1506, 3626, 0, SOUTH, 5)
        SOLDIER_6883(1507, 3639, 0, SOUTH, 0)
        SOLDIER_6887(1507, 3641, 0, SOUTH, 0)
        SOLDIER_6894(1507, 3643, 0, SOUTH, 0)
        SOLDIER_6892(1507, 3645, 0, SOUTH, 0)
        MAN_11057(1508, 3585, 0, SOUTH, 5)
        ROBYN(1508, 3592, 0, SOUTH, 5)
        WOUNDED_SOLDIER(1509, 3614, 0, NORTH, 0)
        WOUNDED_SOLDIER_6856(1509, 3623, 0, EAST, 0)
        SOLDIER_6885(1509, 3639, 0, SOUTH, 0)
        SOLDIER_6884(1509, 3641, 0, SOUTH, 0)
        SOLDIER_6890(1509, 3643, 0, SOUTH, 0)
        SOLDIER_6893(1509, 3645, 0, SOUTH, 0)
        WOUNDED_SOLDIER_6834(1510, 3614, 0, NORTH, 0)
        NURSE_WOONED(1510, 3618, 0, SOUTH, 6)
        DRILL_SERGEANT(1510, 3647, 0, SOUTH, 0)
        OSTEN(1511, 3592, 0, SOUTH, 2)
        SOLDIER_11072(1511, 3632, 0, SOUTH, 5)
        SOLDIER_6888(1511, 3639, 0, SOUTH, 0)
        SOLDIER_6885(1511, 3641, 0, SOUTH, 0)
        SOLDIER_6892(1511, 3643, 0, SOUTH, 0)
        SOLDIER_6894(1511, 3645, 0, SOUTH, 0)
        SOLDIER_6884(1513, 3639, 0, SOUTH, 0)
        SOLDIER_6883(1513, 3641, 0, SOUTH, 0)
        SOLDIER_6891(1513, 3643, 0, SOUTH, 0)
        SOLDIER_6889(1513, 3645, 0, SOUTH, 0)
        WOUNDED_SOLDIER_6842(1515, 3614, 0, NORTH, 0)
        WOUNDED_SOLDIER_6828(1515, 3623, 0, SOUTH, 0)
        SOLDIER_6887(1515, 3639, 0, SOUTH, 0)
        SOLDIER_6888(1515, 3641, 0, SOUTH, 0)
        SOLDIER_6890(1515, 3643, 0, SOUTH, 0)
        SOLDIER_6893(1515, 3645, 0, SOUTH, 0)
        WOUNDED_SOLDIER_6850(1516, 3614, 0, NORTH, 0)
        WOUNDED_SOLDIER_6836(1516, 3623, 0, SOUTH, 0)
        SOLDIER_6884(1517, 3639, 0, SOUTH, 0)
        SOLDIER_6886(1517, 3641, 0, SOUTH, 0)
        SOLDIER_6889(1517, 3643, 0, SOUTH, 0)
        SOLDIER_6891(1517, 3645, 0, SOUTH, 0)
        SERGEANT_11081(1518, 3627, 0, SOUTH, 5)
        JENNIFER(1519, 3591, 0, SOUTH, 5)
        WOUNDED_SOLDIER_6840(1520, 3630, 0, SOUTH, 5)
        WOUNDED_SOLDIER_6832(1520, 3631, 0, SOUTH, 5)
        WOUNDED_SOLDIER_6848(1521, 3613, 0, SOUTH, 5)
        NURSE_INNJUREE(1521, 3621, 0, SOUTH, 0)
        SOLDIER_11076(1521, 3644, 0, SOUTH, 5)
        SOLDIER_11084(1522, 3641, 0, SOUTH, 5)
        WOUNDED_SOLDIER_6844(1523, 3622, 0, SOUTH, 0)
        SOLDIER_11076(1523, 3626, 0, SOUTH, 5)
        WOUNDED_SOLDIER_6852(1524, 3622, 0, SOUTH, 0)
        SERGEANT_11073(1525, 3642, 0, SOUTH, 5)
        WOUNDED_SOLDIER_6838(1527, 3616, 0, WEST, 0)
        WOUNDED_SOLDIER_6830(1527, 3617, 0, WEST, 0)
        SOLDIER_11082(1527, 3632, 0, SOUTH, 5)
        NURSE_BOUBOU(1528, 3627, 0, WEST, 0)
        SOLDIER_11086(1528, 3637, 0, SOUTH, 5)
        SOLDIER_11082(1528, 3641, 0, SOUTH, 5)
        WOUNDED_SOLDIER_6854(1531, 3628, 0, WEST, 0)
        WOUNDED_SOLDIER_6846(1531, 3629, 0, WEST, 0)
        SOLDIER_11078(1531, 3633, 0, SOUTH, 5)
        SOLDIER_11072(1532, 3617, 0, SOUTH, 5)
        SOLDIER_11080(1533, 3642, 0, SOUTH, 5)
        SERGEANT_11079(1534, 3619, 0, SOUTH, 5)
        SOLDIER_11074(1535, 3616, 0, SOUTH, 5)
        SOLDIER_11072(1535, 3646, 0, SOUTH, 5)
        RANGER_6881(1477, 3630, 0, SOUTH_EAST, 0)
        RANGER_6881(1478, 3643, 0, SOUTH_EAST, 0)
        11148(1486, 3635, 1, SOUTH, 5)
        RANGER_6881(1492, 3629, 0, SOUTH_EAST, 0)
        RANGER_6881(1492, 3643, 0, SOUTH_EAST, 0)
        RANGER_6881(1532, 3614, 0, SOUTH_EAST, 0)
        CRAB_1553(1473, 3625, 2, SOUTH, 4)
    }
}