package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5946Spawns : NPCSpawnsScript() {
    init {
        LIZARDMAN_6915(1480, 3719, 0, SOUTH, 12)
        DWARF_1406(1480, 3767, 0, SOUTH, 3)
        LIZARDMAN_BRUTE_6919(1481, 3717, 0, SOUTH, 15)
        LIZARDMAN_6916(1482, 3715, 0, SOUTH, 12)
        SOLDIER_6868(1482, 3721, 0, SOUTH, 6)
        11146(1482, 3746, 0, SOUTH, 5)
        DWARF_8496(1482, 3756, 0, SOUTH, 5)
        11145(1483, 3748, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SPIDER_3019(1483, 3766, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        SOLDIER_6871(1484, 3717, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        LIZARDMAN_BRUTE_6919(1484, 3722, 0, <PERSON><PERSON><PERSON><PERSON>, 15)
        <PERSON><PERSON><PERSON>AR<PERSON><PERSON>(1486, 3719, 0, SOUTH, 6)
        LIZARDMAN_6917(1487, 3721, 0, SOUTH, 10)
        11147(1487, 3748, 0, SOUTH, 5)
        SOLDIER_6870(1488, 3718, 0, SOUTH, 2)
        SOLDIER_6869(1489, 3716, 0, SOUTH, 17)
        LIZARDMAN_BRUTE(1490, 3719, 0, SOUTH, 10)
        LIZARDMAN_6915(1491, 3715, 0, SOUTH, 12)
        LIZARDMAN_6917(1493, 3718, 0, SOUTH, 10)
        LIZARDMAN_BRUTE_6919(1495, 3716, 0, SOUTH, 15)
        SPIDER_3019(1497, 3767, 0, SOUTH, 8)
        LIZARDMAN_6917(1498, 3721, 0, SOUTH, 10)
        DWARF_1403(1498, 3762, 0, SOUTH, 6)
        SOLDIER_6871(1499, 3719, 0, SOUTH, 5)
        SPIDER_3019(1500, 3759, 0, SOUTH, 8)
        LIZARDMAN_6917(1501, 3712, 0, SOUTH, 10)
        SOLDIER_6870(1501, 3718, 0, SOUTH, 2)
        GIANT_BAT(1501, 3740, 0, SOUTH, 11)
        SOLDIER_6868(1502, 3715, 0, SOUTH, 6)
        LIZARDMAN(1503, 3717, 0, SOUTH, 6)
        DWARF_1407(1503, 3770, 0, SOUTH, 5)
        LIZARDMAN_BRUTE_6919(1505, 3720, 0, SOUTH, 15)
        GIANT_BAT(1505, 3738, 0, SOUTH, 11)
        LIZARDMAN_6916(1506, 3716, 0, SOUTH, 12)
        DWARF_1404(1506, 3760, 0, SOUTH, 4)
        DWARF_1407(1507, 3753, 0, SOUTH, 5)
        DWARF_1405(1509, 3770, 0, SOUTH, 6)
        LIZARDMAN_6915(1511, 3714, 0, SOUTH, 12)
        LIZARDMAN_BRUTE_6919(1511, 3717, 0, SOUTH, 15)
        SPIDER_3019(1511, 3761, 0, SOUTH, 8)
        DWARF_1408(1512, 3769, 0, SOUTH, 3)
        LIZARDMAN_6917(1513, 3715, 0, SOUTH, 10)
        LIZARDMAN_6915(1514, 3716, 0, SOUTH, 12)
        SPIDER_3019(1515, 3757, 0, SOUTH, 8)
        SPIDER_3019(1515, 3759, 0, SOUTH, 8)
        7227(1517, 3733, 0, NORTH, 0)
        BANKER_7080(1520, 3742, 0, WEST, 0)
        BANKER_7078(1522, 3736, 0, WEST, 0)
        BANKER_7082(1522, 3742, 0, WEST, 0)
        SPIDER_3019(1522, 3753, 0, SOUTH, 8)
        BANKER_7080(1524, 3742, 0, WEST, 0)
        ARMOURER_TIER_5(1524, 3750, 0, SOUTH, 2)
        BANKER_7082(1526, 3742, 0, WEST, 0)
        BANKER_7080(1528, 3742, 0, WEST, 0)
        GIANT_BAT(1529, 3729, 0, SOUTH, 11)
        BANKER_7078(1530, 3736, 0, WEST, 0)
        BANKER_7082(1530, 3742, 0, WEST, 0)
        BAT(1532, 3727, 0, SOUTH, 23)
        GIANT_BAT(1532, 3732, 0, SOUTH, 11)
        DWARF_1408(1507, 3755, 1, SOUTH, 3)
        DWARF_1407(1511, 3768, 1, SOUTH, 5)
    }
}