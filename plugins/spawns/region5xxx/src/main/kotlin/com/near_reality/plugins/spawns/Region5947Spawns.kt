package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region5947Spawns : NPCSpawnsScript() {
    init {
        GIANT_BAT(1478, 3833, 0, SOUTH, 11)
        SPIDER_3019(1482, 3808, 0, SOUTH, 8)
        DWARF_1401(1485, 3832, 0, SOUTH, 6)
        6786(1485, 3834, 0, SOUTH, 2)
        SPIDER_3019(1487, 3782, 0, SOUTH, 8)
        DWARF_1406(1487, 3810, 0, SOUTH, 3)
        SPIDER_3019(1487, 3813, 0, SOUTH, 8)
        BAT(1495, 3835, 0, SOUTH, 23)
        DWARF_1405(1497, 3832, 0, <PERSON>O<PERSON><PERSON>, 6)
        SPIDER_3019(1498, 3833, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        BAT(1499, 3810, 0, <PERSON><PERSON><PERSON><PERSON>, 23)
        BAT(1499, 3831, 0, SOUTH, 23)
        BAT(1501, 3816, 0, SOUTH, 23)
        BAT(1502, 3833, 0, SOUTH, 23)
        SPIDER_3019(1503, 3835, 0, SOUTH, 8)
        BAT(1506, 3834, 0, SOUTH, 23)
        RAT_2854(1507, 3788, 0, SOUTH, 14)
        BAT(1507, 3816, 0, SOUTH, 23)
        BAT(1508, 3810, 0, SOUTH, 23)
        8627(1517, 3834, 0, SOUTH, 5)
        DWARF_1403(1520, 3810, 0, SOUTH, 6)
        DWARF_1408(1527, 3831, 0, SOUTH, 3)
        GIANT_RAT_2856(1530, 3789, 0, SOUTH, 6)
        DWARF_1405(1530, 3830, 0, SOUTH, 6)
        RAT_2854(1531, 3805, 0, SOUTH, 14)
        GIANT_RAT_2862(1532, 3804, 0, SOUTH, 6)
        DWARF_1407(1517, 3834, 1, SOUTH, 5)
    }
}