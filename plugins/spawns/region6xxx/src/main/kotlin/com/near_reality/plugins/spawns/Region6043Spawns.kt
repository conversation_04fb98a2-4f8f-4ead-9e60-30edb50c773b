package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6043Spawns : NPCSpawnsScript() {
    init {
        SKELETON_8072(1481, 9950, 1, SOUTH, 2)
        SKELETON_8071(1483, 9961, 1, SOUTH, 2)
        SKELETON_8071(1484, 9944, 1, SOUTH, 2)
        ZOMBIE_8069(1484, 9973, 1, SOUTH, 2)
        ZOMBIE_8067(1485, 9964, 1, SOUTH, 5)
        ZOMBIE_8067(1488, 9950, 1, SOUTH, 5)
        ZOMBIE_8068(1492, 9978, 1, SOUTH, 4)
        SKELETON_8072(1495, 9947, 1, <PERSON><PERSON><PERSON><PERSON>, 2)
        ZOMBIE_8068(1496, 9932, 1, <PERSON><PERSON><PERSON><PERSON>, 4)
        SKELETON_8070(1496, 9958, 1, <PERSON><PERSON><PERSON><PERSON>, 3)
        SKELETON_8071(1496, 9968, 1, S<PERSON>UTH, 2)
        ZOMBIE_8069(1497, 9942, 1, SOUTH, 2)
        ZOMBIE_8069(1500, 9954, 1, SOUTH, 2)
        ZOMBIE_8067(1501, 9973, 1, SOUTH, 5)
        SKELETON_8071(1503, 9948, 1, SOUTH, 2)
        SKELETON_8072(1504, 9930, 1, SOUTH, 2)
        ZOMBIE_8068(1504, 9969, 1, SOUTH, 4)
        SKELETON_8070(1509, 9945, 1, SOUTH, 3)
        ZOMBIE_8068(1510, 9953, 1, SOUTH, 4)
        ZOMBIE_8067(1510, 9958, 1, SOUTH, 5)
        ZOMBIE_8067(1511, 9939, 1, SOUTH, 5)
        SKELETON_8072(1511, 9961, 1, SOUTH, 2)
        SKELETON_8070(1511, 9965, 1, SOUTH, 3)
        SKELETON_8070(1512, 9932, 1, SOUTH, 3)
        SKELETON_8071(1514, 9950, 1, SOUTH, 2)
        ZOMBIE_8067(1515, 9974, 1, SOUTH, 5)
        SKELETON_8071(1515, 9979, 1, SOUTH, 2)
        SKELETON_8070(1521, 9960, 1, SOUTH, 3)
        SKELETON_8070(1522, 9973, 1, SOUTH, 3)
        ZOMBIE_8068(1523, 9946, 1, SOUTH, 4)
        ZOMBIE_8069(1523, 9962, 1, SOUTH, 2)
        ZOMBIE_8067(1481, 9939, 2, SOUTH, 5)
        SKELETON_8072(1481, 9945, 2, SOUTH, 2)
        SKELETON_8071(1481, 9950, 2, SOUTH, 2)
        ZOMBIE_8068(1481, 9964, 2, SOUTH, 4)
        SKELETON_8070(1486, 9933, 2, SOUTH, 3)
        SKELETON_8070(1487, 9962, 2, SOUTH, 3)
        ZOMBIE_8069(1488, 9939, 2, SOUTH, 2)
        SKELETON_8072(1488, 9955, 2, SOUTH, 2)
        SKELETON_8072(1492, 9970, 2, SOUTH, 2)
        ZOMBIE_8068(1493, 9943, 2, SOUTH, 4)
        SKELETON_8071(1493, 9958, 2, SOUTH, 2)
        ZOMBIE_8069(1494, 9930, 2, SOUTH, 2)
        ZOMBIE_8067(1497, 9950, 2, SOUTH, 5)
        ZOMBIE_8069(1498, 9979, 2, SOUTH, 2)
        ZOMBIE_8067(1500, 9936, 2, SOUTH, 5)
        ZOMBIE_8067(1500, 9964, 2, SOUTH, 5)
        SKELETON_8072(1502, 9941, 2, SOUTH, 2)
        ZOMBIE_8068(1502, 9943, 2, SOUTH, 4)
        SKELETON_8070(1502, 9956, 2, SOUTH, 3)
        ZOMBIE_8069(1503, 9952, 2, SOUTH, 2)
        ZOMBIE_8067(1503, 9971, 2, SOUTH, 5)
        SKELETON_8071(1505, 9942, 2, SOUTH, 2)
        ZOMBIE_8068(1506, 9927, 2, SOUTH, 4)
        ZOMBIE_8068(1506, 9934, 2, SOUTH, 4)
        SKELETON_8072(1507, 9966, 2, SOUTH, 2)
        SKELETON_8071(1507, 9978, 2, SOUTH, 2)
        SKELETON_8072(1513, 9950, 2, SOUTH, 2)
        ZOMBIE_8067(1514, 9942, 2, SOUTH, 5)
        SKELETON_8070(1515, 9969, 2, SOUTH, 3)
        SKELETON_8071(1516, 9930, 2, SOUTH, 2)
        SKELETON_8072(1519, 9938, 2, SOUTH, 2)
        SKELETON_8070(1519, 9955, 2, SOUTH, 3)
        SKELETON_8071(1520, 9940, 2, SOUTH, 2)
        ZOMBIE_8069(1520, 9962, 2, SOUTH, 2)
        ZOMBIE_8067(1521, 9945, 2, SOUTH, 5)
        ZOMBIE_8068(1526, 9934, 2, SOUTH, 4)
        SKELETON_8070(1526, 9950, 2, SOUTH, 3)
        SKELETON_8072(1526, 9960, 2, SOUTH, 2)
        SKELETON_8070(1483, 9943, 3, SOUTH, 3)
        ZOMBIE_8069(1483, 9963, 3, SOUTH, 2)
        ZOMBIE_8068(1483, 9966, 3, SOUTH, 4)
        ZOMBIE_8067(1491, 9932, 3, SOUTH, 5)
        ZOMBIE_8068(1491, 9945, 3, SOUTH, 4)
        SKELETON_8072(1491, 9957, 3, SOUTH, 2)
        ZOMBIE_8068(1491, 9971, 3, SOUTH, 4)
        SKELETON_8071(1499, 9951, 3, SOUTH, 2)
        ZOMBIE_8069(1501, 9966, 3, SOUTH, 2)
        ZOMBIE_8069(1501, 9977, 3, SOUTH, 2)
        SKELETON_8071(1503, 9932, 3, SOUTH, 2)
        ZOMBIE_8068(1503, 9943, 3, SOUTH, 4)
        ZOMBIE_8067(1503, 9957, 3, SOUTH, 5)
        SKELETON_8070(1504, 9964, 3, SOUTH, 3)
        ZOMBIE_8069(1506, 9950, 3, SOUTH, 2)
        SKELETON_8071(1506, 9978, 3, SOUTH, 2)
        SKELETON_8070(1507, 9937, 3, SOUTH, 3)
        SKELETON_8072(1516, 9932, 3, SOUTH, 2)
        ZOMBIE_8067(1516, 9943, 3, SOUTH, 5)
        SKELETON_8071(1516, 9957, 3, SOUTH, 2)
        ZOMBIE_8067(1516, 9971, 3, SOUTH, 5)
        ZOMBIE_8068(1523, 9935, 3, SOUTH, 4)
        ZOMBIE_8067(1524, 9937, 3, SOUTH, 5)
        SKELETON_8070(1524, 9948, 3, SOUTH, 3)
        ZOMBIE_8069(1524, 9962, 3, SOUTH, 2)
    }
}