package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6197Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT_7462(1536, 3414, 0, SOUTH, 0)
        FISHING_SPOT_7462(1542, 3417, 0, SOUTH, 0)
        COPPER_LONGTAIL(1550, 3436, 0, SOUTH, 7)
        COPPER_LONGTAIL(1554, 3435, 0, SOUTH, 7)
        COPPER_LONGTAIL(1555, 3442, 0, <PERSON>OUT<PERSON>, 7)
        COPPER_LONGTAIL(1558, 3431, 0, SOUTH, 7)
        COPPER_LONGTAIL(1561, 3435, 0, SOUT<PERSON>, 7)
        COPPER_LONGTAIL(1564, 3430, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        BARBARIAN_10985(1580, 3432, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        BARBARIAN_10986(1580, 3435, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        BARBARIAN_10988(1583, 3431, 0, SOUTH, 5)
        BARBAR<PERSON>N_10987(1583, 3435, 0, SOUTH, 5)
        BARBARIAN_10984(1584, 3433, 0, SOUTH, 5)
    }
}