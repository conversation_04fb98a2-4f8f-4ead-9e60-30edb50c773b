package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6200Spawns : NPCSpawnsScript() {
    init {
        SOLDIER_11072(1542, 3641, 0, SOUTH, 5)
        SOLDIER_11074(1545, 3636, 0, SOUTH, 5)
        SOLDIER_11049(1552, 3633, 0, SOUTH, 5)
        GUARD_11100(1553, 3588, 0, SOUTH, 5)
        SOLDIER_11082(1553, 3637, 0, SOUTH, 5)
        SERGEANT_11085(1554, 3639, 0, SOUTH, 5)
        GUARD_11094(1555, 3590, 0, SOUTH, 5)
        HEAD_GUARD_11095(1556, 3589, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SOLDIER_11084(1557, 3639, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GUARD_11098(1558, 3587, 0, SOUTH, 5)
        SOLDIER_11078(1558, 3636, 0, SOUTH, 5)
        GUARD_11104(1560, 3588, 0, SOUTH, 5)
        BANDIT_11065(1574, 3598, 0, SOUTH, 5)
        BANDIT_11064(1576, 3595, 0, SOUTH, 5)
        BANDIT_11063(1578, 3599, 0, SOUTH, 5)
        BANDIT_11065(1584, 3596, 0, SOUTH, 5)
        BANDIT_11063(1586, 3594, 0, SOUTH, 5)
        BANDIT_11064(1587, 3597, 0, SOUTH, 5)
        7230(1591, 3620, 0, WEST, 0)
        6507(1540, 3624, 0, SOUTH, 2)
        6761(1541, 3619, 0, SOUTH, 2)
        6760(1544, 3626, 0, SOUTH, 2)
        6508(1545, 3619, 0, SOUTH, 2)
        6763(1547, 3623, 0, SOUTH, 2)
    }
}