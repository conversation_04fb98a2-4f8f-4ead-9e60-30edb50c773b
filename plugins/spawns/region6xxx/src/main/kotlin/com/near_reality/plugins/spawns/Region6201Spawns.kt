package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6201Spawns : NPCSpawnsScript() {
    init {
        SOLDIER_11086(1539, 3653, 0, SOUTH, 5)
        SERGEANT_11077(1540, 3655, 0, SOUTH, 5)
        SOLDIER_11072(1543, 3655, 0, SOUTH, 5)
        CAPTAIN_RACHELLE(1543, 3686, 0, SOUTH, 2)
        SOLDIER_11084(1543, 3693, 0, SOUTH, 5)
        SOLDIER_11078(1544, 3695, 0, SOUTH, 5)
        SOLDIER_11082(1545, 3654, 0, SOUT<PERSON>, 5)
        BOAR(1545, 3671, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SOLDIER_11074(1545, 3694, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SOLDIER_11086(1546, 3703, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SOLDIER_11072(1547, 3687, 0, SO<PERSON><PERSON>, 5)
        BOAR_11067(1548, 3673, 0, SOUTH, 5)
        BOAR(1549, 3669, 0, SOUTH, 5)
        BOAR(1552, 3694, 0, SOUTH, 5)
        BOAR(1554, 3669, 0, SOUTH, 5)
        BOAR(1554, 3692, 0, SOUTH, 5)
        BOAR(1555, 3675, 0, SOUTH, 5)
        BOAR(1555, 3694, 0, SOUTH, 5)
        BOAR(1555, 3697, 0, SOUTH, 5)
        BOAR_11067(1556, 3667, 0, SOUTH, 5)
        BOAR(1556, 3689, 0, SOUTH, 5)
        BOAR(1558, 3670, 0, SOUTH, 5)
        BOAR(1560, 3673, 0, SOUTH, 5)
        GIANT_SPIDER_3017(1565, 3701, 0, SOUTH, 10)
        GIANT_SPIDER_3018(1566, 3699, 0, SOUTH, 10)
        GIANT_RAT_2857(1567, 3708, 0, SOUTH, 4)
        GIANT_RAT_2856(1568, 3711, 0, SOUTH, 6)
        BOAR_11067(1569, 3657, 0, SOUTH, 5)
        BOAR(1570, 3662, 0, SOUTH, 5)
        GIANT_SPIDER_3017(1571, 3701, 0, SOUTH, 10)
        BOAR(1572, 3686, 0, SOUTH, 5)
        GIANT_RAT_2856(1572, 3709, 0, SOUTH, 6)
        BOAR_11067(1573, 3686, 0, SOUTH, 5)
        BOAR(1574, 3658, 0, SOUTH, 5)
        BOAR(1574, 3693, 0, SOUTH, 5)
        BOAR(1575, 3666, 0, SOUTH, 5)
        BOAR(1575, 3683, 0, SOUTH, 5)
        BOAR_11067(1575, 3688, 0, SOUTH, 5)
        BOAR_11067(1576, 3661, 0, SOUTH, 5)
        GIANT_SPIDER_3018(1577, 3665, 0, SOUTH, 10)
        BOAR(1578, 3690, 0, SOUTH, 5)
        BOAR(1578, 3694, 0, SOUTH, 5)
        BOAR(1579, 3685, 0, SOUTH, 5)
        HEAD_GUARD_11105(1587, 3674, 0, SOUTH, 5)
        GUARD_11096(1588, 3672, 0, SOUTH, 5)
        GUARD_11102(1589, 3674, 0, SOUTH, 5)
        GUARD_11098(1591, 3673, 0, SOUTH, 5)
        GUARD_11092(1593, 3675, 0, SOUTH, 5)
        GUARD_11100(1594, 3671, 0, SOUTH, 5)
        GUARD_11092(1595, 3663, 0, SOUTH, 5)
        GUARD_11094(1596, 3668, 0, SOUTH, 5)
        GUARD_11098(1596, 3678, 0, SOUTH, 5)
        HEAD_GUARD_11103(1596, 3681, 0, SOUTH, 5)
        GUARD_11092(1596, 3684, 0, SOUTH, 5)
        GUARD_11106(1597, 3674, 0, SOUTH, 5)
        GUARD_11104(1598, 3663, 0, SOUTH, 5)
        HEAD_GUARD(1598, 3672, 0, SOUTH, 5)
        GUARD_11104(1598, 3681, 0, SOUTH, 5)
        SERGEANT_11073(1538, 3682, 0, SOUTH, 5)
        SOLDIER_11076(1538, 3707, 0, SOUTH, 5)
        SOLDIER_11072(1539, 3682, 0, SOUTH, 5)
        SERGEANT_11085(1539, 3706, 0, SOUTH, 5)
        SOLDIER_11086(1540, 3683, 0, SOUTH, 5)
        SOLDIER_11080(1540, 3705, 0, SOUTH, 5)
        SOLDIER_11074(1540, 3708, 0, SOUTH, 5)
        SOLDIER_11074(1541, 3681, 0, SOUTH, 5)
        SOLDIER_11084(1543, 3706, 0, SOUTH, 5)
        RANGER_7472(1595, 3668, 2, SOUTH, 5)
        RANGER_7472(1595, 3672, 2, SOUTH, 5)
        RANGER_7472(1595, 3674, 2, SOUTH, 5)
        RANGER_7472(1595, 3678, 2, SOUTH, 5)
        GUARD_11092(1595, 3661, 3, SOUTH, 5)
        HEAD_GUARD(1595, 3667, 3, SOUTH, 5)
        HEAD_GUARD(1595, 3679, 3, SOUTH, 5)
        GUARD_11092(1595, 3685, 3, SOUTH, 5)
    }
}