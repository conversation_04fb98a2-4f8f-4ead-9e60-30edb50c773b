package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6202Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(1536, 3719, 0, SOUTH, 14)
        DWARF_1403(1537, 3721, 0, SOUTH, 6)
        SPIDER_3019(1543, 3714, 0, SOUTH, 8)
        BAT(1543, 3768, 0, SOUTH, 23)
        BAT(1544, 3772, 0, SOUTH, 23)
        SPIDER_3019(1546, 3722, 0, SOUTH, 8)
        MUNTY(1551, 3749, 0, SOUTH, 2)
        SPIDER_3019(1555, 3752, 0, SO<PERSON><PERSON>, 8)
        RAT_2854(1565, 3759, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        MINER_7093(1567, 3765, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        MINER_7092(1568, 3761, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        MINER_7091(1568, 3763, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        MINER_7094(1569, 3765, 0, SOUTH, 2)
        MOGGY(1570, 3718, 0, SOUTH, 2)
        FUGGY(1570, 3760, 0, SOUTH, 2)
        GIANT_RAT_2858(1571, 3712, 0, SOUTH, 2)
        BAT(1571, 3748, 0, SOUTH, 23)
        SPIDER_3019(1574, 3766, 0, SOUTH, 8)
        BAT(1575, 3769, 0, SOUTH, 23)
        BAT(1576, 3751, 0, SOUTH, 23)
        BAT(1578, 3753, 0, SOUTH, 23)
        BAT(1578, 3771, 0, SOUTH, 23)
        SPIDER_3019(1579, 3737, 0, SOUTH, 8)
        DARK_WARRIOR_11109(1588, 3766, 0, SOUTH, 5)
        ZOMBIE_7486(1589, 3764, 0, SOUTH, 6)
        DARK_WARRIOR_11111(1590, 3758, 0, SOUTH, 5)
        NECROMANCER_11088(1590, 3766, 0, SOUTH, 5)
        SPIDER_3019(1591, 3721, 0, SOUTH, 8)
        DARK_WARRIOR_11110(1591, 3761, 0, SOUTH, 5)
        ZOMBIE_7485(1591, 3767, 0, SOUTH, 6)
        ZOMBIE_7487(1592, 3760, 0, SOUTH, 5)
        DARK_WARRIOR_11109(1593, 3759, 0, SOUTH, 5)
        NECROMANCER_11088(1593, 3762, 0, SOUTH, 5)
        DARK_WARRIOR_11111(1594, 3763, 0, SOUTH, 5)
        ZOMBIE_7485(1594, 3765, 0, SOUTH, 6)
    }
}