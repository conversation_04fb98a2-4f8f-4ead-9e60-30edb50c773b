package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6204Spawns : NPCSpawnsScript() {
    init {
        11142(1541, 3886, 0, SOUTH, 0)
        7896(1545, 3895, 0, SOUTH, 2)
        ICE_SPIDER_10722(1550, 3887, 0, SOUTH, 5)
        ICE_GIANT_2087(1551, 3885, 0, SOUTH, 3)
        ICE_GIANT(1552, 3881, 0, SOUTH, 3)
        ICE_GIANT(1554, 3889, 0, SOUTH, 3)
        ICE_SPIDER_10722(1555, 3884, 0, SOUTH, 5)
        ICE_GIANT_2087(1557, 3894, 0, <PERSON>O<PERSON><PERSON>, 3)
        ICE_SPIDER_10722(1558, 3890, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ICE_SPIDER_10722(1560, 3887, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ICE_GIANT_2086(1562, 3887, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        ICE_<PERSON>OLF_712(1579, 3891, 0, SOUTH, 4)
        ICE_WOLF_710(1580, 3901, 0, SOUTH, 5)
        ICE_WOLF_713(1585, 3893, 0, SOUTH, 6)
        ICE_WOLF_711(1586, 3898, 0, SOUTH, 2)
    }
}