package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6223Spawns : NPCSpawnsScript() {
    init {
        ADAMANT_DRAGON(1542, 5081, 0, SOUTH, 2)
        ADAMANT_DRAGON(1543, 5067, 0, SOUTH, 2)
        ADAMANT_DRAGON(1547, 5073, 0, SOUTH, 2)
        ADAMANT_DRAGON(1549, 5066, 0, SOUTH, 2)
        ADAMANT_DRAGON(1550, 5079, 0, SOUTH, 2)
        RUNE_DRAGON_8031(1581, 5072, 0, SOUTH, 0)
        RUNE_DRAGON_8031(1582, 5079, 0, SOUTH, 0)
        RUNE_DRAGON_8031(1584, 5066, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        RUNE_DRAGON_8031(1588, 5080, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        RUNE_DRAGON_8031(1590, 5067, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
    }
}