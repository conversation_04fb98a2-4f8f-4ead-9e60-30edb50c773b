package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6298Spawns : NPCSpawnsScript() {
    init {
        ENT_7234(1548, 9893, 0, SOUTH, 5)
        ENT_7234(1550, 9886, 0, SOUTH, 5)
        ENT_7234(1552, 9902, 0, SOUTH, 5)
        ENT_7234(1554, 9892, 0, SOUTH, 5)
        ENT_7234(1557, 9868, 0, SOUTH, 5)
        ENT_7234(1559, 9864, 0, SOUTH, 5)
        ENT_7234(1559, 9902, 0, SOUTH, 5)
        ENT_7234(1561, 9908, 0, SOUTH, 5)
        ENT_7234(1562, 9868, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ENT_7234(1568, 9900, 0, <PERSON><PERSON>UT<PERSON>, 5)
        ENT_7234(1568, 9909, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        ENT_7234(1571, 9879, 0, S<PERSON><PERSON><PERSON>, 5)
        ENT_7234(1571, 9883, 0, SOUTH, 5)
        ENT_7234(1574, 9871, 0, SOUTH, 5)
        ENT_7234(1575, 9890, 0, SOUTH, 5)
        ENT_7234(1577, 9899, 0, SOUTH, 5)
        ENT_7234(1578, 9884, 0, SOUTH, 5)
        ENT_7234(1579, 9880, 0, SOUTH, 5)
        ENT_7234(1579, 9912, 0, SOUTH, 5)
        ENT_7234(1580, 9905, 0, SOUTH, 5)
        ENT_7234(1587, 9865, 0, SOUTH, 5)
        ENT_7234(1590, 9863, 0, SOUTH, 5)
        ENT_7234(1592, 9868, 0, SOUTH, 5)
        KAI(1593, 9900, 0, SOUTH, 5)
    }
}