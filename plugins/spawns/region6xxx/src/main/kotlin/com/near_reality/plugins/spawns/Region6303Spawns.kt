package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6303Spawns : NPCSpawnsScript() {
    init {
        KRATO(1541, 10224, 0, SOUTH, 5)
        LOGIOS(1548, 10217, 0, SOUTH, 5)
        IMEROMINIA(1550, 10230, 0, SOUTH, 5)
        MELETI(1551, 10213, 0, SOUTH, 5)
        11151(1552, 10224, 0, SOUTH, 5)
        EKTHEME(1553, 10227, 0, SOUTH, 5)
        PAGIDA(1554, 10221, 0, SOUTH, 5)
    }
}