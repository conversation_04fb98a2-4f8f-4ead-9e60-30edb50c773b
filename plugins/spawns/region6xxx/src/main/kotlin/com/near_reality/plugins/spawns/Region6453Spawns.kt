package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6453Spawns : NPCSpawnsScript() {
    init {
        UNICORN(1600, 3453, 0, SOUTH, 15)
        UNICORN(1607, 3437, 0, SOUTH, 15)
        UNICORN(1611, 3447, 0, SOUTH, 15)
        UNICORN(1617, 3438, 0, SOUTH, 15)
        UNICORN(1626, 3450, 0, SOUTH, 15)
    }
}