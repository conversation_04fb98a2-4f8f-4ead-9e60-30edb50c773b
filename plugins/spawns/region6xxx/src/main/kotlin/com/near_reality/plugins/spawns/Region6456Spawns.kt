package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6456Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(1620, 3602, 0, SOUTH, 14)
        RACCOON_1421(1622, 3604, 0, SOUTH, 11)
        BUTTERFLY_235(1623, 3599, 0, SOUTH, 7)
        BUTTERFLY_1854(1625, 3593, 0, SOUTH, 5)
        BUTTERFLY_1854(1625, 3632, 0, SOUTH, 5)
        BUTTERFLY_235(1626, 3603, 0, SOUTH, 7)
        BUTTERFLY(1626, 3624, 0, SOUTH, 7)
        RACCOON(1627, 3597, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        BUTTERFLY_235(1631, 3618, 0, <PERSON>OUT<PERSON>, 7)
        BUTTERFLY_1854(1633, 3604, 0, <PERSON>OUTH, 5)
        SERVERY_ASSISTANT_6928(1636, 3620, 0, SOUTH, 5)
        SERVERY_ASSISTANT(1636, 3633, 0, SOUTH, 3)
        SQUIRREL_1418(1637, 3589, 0, SOUTH, 10)
        SOLDIER_6933(1637, 3619, 0, SOUTH, 0)
        SOLDIER_6930(1637, 3621, 0, SOUTH, 0)
        SOLDIER_6936(1637, 3632, 0, SOUTH, 0)
        SOLDIER_6929(1637, 3634, 0, SOUTH, 0)
        SOLDIER_6934(1638, 3619, 0, SOUTH, 0)
        SOLDIER_6933(1638, 3632, 0, SOUTH, 0)
        SOLDIER_6931(1638, 3634, 0, SOUTH, 0)
        SQUIRREL_1418(1639, 3592, 0, SOUTH, 10)
        SOLDIER_6935(1639, 3619, 0, SOUTH, 0)
        SOLDIER_6929(1639, 3621, 0, SOUTH, 0)
        SOLDIER_6934(1639, 3632, 0, SOUTH, 0)
        SOLDIER_6930(1639, 3634, 0, SOUTH, 0)
        SQUIRREL_1418(1640, 3590, 0, SOUTH, 10)
        SOLDIER_6932(1640, 3621, 0, SOUTH, 0)
        BUTTERFLY_1854(1640, 3639, 0, SOUTH, 5)
        RACCOON_1421(1642, 3604, 0, SOUTH, 11)
        SOLDIER_6935(1642, 3619, 0, SOUTH, 0)
        SOLDIER_6935(1642, 3632, 0, SOUTH, 0)
        SOLDIER_6932(1642, 3634, 0, SOUTH, 0)
        SOLDIER_6931(1643, 3621, 0, SOUTH, 0)
        SOLDIER_6936(1643, 3632, 0, SOUTH, 0)
        SOLDIER_6933(1644, 3619, 0, SOUTH, 0)
        SOLDIER_6929(1644, 3621, 0, SOUTH, 0)
        SOLDIER_6934(1644, 3632, 0, SOUTH, 0)
        SOLDIER_6929(1644, 3634, 0, SOUTH, 0)
        SOLDIER_6936(1645, 3619, 0, SOUTH, 0)
        SOLDIER_6930(1645, 3621, 0, SOUTH, 0)
        SOLDIER_6931(1645, 3634, 0, SOUTH, 0)
        EWESEY(1646, 3627, 0, SOUTH, 5)
        BUTTERFLY_235(1649, 3617, 0, SOUTH, 7)
        BUTTERFLY(1650, 3606, 0, SOUTH, 7)
        RAT_2854(1650, 3609, 0, SOUTH, 14)
        SQUIRREL_1418(1651, 3588, 0, SOUTH, 10)
        SQUIRREL_1418(1651, 3591, 0, SOUTH, 10)
        RAT_2854(1652, 3635, 0, SOUTH, 14)
        RACCOON_1421(1653, 3621, 0, SOUTH, 11)
        SQUIRREL_1418(1654, 3590, 0, SOUTH, 10)
        SQUIRREL_1418(1655, 3592, 0, SOUTH, 10)
        SQUIRREL_1418(1657, 3588, 0, SOUTH, 10)
        BUTTERFLY_235(1657, 3597, 0, SOUTH, 7)
        RACCOON_1421(1657, 3631, 0, SOUTH, 11)
        BUTTERFLY_1854(1661, 3614, 0, SOUTH, 5)
        BUTTERFLY(1661, 3624, 0, SOUTH, 7)
        RAT_2854(1662, 3603, 0, SOUTH, 14)
        RACCOON_1420(1662, 3619, 0, SOUTH, 12)
        CAT(1637, 3632, 1, SOUTH, 5)
        JON(1638, 3633, 1, SOUTH, 5)
    }
}