package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6457Spawns : NPCSpawnsScript() {
    init {
        GUARD_11098(1602, 3674, 0, SOUTH, 5)
        GUARD_11106(1603, 3667, 0, SOUTH, 5)
        GUARD_11092(1605, 3669, 0, SOUTH, 5)
        GUARD_11102(1605, 3677, 0, SOUTH, 5)
        11120(1606, 3656, 0, SOUTH, 5)
        11117(1612, 3669, 0, SOUTH, 5)
        IMP_5007(1616, 3704, 0, SOUTH, 100)
        ROYAL_GUARD(1619, 3678, 0, <PERSON>O<PERSON><PERSON>, 5)
        ALYSSA(1623, 3659, 0, <PERSON>O<PERSON><PERSON>, 5)
        SILK_MERCHANT(1625, 3682, 0, <PERSON>OUTH, 0)
        GUARD_11104(1627, 3654, 0, SOUTH, 5)
        HEAD_GUARD_11097(1628, 3650, 0, SOUTH, 5)
        GUARD_11094(1629, 3648, 0, SOUTH, 5)
        GUARD_11104(1630, 3682, 0, SOUTH, 5)
        GUARD_11100(1631, 3665, 0, SOUTH, 5)
        HOSA(1631, 3670, 0, SOUTH, 3)
        GUARD_11098(1632, 3648, 0, SOUTH, 5)
        GUARD_DOG_7209(1632, 3665, 0, SOUTH, 5)
        6787(1632, 3678, 0, SOUTH, 3)
        COB(1632, 3707, 0, SOUTH, 2)
        GUARD_11100(1633, 3651, 0, SOUTH, 5)
        GEM_MERCHANT(1633, 3684, 0, SOUTH, 0)
        GUARD_11098(1637, 3666, 0, SOUTH, 5)
        GUARD_11094(1637, 3679, 0, SOUTH, 5)
        6783(1638, 3668, 0, SOUTH, 3)
        BAKER(1640, 3684, 0, SOUTH, 2)
        6789(1641, 3679, 0, SOUTH, 3)
        6791(1642, 3673, 0, SOUTH, 3)
        GUARD_11102(1643, 3667, 0, SOUTH, 5)
        ELISE(1648, 3665, 0, SOUTH, 0)
        10730(1659, 3672, 0, SOUTH, 5)
        11114(1661, 3676, 0, SOUTH, 5)
        8499(1663, 3673, 0, SOUTH, 5)
        GUARD_11104(1602, 3664, 1, SOUTH, 5)
        GUARD_11092(1604, 3682, 1, SOUTH, 5)
        8716(1605, 3673, 1, SOUTH, 5)
        ROYAL_GUARD(1611, 3682, 1, SOUTH, 5)
        ROYAL_GUARD(1612, 3664, 1, SOUTH, 5)
        ROYAL_GUARD(1613, 3674, 1, SOUTH, 5)
        11138(1616, 3668, 1, SOUTH, 5)
        11130(1617, 3677, 1, SOUTH, 5)
        ROYAL_GUARD_8543(1618, 3669, 1, SOUTH, 5)
        7998(1619, 3678, 1, SOUTH, 5)
        ROYAL_GUARD_8543(1619, 3680, 1, SOUTH, 5)
        7924(1620, 3673, 1, SOUTH, 5)
        11140(1621, 3671, 1, SOUTH, 5)
        11137(1621, 3673, 1, SOUTH, 5)
        HEAD_GUARD_11103(1624, 3659, 1, SOUTH, 5)
        GUARD_11094(1634, 3660, 1, SOUTH, 5)
        GUARD_11106(1637, 3686, 1, SOUTH, 5)
        GUARD_11094(1643, 3686, 1, SOUTH, 5)
        GUARD_11098(1644, 3660, 1, SOUTH, 5)
        GUARD_11104(1649, 3686, 1, SOUTH, 5)
        GUARD_11104(1655, 3664, 1, SOUTH, 5)
        GUARD_11092(1655, 3682, 1, SOUTH, 5)
        6523(1611, 3679, 2, NORTH, 0)
        ROYAL_GUARD(1612, 3677, 2, SOUTH, 5)
        BANKER_1613(1612, 3679, 2, NORTH, 0)
        7927(1614, 3689, 2, SOUTH, 5)
        ROYAL_GUARD_8543(1615, 3664, 2, SOUTH, 5)
        DARK_WIZARD_2057(1617, 3669, 2, SOUTH, 2)
        DARK_WIZARD_2058(1618, 3666, 2, SOUTH, 3)
        LESSER_DEMON_2007(1619, 3667, 2, SOUTH, 4)
        7930(1619, 3677, 2, SOUTH, 5)
    }
}