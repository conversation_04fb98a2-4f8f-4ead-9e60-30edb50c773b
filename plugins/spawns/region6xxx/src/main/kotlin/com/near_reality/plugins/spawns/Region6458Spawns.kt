package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6458Spawns : NPCSpawnsScript() {
    init {
        DARK_WARRIOR_11111(1600, 3752, 0, SOUTH, 5)
        GUARD_11100(1601, 3714, 0, SOUTH, 5)
        RAT_2854(1601, 3723, 0, SOUTH, 14)
        DARK_WARRIOR_11109(1602, 3755, 0, SOUTH, 5)
        HEAD_GUARD_11095(1603, 3719, 0, SOUTH, 5)
        DARK_WARRIOR_11110(1604, 3751, 0, SOUTH, 5)
        GUARD_11092(1606, 3715, 0, SOUTH, 5)
        GUARD_11096(1610, 3718, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GUARD_11102(1619, 3713, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GIANT_BAT_6824(1619, 3758, 0, SOUTH, 8)
        GUARD_11106(1621, 3719, 0, SOUTH, 5)
        BANKER_7060(1622, 3740, 0, EAST, 0)
        BANKER_7058(1622, 3743, 0, EAST, 0)
        BANKER_7060(1622, 3746, 0, EAST, 0)
        BANKER_7058(1622, 3749, 0, EAST, 0)
        GUARD_11094(1624, 3714, 0, SOUTH, 5)
        GUARD_11098(1625, 3722, 0, SOUTH, 5)
        HEAD_GUARD_11107(1631, 3723, 0, SOUTH, 5)
        BANKER_7059(1637, 3740, 0, WEST, 0)
        BANKER_7057(1637, 3743, 0, WEST, 0)
        BANKER_7059(1637, 3746, 0, WEST, 0)
        BANKER_7057(1637, 3749, 0, WEST, 0)
        BAT(1644, 3741, 0, SOUTH, 23)
        BAT(1646, 3765, 0, SOUTH, 23)
        DARK_WIZARD_7065(1648, 3723, 0, SOUTH, 0)
        DARK_WIZARD_7065(1651, 3731, 0, SOUTH, 0)
        6782(1652, 3757, 0, SOUTH, 2)
        DARK_WIZARD_7065(1652, 3719, 1, SOUTH, 0)
    }
}