package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6459Spawns : NPCSpawnsScript() {
    init {
        ARCHEIO(1624, 3808, 0, SOUTH, 5)
        PROFESSOR_GRACKLEBONE(1626, 3801, 0, SOUTH, 5)
        VILLIA(1626, 3814, 0, SOUTH, 0)
        LOGOSIA(1633, 3808, 0, SOUTH, 0)
        SAM_7049(1639, 3801, 0, SOUTH, 2)
        HORPHIS(1639, 3814, 0, SOUTH, 2)
        GALANA(1652, 3826, 0, SOUTH, 4)
        WIZARD_7066(1609, 3791, 2, WEST, 0)
        DARK_WIZARD_7065(1611, 3823, 2, <PERSON><PERSON><PERSON><PERSON>, 0)
        BIBLIA(1616, 3829, 2, <PERSON><PERSON><PERSON><PERSON>, 5)
        WIZARD_7067(1646, 3820, 2, SOUTH, 2)
    }
}