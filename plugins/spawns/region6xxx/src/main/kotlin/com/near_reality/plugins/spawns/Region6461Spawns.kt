package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6461Spawns : NPCSpawnsScript() {
    init {
        WINTER_SOLDIER(1623, 3934, 0, SOUTH, 3)
        WINTER_SOLDIER(1623, 3937, 0, SOUTH, 3)
        8626(1625, 3943, 0, SOUTH, 5)
        ESTHER(1629, 3939, 0, EAST, 0)
        WINTERTOAD(1629, 3941, 0, SOUTH, 7)
        WINTER_SOLDIER(1629, 3947, 0, SOUTH, 3)
        CAPTAIN_KALT(1631, 3941, 0, SOUTH, 0)
        ISH_THE_NAVIGATOR(1633, 3939, 0, WEST, 0)
        IGNISIA(1634, 3948, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        CAT_7380(1635, 3946, 0, <PERSON>OUTH, 5)
    }
}