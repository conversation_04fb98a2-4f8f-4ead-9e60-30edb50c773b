package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6462Spawns : NPCSpawnsScript() {
    init {
        SNOW(1614, 4001, 0, SOUTH, 0)
        SNOW(1614, 4008, 0, SOUTH, 0)
        SNOW(1614, 4015, 0, SOUTH, 0)
        SNOW(1617, 3994, 0, SOUTH, 0)
        SNOW(1618, 4020, 0, SOUTH, 0)
        SNOW(1623, 4023, 0, SOUTH, 0)
        SNOW(1625, 3991, 0, SOUTH, 0)
        SNOW(1635, 3991, 0, SOUTH, 0)
        SNOW(1637, 4023, 0, SOUTH, 0)
        SNOW(1642, 4020, 0, SOUTH, 0)
        SNOW(1643, 3994, 0, SOUTH, 0)
        SNOW(1646, 4001, 0, <PERSON>OUT<PERSON>, 0)
        SNOW(1646, 4008, 0, SOUTH, 0)
        SNOW(1646, 4015, 0, SOUTH, 0)
    }
}