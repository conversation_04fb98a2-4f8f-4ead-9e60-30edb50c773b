package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6475Spawns : NPCSpawnsScript() {
    init {
        7430(1615, 4832, 0, SOUTH, 5)
        7436(1622, 4828, 0, SOUTH, 0)
        7433(1626, 4840, 0, SOUTH, 5)
        7432(1629, 4850, 0, SOUTH, 5)
        7434(1636, 4817, 0, SOUTH, 5)
        7431(1639, 4838, 0, SOUTH, 5)
        7435(1644, 4819, 0, <PERSON><PERSON>UT<PERSON>, 5)
    }
}