package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6483Spawns : NPCSpawnsScript() {
    init {
        WATERFIEND(1610, 5343, 0, SOUTH, 16)
        WATERFIEND(1610, 5348, 0, SOUTH, 16)
        WATERFIEND(1610, 5352, 0, SOUTH, 16)
        WATERFIEND(1613, 5356, 0, SOUTH, 16)
        WATERFIEND(1614, 5342, 0, SOUTH, 16)
        WATERFIEND(1614, 5361, 0, SOUTH, 16)
        ANGRY_BARBARIAN_SPIRIT(1618, 5326, 0, SOUTH, 0)
        WATERFIEND(1618, 5340, 0, <PERSON>O<PERSON><PERSON>, 16)
        WATERFIEND(1619, 5363, 0, <PERSON>O<PERSON><PERSON>, 16)
        SKELETON_BRUTE(1620, 5323, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        ENRAGED_BARBARIAN_SPIRIT(1623, 5331, 0, <PERSON>O<PERSON><PERSON>, 2)
        WATERFIEND(1623, 5342, 0, SOUTH, 16)
        WATERFIEND(1624, 5351, 0, SOUTH, 16)
        WATERFIEND(1627, 5355, 0, SOUTH, 16)
        WATERFIEND(1627, 5360, 0, SOUTH, 16)
        BRUTAL_GREEN_DRAGON(1628, 5334, 0, SOUTH, 2)
        WATERFIEND(1629, 5361, 0, SOUTH, 16)
        LOST_BARBARIAN(1633, 5322, 0, SOUTH, 0)
        BRUTAL_GREEN_DRAGON(1635, 5326, 0, SOUTH, 2)
        SKELETON_WARLORD(1635, 5355, 0, SOUTH, 2)
        WATERFIEND(1635, 5361, 0, SOUTH, 16)
        BRUTAL_GREEN_DRAGON(1637, 5332, 0, SOUTH, 2)
        WATERFIEND(1638, 5363, 0, SOUTH, 16)
        CONFUSED_BARBARIAN(1639, 5345, 0, SOUTH, 0)
        WATERFIEND(1639, 5359, 0, SOUTH, 16)
        BRUTAL_GREEN_DRAGON(1643, 5340, 0, SOUTH, 2)
        BRUTAL_GREEN_DRAGON(1645, 5358, 0, SOUTH, 2)
        BRUTAL_GREEN_DRAGON(1646, 5350, 0, SOUTH, 2)
        ENRAGED_BARBARIAN_SPIRIT(1647, 5324, 0, SOUTH, 2)
        BRUTAL_GREEN_DRAGON(1650, 5332, 0, SOUTH, 2)
        BERSERK_BARBARIAN_SPIRIT(1651, 5325, 0, SOUTH, 4)
        SKELETON_THUG(1659, 5340, 0, SOUTH, 5)
        MITHRIL_DRAGON(1627, 5326, 1, SOUTH, 2)
        MITHRIL_DRAGON(1634, 5338, 1, SOUTH, 2)
        MITHRIL_DRAGON(1638, 5331, 1, SOUTH, 2)
        MITHRIL_DRAGON(1639, 5343, 1, SOUTH, 2)
        MITHRIL_DRAGON(1652, 5339, 1, SOUTH, 2)
        MITHRIL_DRAGON(1653, 5356, 1, SOUTH, 2)
        MITHRIL_DRAGON(1655, 5328, 1, SOUTH, 2)
        SKELETON_WARLORD(1661, 5333, 1, SOUTH, 2)
    }
}