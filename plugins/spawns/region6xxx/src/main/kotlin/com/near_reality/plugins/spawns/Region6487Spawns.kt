package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6487Spawns : NPCSpawnsScript() {
    init {
        GUARD_3010(1651, 5594, 2, SOUTH, 5)
        GUARD_3010(1651, 5600, 2, SOUTH, 5)
        GUARD_3010(1651, 5606, 2, SOUTH, 5)
        GUARD_3010(1652, 5612, 2, SOUTH, 5)
        GUARD_3010(1653, 5590, 2, SOUTH, 5)
        GUARD_3010(1654, 5598, 2, SOUTH, 5)
        GUARD_3010(1654, 5601, 2, SOUTH, 5)
        GUARD_3010(1655, 5610, 2, SOUTH, 5)
        GUARD_3010(1655, 5614, 2, SOUTH, 5)
        GUARD_3010(1656, 5591, 2, <PERSON><PERSON>UTH, 5)
        GUARD_3010(1657, 5594, 2, <PERSON><PERSON>UT<PERSON>, 5)
        GUARD_3010(1657, 5599, 2, SOUTH, 5)
        GUARD_3010(1657, 5605, 2, SOUTH, 5)
    }
}