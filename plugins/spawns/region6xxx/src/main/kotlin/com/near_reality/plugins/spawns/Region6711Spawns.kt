package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6711Spawns : NPCSpawnsScript() {
    init {
        WOLF(1665, 3553, 0, SOUT<PERSON>, 6)
        WOLF(1667, 3549, 0, SOUTH, 6)
        WOLF(1669, 3556, 0, SOUTH, 6)
        SKELETON_10719(1670, 3574, 0, SOUTH, 5)
        SKELETON_10718(1671, 3570, 0, SOUTH, 5)
        WOLF(1672, 3552, 0, SOUTH, 6)
        7609(1673, 3580, 0, SOUTH, 5)
        SKELETON_10721(1674, 3573, 0, SO<PERSON><PERSON>, 5)
        SKELETON_10717(1677, 3575, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKELETON_10718(1679, 3571, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKELETON_10720(1679, 3578, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        SKELETON_10719(1680, 3568, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        KONOO(1687, 3532, 0, SOUTH, 0)
        LAMMY_LANGLE(1690, 3541, 0, SOUTH, 2)
        UNICORN(1696, 3521, 0, SOUTH, 15)
        TOOL_LEPRECHAUN(1696, 3544, 0, SOUTH, 0)
        CLERK_6923(1701, 3529, 0, SOUTH, 0)
        UNICORN_FOAL(1717, 3566, 0, SOUTH, 7)
        UNICORN_FOAL(1719, 3562, 0, SOUTH, 7)
        UNICORN_FOAL(1724, 3567, 0, SOUTH, 7)
        UNICORN(1725, 3559, 0, SOUTH, 15)
    }
}