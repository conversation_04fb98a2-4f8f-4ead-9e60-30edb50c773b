package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6712Spawns : NPCSpawnsScript() {
    init {
        BUTTERFLY_235(1666, 3601, 0, SOUTH, 7)
        CROW_2070(1666, 3631, 0, SOUTH, 4)
        DUCK_1839(1669, 3591, 0, SOUTH, 20)
        SQUIRREL_1418(1669, 3617, 0, SOUTH, 10)
        CHICKEN_1174(1669, 3636, 0, SOUTH, 4)
        CHICKEN(1669, 3637, 0, SOUTH, 2)
        CHICKEN_1174(1669, 3639, 0, SOUTH, 4)
        CHICKEN(1669, 3640, 0, <PERSON>OUT<PERSON>, 2)
        CHICKEN_1174(1670, 3637, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        CHICKEN(1670, 3639, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        DUCK(1671, 3591, 0, <PERSON><PERSON><PERSON><PERSON>, 18)
        BUTTERFLY(1671, 3613, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        BUTTERFLY(1674, 3629, 0, SOUTH, 7)
        BUTTERFLY(1674, 3640, 0, SOUTH, 7)
        SQUIRREL_1418(1675, 3607, 0, SOUTH, 10)
        RAMOCEAN(1676, 3620, 0, SOUTH, 5)
        SQUIRREL_1418(1676, 3631, 0, SOUTH, 10)
        SPIDER_3019(1677, 3627, 0, SOUTH, 8)
        MAN_6988(1678, 3609, 0, SOUTH, 4)
        PIGLET_2810(1678, 3639, 0, SOUTH, 3)
        PIG_2807(1679, 3636, 0, SOUTH, 3)
        BUTTERFLY_235(1680, 3597, 0, SOUTH, 7)
        TALIA(1681, 3617, 0, SOUTH, 5)
        PIG_2808(1681, 3635, 0, SOUTH, 2)
        PIG_2807(1681, 3638, 0, SOUTH, 3)
        PIG_2808(1682, 3638, 0, SOUTH, 2)
        BUTTERFLY(1683, 3605, 0, SOUTH, 7)
        SQUIRREL_1418(1683, 3611, 0, SOUTH, 10)
        BUTTERFLY_235(1685, 3629, 0, SOUTH, 7)
        PIG_2808(1685, 3640, 0, SOUTH, 2)
        SPIDER_3019(1686, 3630, 0, SOUTH, 8)
        PIGLET_2809(1686, 3636, 0, SOUTH, 3)
        BUTTERFLY_235(1687, 3612, 0, SOUTH, 7)
        SQUIRREL(1687, 3626, 0, SOUTH, 8)
        CROW_2071(1688, 3625, 0, SOUTH, 6)
        PIGLET_2811(1688, 3639, 0, SOUTH, 3)
        SQUIRREL_1417(1689, 3619, 0, SOUTH, 9)
        WOMAN_6990(1690, 3603, 0, SOUTH, 5)
        CROW_2072(1690, 3615, 0, SOUTH, 5)
        BUTTERFLY_235(1691, 3599, 0, SOUTH, 7)
        FOX(1691, 3623, 0, SOUTH, 3)
        CROW(1691, 3639, 0, SOUTH, 5)
        DUCK_1839(1692, 3593, 0, SOUTH, 20)
        RABBIT_3664(1692, 3641, 0, SOUTH, 5)
        DUCK(1694, 3591, 0, SOUTH, 18)
        BUTTERFLY(1694, 3635, 0, SOUTH, 7)
        SQUIRREL_1418(1696, 3630, 0, SOUTH, 10)
        SQUIRREL(1697, 3606, 0, SOUTH, 8)
        CROW_2074(1697, 3607, 0, SOUTH, 5)
        BUTTERFLY_237(1698, 3603, 0, SOUTH, 7)
        BUTTERFLY(1698, 3609, 0, SOUTH, 7)
        BUTTERFLY_235(1699, 3605, 0, SOUTH, 7)
        SQUIRREL(1699, 3630, 0, SOUTH, 8)
        FOX(1700, 3609, 0, SOUTH, 3)
        CROW_2072(1700, 3611, 0, SOUTH, 5)
        BUTTERFLY_235(1701, 3596, 0, SOUTH, 7)
        CROW_2071(1701, 3631, 0, SOUTH, 6)
        SQUIRREL_1417(1702, 3618, 0, SOUTH, 9)
        SPIDER_3019(1703, 3614, 0, SOUTH, 8)
        BUTTERFLY_235(1703, 3622, 0, SOUTH, 7)
        DUCK(1703, 3639, 0, SOUTH, 18)
        RABBIT_3664(1705, 3612, 0, SOUTH, 5)
        DUCK(1705, 3641, 0, SOUTH, 18)
        DUCK_1839(1706, 3588, 0, SOUTH, 20)
        SQUIRREL_1417(1707, 3610, 0, SOUTH, 9)
        SQUIRREL(1707, 3617, 0, SOUTH, 8)
        DUCK(1707, 3632, 0, SOUTH, 18)
        DUCK(1713, 3602, 0, SOUTH, 18)
        ROD_FISHING_SPOT_7468(1714, 3607, 0, SOUTH, 0)
        ROD_FISHING_SPOT_7468(1715, 3612, 0, SOUTH, 0)
        DUCK(1715, 3638, 0, SOUTH, 18)
        DUCK_1839(1718, 3608, 0, SOUTH, 20)
        DUCK(1718, 3613, 0, SOUTH, 18)
        MAN_6987(1719, 3594, 0, SOUTH, 4)
        MAN_6989(1670, 3599, 1, SOUTH, 5)
        WOMAN_6992(1671, 3600, 1, SOUTH, 3)
    }
}