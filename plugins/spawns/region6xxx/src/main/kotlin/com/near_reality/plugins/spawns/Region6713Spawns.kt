package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6713Spawns : NPCSpawnsScript() {
    init {
        8187(1665, 3674, 0, SOUT<PERSON>, 2)
        WOMAN_6990(1666, 3661, 0, SOUT<PERSON>, 5)
        8498(1666, 3667, 0, SOUTH, 5)
        8580(1666, 3671, 0, SOUTH, 0)
        10715(1666, 3673, 0, SOUTH, 5)
        8506(1666, 3678, 0, SOUTH, 5)
        MAN_6988(1667, 3656, 0, SOUTH, 4)
        8186(1667, 3669, 0, SOUTH, 2)
        8185(1667, 3676, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        MAN_6989(1667, 3685, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        8184(1668, 3673, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        10729(1668, 3675, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        WOMAN_6992(1669, 3650, 0, <PERSON><PERSON><PERSON><PERSON>, 3)
        10716(1670, 3672, 0, SOUTH, 5)
        10714(1670, 3674, 0, SOUTH, 5)
        11115(1673, 3676, 0, SOUTH, 5)
        11116(1674, 3671, 0, SOUTH, 5)
        WOMAN_10728(1674, 3674, 0, SOUTH, 5)
        10930(1676, 3683, 0, SOUTH, 5)
        MAN_6987(1676, 3696, 0, SOUTH, 4)
        11118(1677, 3674, 0, SOUTH, 5)
        MAN_6988(1682, 3673, 0, SOUTH, 4)
        MAN_6987(1684, 3650, 0, SOUTH, 4)
        MAN_6989(1686, 3658, 0, SOUTH, 5)
        GUARD_11104(1686, 3676, 0, SOUTH, 5)
        GUARD_11098(1687, 3671, 0, SOUTH, 5)
        WOMAN_6992(1687, 3690, 0, SOUTH, 3)
        HEAD_GUARD_11107(1689, 3675, 0, SOUTH, 5)
        GUARD_11092(1691, 3673, 0, SOUTH, 5)
        GUARD_11102(1694, 3672, 0, SOUTH, 5)
        GUARD_11094(1694, 3675, 0, SOUTH, 5)
        IMP_5007(1696, 3683, 0, SOUTH, 100)
        SQUIRREL_1417(1697, 3655, 0, SOUTH, 9)
        RESHI(1698, 3685, 0, SOUTH, 5)
        RABBIT_3664(1700, 3664, 0, SOUTH, 5)
        KENDALL(1700, 3688, 0, SOUTH, 5)
        THOMDRIL(1701, 3684, 0, SOUTH, 5)
        MAN_6989(1702, 3656, 0, SOUTH, 5)
        RABBIT_3664(1703, 3651, 0, SOUTH, 5)
        IMP_5007(1703, 3688, 0, SOUTH, 100)
        GIANT_BAT_6824(1705, 3701, 0, SOUTH, 8)
        WOMAN_6991(1706, 3662, 0, SOUTH, 5)
        WOMAN_6990(1709, 3655, 0, SOUTH, 5)
        BAT(1709, 3709, 0, SOUTH, 23)
        BAT(1710, 3706, 0, SOUTH, 23)
        SQUIRREL_1417(1712, 3662, 0, SOUTH, 9)
        SQUIRREL_1417(1713, 3655, 0, SOUTH, 9)
        MAN_6988(1713, 3660, 0, SOUTH, 4)
        BAT(1714, 3703, 0, SOUTH, 23)
        RABBIT_3664(1717, 3664, 0, SOUTH, 5)
        RABBIT_3664(1718, 3653, 0, SOUTH, 5)
        SQUIRREL_1417(1719, 3649, 0, SOUTH, 9)
        ROD_FISHING_SPOT(1720, 3683, 0, SOUTH, 5)
        ROD_FISHING_SPOT(1721, 3686, 0, SOUTH, 5)
        ROD_FISHING_SPOT(1724, 3685, 0, SOUTH, 5)
        6946(1664, 3669, 0, SOUTH, 0)
        7017(1664, 3670, 0, SOUTH, 5)
        GUARD_11092(1669, 3700, 2, SOUTH, 5)
        GUARD_11092(1674, 3699, 2, SOUTH, 5)
        GUARD_11092(1678, 3700, 2, SOUTH, 5)
        LESSER_DEMON(1680, 3678, 2, SOUTH, 4)
        GUARD_11092(1683, 3699, 2, SOUTH, 5)
        GUARD_11092(1688, 3700, 2, SOUTH, 5)
        GUARD_11092(1690, 3649, 2, SOUTH, 5)
        GUARD_11092(1690, 3659, 2, SOUTH, 5)
        GUARD_11092(1690, 3667, 2, SOUTH, 5)
        GUARD_11092(1690, 3675, 2, SOUTH, 5)
        GUARD_11092(1690, 3687, 2, SOUTH, 5)
        GUARD_11092(1690, 3693, 2, SOUTH, 5)
        GUARD_11092(1691, 3653, 2, SOUTH, 5)
        GUARD_11092(1691, 3662, 2, SOUTH, 5)
        GUARD_11092(1691, 3672, 2, SOUTH, 5)
        GUARD_11092(1691, 3682, 2, SOUTH, 5)
        GUARD_11092(1691, 3697, 2, SOUTH, 5)
    }
}