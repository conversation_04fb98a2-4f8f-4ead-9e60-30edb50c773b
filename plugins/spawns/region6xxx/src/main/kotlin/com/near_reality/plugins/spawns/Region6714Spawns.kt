package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6714Spawns : NPCSpawnsScript() {
    init {
        BAT(1667, 3735, 0, SOUTH, 23)
        FILAMINA(1668, 3728, 0, SOUTH, 2)
        GIANT_BAT_6824(1678, 3757, 0, SOUTH, 8)
        DARK_WIZARD_7064(1678, 3766, 0, SOUTH, 0)
        WIZARD_7067(1682, 3769, 0, SOUTH, 2)
        RASSAIN(1686, 3725, 0, SOUTH, 2)
        OUDITOR(1686, 3764, 0, SOUTH, 4)
        WIZARD_7066(1686, 3766, 0, WEST, 0)
        7208(1698, 3742, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        FREALD(1702, 3770, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        BATT_MELLAMY(1703, 3771, 0, <PERSON>OUTH, 0)
        GIANT_BAT_6824(1708, 3715, 0, SOUTH, 8)
        BAT(1709, 3742, 0, SOUTH, 23)
        REGATH(1720, 3724, 0, SOUTH, 2)
        THYRIA(1722, 3745, 0, SOUTH, 2)
    }
}