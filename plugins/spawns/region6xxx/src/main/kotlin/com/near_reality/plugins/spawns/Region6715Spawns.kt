package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6715Spawns : NPCSpawnsScript() {
    init {
        BAT(1670, 3797, 0, SOUT<PERSON>, 23)
        7231(1670, 3834, 0, SOUTH, 0)
        ENOCH(1712, 3829, 0, WEST, 0)
        BAT(1718, 3824, 0, SOUTH, 23)
        NOVICE(1687, 3784, 1, SOUTH, 5)
        NOVICE(1687, 3803, 1, SOUTH, 5)
        NOVICE(1709, 3783, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        MOFINA(1692, 3793, 2, SOUTH, 5)
        7375(1706, 3799, 2, SOUT<PERSON>, 2)
    }
}