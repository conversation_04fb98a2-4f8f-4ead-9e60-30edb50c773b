package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6716Spawns : NPCSpawnsScript() {
    init {
        GIANT_BAT_6824(1673, 3861, 0, SOUTH, 8)
        BAT(1675, 3877, 0, SOUTH, 23)
        BAT(1678, 3859, 0, SOUTH, 23)
        SKELETON_74(1686, 3880, 0, SOUTH, 3)
        BAT(1691, 3855, 0, SOUTH, 23)
        SKELETON_75(1692, 3878, 0, SOUTH, 3)
        GIANT_BAT_6824(1700, 3857, 0, SOUTH, 8)
        BAT(1701, 3862, 0, SOUTH, 23)
        SKELETON_76(1703, 3880, 0, SOUTH, 3)
        GIANT_BAT_6824(1709, 3860, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        TYSS(1713, 3883, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        BAT(1714, 3888, 0, <PERSON>O<PERSON><PERSON>, 23)
        SKELETON_75(1716, 3878, 0, SOUTH, 3)
        BAT(1721, 3855, 0, SOUTH, 23)
        BAT(1724, 3890, 0, SOUTH, 23)
    }
}