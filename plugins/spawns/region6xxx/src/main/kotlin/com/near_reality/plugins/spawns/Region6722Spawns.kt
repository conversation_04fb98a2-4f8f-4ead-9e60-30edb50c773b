package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6722Spawns : NPCSpawnsScript() {
    init {
        RENEGADE_KNIGHT_4321(1692, 4252, 0, SOUTH, 5)
        SIR_BEDIVERE_4345(1692, 4256, 0, SOUTH, 5)
        RENEGADE_KNIGHT_4321(1693, 4254, 0, SOUTH, 5)
        SIR_KAY_4349(1695, 4257, 0, SOUTH, 5)
        SIR_GAWAIN_4348(1698, 4257, 0, SOUTH, 5)
        RENEGADE_KNIGHT_4321(1699, 4254, 0, SO<PERSON><PERSON>, 5)
        SIR_PELLEAS_4347(1692, 4258, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        RENEGADE_KNIGHT_4321(1692, 4261, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        RENEGADE_KNIGHT_4321(1693, 4255, 1, SOUTH, 5)
        SIR_TRISTRAM_4346(1694, 4251, 1, SOUTH, 5)
        SIR_LANCELOT_4344(1694, 4257, 1, SOUTH, 5)
        RENEGADE_KNIGHT_4321(1696, 4251, 1, SOUTH, 5)
        RENEGADE_KNIGHT_4321(1697, 4257, 1, SOUTH, 5)
        SIR_LUCAN_4342(1698, 4256, 1, SOUTH, 5)
        RENEGADE_KNIGHT_4321(1700, 4260, 1, SOUTH, 5)
        SIR_MORDRED(1695, 4258, 2, SOUTH, 5)
    }
}