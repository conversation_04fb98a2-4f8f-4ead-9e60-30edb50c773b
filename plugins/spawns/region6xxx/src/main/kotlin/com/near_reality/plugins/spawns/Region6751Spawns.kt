package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6751Spawns : NPCSpawnsScript() {
    init {
        MASTER_NAVIGATOR(1667, 6095, 0, SOUTH, 5)
        BUTTERFLY(1674, 6085, 0, SOUT<PERSON>, 7)
        QUEST_GUIDE_9480(1677, 6130, 0, SOUTH, 5)
        BUTTERFLY(1684, 6090, 0, SOUTH, 7)
        BUTTERFLY_236(1685, 6094, 0, SOUTH, 9)
        GIELINOR_GUIDE(1686, 6115, 0, SOUTH, 5)
        BUTTERFLY_236(1689, 6093, 0, SOUT<PERSON>, 9)
        FISHING_SPOT_9478(1691, 6098, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        FISHING_SPOT_9478(1693, 6100, 0, SOUTH, 5)
        FISHING_SPOT_9478(1695, 6100, 0, SOUTH, 5)
        SURVIVAL_EXPERT_9477(1695, 6103, 0, SOUTH, 5)
        BUTTERFLY(1701, 6098, 0, SOUTH, 7)
        BUTTERFLY(1704, 6101, 0, SOUTH, 7)
        BANKER_9484(1712, 6133, 0, SOUTH, 5)
        BANKER_9484(1714, 6133, 0, SOUTH, 5)
        BUTTERFLY_235(1717, 6092, 0, SOUTH, 7)
        BROTHER_BRACE_9485(1717, 6114, 0, SOUTH, 5)
        BUTTERFLY_236(1721, 6099, 0, SOUTH, 9)
        IRON_MAN_TUTOR_9486(1722, 6096, 0, SOUTH, 5)
        BUTTERFLY_236(1727, 6092, 0, SOUTH, 9)
    }
}