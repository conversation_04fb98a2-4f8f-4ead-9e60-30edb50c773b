package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6809Spawns : NPCSpawnsScript() {
    init {
        SANDY_ROCKS_7207(1667, 9832, 0, SOUTH, 0)
        SANDY_ROCKS(1667, 9833, 0, SOUTH, 0)
        SANDY_ROCKS(1669, 9825, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1669, 9832, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1670, 9826, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1671, 9823, 0, SOUTH, 0)
        SANDY_ROCKS(1672, 9822, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1672, 9829, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        SANDY_ROCKS(1673, 9831, 0, <PERSON>OUTH, 0)
        SANDY_ROCKS(1675, 9826, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1678, 9821, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1678, 9823, 0, SOUTH, 0)
        SANDY_ROCKS(1680, 9821, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1680, 9822, 0, SOUTH, 0)
        SANDY_BOULDER(1690, 9823, 0, SOUTH, 2)
        SANDY_BOULDER(1694, 9824, 0, SOUTH, 2)
        SANDY_BOULDER(1697, 9819, 0, SOUTH, 2)
        SANDY_BOULDER(1699, 9823, 0, SOUTH, 2)
        SANDY_BOULDER(1708, 9844, 0, SOUTH, 2)
        SANDY_BOULDER(1713, 9840, 0, SOUTH, 2)
        SANDY_BOULDER(1713, 9846, 0, SOUTH, 2)
        SANDY_BOULDER(1718, 9842, 0, SOUTH, 2)
    }
}