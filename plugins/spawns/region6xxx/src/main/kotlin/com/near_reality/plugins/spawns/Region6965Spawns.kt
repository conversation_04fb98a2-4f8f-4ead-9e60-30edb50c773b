package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6965Spawns : NPCSpawnsScript() {
    init {
        SANDY_ROCKS(1748, 3412, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1749, 3411, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1749, 3413, 0, SOUTH, 0)
        SANDY_ROCKS(1750, 3413, 0, SOUTH, 0)
        SANDY_ROCKS(1750, 3425, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1751, 3424, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1751, 3426, 0, SOUTH, 0)
        SANDY_ROCKS(1752, 3410, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1752, 3425, 0, <PERSON>O<PERSON><PERSON>, 0)
        SANDY_ROCKS(1754, 3429, 0, <PERSON><PERSON><PERSON><PERSON>, 0)
        SANDY_ROCKS_7207(1756, 3410, 0, <PERSON>OUTH, 0)
        SANDY_ROCKS_7207(1757, 3439, 0, SOUTH, 0)
        SANDY_ROCKS(1758, 3440, 0, SOUTH, 0)
        SANDY_ROCKS(1759, 3439, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1761, 3412, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1763, 3426, 0, SOUTH, 0)
        SANDY_ROCKS(1763, 3429, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1763, 3445, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1764, 3444, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1764, 3446, 0, SOUTH, 0)
        SANDY_ROCKS(1765, 3446, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1766, 3427, 0, SOUTH, 0)
        SANDY_ROCKS(1767, 3409, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1768, 3408, 0, SOUTH, 0)
        SANDY_ROCKS(1769, 3409, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1769, 3447, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1774, 3412, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1776, 3429, 0, SOUTH, 0)
        SANDY_ROCKS(1777, 3432, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1778, 3429, 0, SOUTH, 0)
        SANDY_ROCKS(1779, 3407, 0, SOUTH, 0)
        SANDICRAHB_7484(1779, 3418, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1779, 3438, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1780, 3406, 0, SOUTH, 0)
        SANDY_ROCKS(1780, 3408, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1780, 3412, 0, SOUTH, 0)
        SANDY_ROCKS(1780, 3427, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1780, 3437, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1780, 3439, 0, SOUTH, 0)
        SANDY_ROCKS(1781, 3408, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1781, 3432, 0, SOUTH, 0)
        SANDY_ROCKS(1781, 3438, 0, SOUTH, 0)
        SANDY_ROCKS(1783, 3412, 0, SOUTH, 0)
        SANDY_ROCKS(1785, 3404, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1786, 3403, 0, SOUTH, 0)
        SANDY_ROCKS_7207(1787, 3404, 0, SOUTH, 0)
        SANDY_ROCKS(1787, 3405, 0, SOUTH, 0)
        SANDY_ROCKS(1787, 3409, 0, SOUTH, 0)
    }
}