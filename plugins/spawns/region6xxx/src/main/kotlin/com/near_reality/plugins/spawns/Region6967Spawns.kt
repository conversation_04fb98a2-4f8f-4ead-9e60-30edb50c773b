package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6967Spawns : NPCSpawnsScript() {
    init {
        FATHER_JEAN(1734, 3576, 0, SOUTH, 4)
        MARISI(1736, 3556, 0, SOUTH, 2)
        TOOL_LEPRECHAUN(1741, 3552, 0, SOUTH, 0)
        GUARD_398(1743, 3565, 0, SOUTH, 5)
        GUARD_399(1744, 3564, 0, SOUTH, 5)
        BARBARA(1747, 3538, 0, SOUTH, 5)
        CHIEF_FARMER(1749, 3534, 0, SOUTH, 4)
        SPIDER_3019(1751, 3533, 0, SOUTH, 8)
        GUARD_400(1758, 3565, 0, SOUT<PERSON>, 5)
        GUARD(1760, 3563, 0, SOUTH, 5)
        PLOUGH(1763, 3523, 0, EAST, 0)
        PLOUGH(1764, 3543, 0, EAST, 0)
        PLOUGH(1764, 3551, 0, EAST, 0)
        PLOUGH(1765, 3529, 0, EAST, 0)
        PLOUGH(1767, 3535, 0, EAST, 0)
        PLOUGH(1770, 3545, 0, EAST, 0)
        PLOUGH(1770, 3553, 0, EAST, 0)
        PLOUGH(1771, 3523, 0, EAST, 0)
        PLOUGH(1773, 3529, 0, EAST, 0)
        PLOUGH(1775, 3535, 0, EAST, 0)
        PLOUGH(1776, 3547, 0, EAST, 0)
        PLOUGH(1776, 3555, 0, EAST, 0)
        CHEF_OLIVIA(1776, 3566, 0, SOUTH, 2)
        7925(1776, 3577, 0, SOUTH, 2)
        11143(1782, 3571, 0, SOUTH, 5)
        11139(1782, 3572, 0, SOUTH, 5)
        BUTLER_JARVIS(1780, 3566, 1, SOUTH, 5)
        11144(1779, 3567, 2, SOUTH, 5)
    }
}