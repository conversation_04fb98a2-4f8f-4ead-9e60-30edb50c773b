package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6968Spawns : NPCSpawnsScript() {
    init {
        MAN_6989(1728, 3619, 0, SOUTH, 5)
        WOMAN_6990(1729, 3615, 0, SOUTH, 5)
        WOMAN_6992(1730, 3612, 0, SOUTH, 3)
        RAT_2854(1731, 3616, 0, SOUTH, 14)
        MAN_6988(1731, 3618, 0, SOUTH, 4)
        RAT_2854(1733, 3622, 0, SOUTH, 14)
        GUARD_400(1738, 3592, 0, SOUTH, 5)
        SPIDER_3019(1738, 3611, 0, SOUTH, 8)
        GUARD_DOG_7209(1739, 3600, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        RAT_2854(1739, 3612, 0, <PERSON><PERSON><PERSON><PERSON>, 14)
        FARMER_6952(1739, 3619, 0, SOUTH, 2)
        FARMER_6948(1740, 3620, 0, SOUTH, 2)
        FARMER_HAYFIELD(1741, 3588, 0, SOUTH, 0)
        GUARD_398(1741, 3591, 0, SOUTH, 5)
        RICHARD_6954(1741, 3612, 0, SOUTH, 2)
        GUARD_DOG_7209(1742, 3595, 0, SOUTH, 5)
        MAN_6987(1742, 3616, 0, SOUTH, 4)
        GOLOVA(1743, 3612, 0, SOUTH, 2)
        RAT_2854(1743, 3618, 0, SOUTH, 14)
        WOMAN_6991(1744, 3615, 0, SOUTH, 5)
        BANKER_6942(1746, 3598, 0, EAST, 0)
        BANKER_6940(1746, 3599, 0, EAST, 0)
        BANKER_6942(1746, 3600, 0, EAST, 0)
        FARMER_6950(1746, 3613, 0, SOUTH, 2)
        FARMER_6951(1747, 3618, 0, SOUTH, 2)
        COW_CALF(1747, 3637, 0, SOUTH, 9)
        COW(1747, 3640, 0, SOUTH, 3)
        FARMER_6947(1748, 3614, 0, SOUTH, 0)
        FARMER_6949(1748, 3618, 0, SOUTH, 2)
        JONATHAN(1750, 3587, 0, SOUTH, 5)
        COW_2791(1751, 3636, 0, SOUTH, 4)
        COW_CALF_2794(1754, 3637, 0, SOUTH, 3)
        TOWN_CRIER_6823(1756, 3599, 0, SOUTH, 5)
        GUARD_DOG_7209(1756, 3619, 0, SOUTH, 5)
        COW(1757, 3638, 0, SOUTH, 3)
        COW_2791(1758, 3643, 0, SOUTH, 4)
        GUARD_DOG_7209(1760, 3596, 0, SOUTH, 5)
        MASTER_FARMER(1761, 3633, 0, SOUTH, 5)
        COW_CALF(1761, 3642, 0, SOUTH, 9)
        VANNAH(1763, 3593, 0, SOUTH, 4)
        COW_2793(1763, 3638, 0, SOUTH, 7)
        MAN_6988(1765, 3596, 0, SOUTH, 4)
        GUARD_DOG_7209(1765, 3601, 0, SOUTH, 5)
        1172(1765, 3643, 0, SOUTH, 0)
        WOMAN_6992(1767, 3603, 0, SOUTH, 3)
        MARIAH(1767, 3622, 0, SOUTH, 5)
        LOGAVA(1768, 3598, 0, SOUTH, 2)
        GUARD(1770, 3633, 0, SOUTH, 5)
        GUARD_399(1772, 3634, 0, SOUTH, 5)
        HORACE(1773, 3588, 0, SOUTH, 5)
        GUARD_DOG_7209(1778, 3597, 0, SOUTH, 5)
        WOMAN_6991(1778, 3619, 0, SOUTH, 5)
        ESTATE_AGENT(1781, 3628, 0, SOUTH, 2)
        10431(1782, 3624, 0, SOUTH, 5)
        LEELA_10423(1784, 3590, 0, SOUTH, 5)
        GUARD_398(1786, 3598, 0, SOUTH, 5)
        MASTER_FARMER_5731(1787, 3592, 0, SOUTH, 5)
        GIANT_RAT_2864(1787, 3641, 0, SOUTH, 6)
        GUARD_399(1788, 3596, 0, SOUTH, 5)
        GIANT_RAT_2863(1788, 3634, 0, SOUTH, 6)
    }
}