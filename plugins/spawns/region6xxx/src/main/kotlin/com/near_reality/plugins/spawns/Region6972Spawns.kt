package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6972Spawns : NPCSpawnsScript() {
    init {
        SKELETON_74(1729, 3889, 0, SOUTH, 3)
        BAT(1732, 3840, 0, SOUTH, 23)
        GIANT_BAT_6824(1732, 3855, 0, SOUTH, 8)
        SKELETON_80(1734, 3876, 0, SOUTH, 8)
        BAT(1737, 3851, 0, SOUTH, 23)
        GIANT_BAT_6824(1743, 3892, 0, SOUTH, 8)
        BAT(1750, 3897, 0, SOUTH, 23)
        BAT(1755, 3876, 0, SOUTH, 23)
        CLERRIS(1762, 3852, 0, SOUTH, 2)
        BAT(1768, 3858, 0, SOUTH, 23)
        GHOST_97(1774, 3896, 0, <PERSON><PERSON>UTH, 2)
        GHOST_93(1790, 3892, 0, SOUTH, 3)
    }
}