package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6991Spawns : NPCSpawnsScript() {
    init {
        ETHEREAL_GUIDE(1734, 5111, 2, SOUTH, 5)
        ETHEREAL_FLUKE(1737, 5068, 2, SOUTH, 5)
        ETHEREAL_LADY(1761, 5087, 2, SOUTH, 5)
        ETHEREAL_MAN(1761, 5088, 2, SOUTH, 5)
        ETHEREAL_PERCEPTIVE(1765, 5112, 2, SOUTH, 5)
        ETHEREAL_MIMIC(1770, 5070, 2, SOUTH, 5)
        ETHEREAL_NUMERATOR(1786, 5066, 2, SOUTH, 5)
        ETHEREAL_EXPERT(1787, 5079, 2, <PERSON><PERSON><PERSON><PERSON>, 5)
    }
}