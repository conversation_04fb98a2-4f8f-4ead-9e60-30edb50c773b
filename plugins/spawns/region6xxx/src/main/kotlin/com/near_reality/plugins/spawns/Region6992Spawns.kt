package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6992Spawns : NPCSpawnsScript() {
    init {
        BABY_MOLE(1737, 5156, 0, SOUTH, 4)
        BABY_MOLE(1739, 5177, 0, SOUTH, 4)
        BABY_MOLE_5782(1742, 5163, 0, SOUTH, 12)
        BABY_MOLE_5782(1746, 5152, 0, SOUTH, 12)
        BABY_MOLE_5782(1746, 5171, 0, SOUTH, 12)
        BABY_MOLE_5781(1756, 5181, 0, SOUTH, 4)
        BABY_MOLE_5781(1760, 5152, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BABY_MOLE_5782(1762, 5182, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        BABY_MOLE(1765, 5178, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BABY_MOLE_5782(1770, 5174, 0, <PERSON>O<PERSON><PERSON>, 12)
        BABY_M<PERSON>E_5781(1780, 5180, 0, SOUTH, 4)
        BABY_MOLE_5781(1781, 5152, 0, SOUTH, 4)
        BABY_MOLE(1756, 5164, 0, SOUTH, 4)
        BABY_MOLE(1761, 5161, 0, SOUTH, 4)
        BABY_MOLE_5782(1764, 5164, 0, SOUTH, 12)
        BABY_MOLE_5781(1779, 5165, 0, SOUTH, 4)
    }
}