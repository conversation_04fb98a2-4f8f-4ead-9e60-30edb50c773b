package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6993Spawns : NPCSpawnsScript() {
    init {
        BABY_MOLE_5781(1734, 5202, 0, SOUTH, 4)
        BABY_MOLE(1736, 5227, 0, SOUTH, 4)
        BABY_MOLE_5782(1737, 5209, 0, SOUTH, 12)
        BABY_MOLE(1741, 5188, 0, SOUTH, 4)
        BABY_MOLE_5781(1746, 5221, 0, SOUTH, 4)
        BABY_MOLE_5781(1755, 5199, 0, SOUTH, 4)
        GIANT_MOLE(1759, 5189, 0, SOUT<PERSON>, 64)
        BABY_MOLE_5782(1760, 5215, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        BABY_MOLE_5782(1763, 5196, 0, <PERSON><PERSON><PERSON><PERSON>, 12)
        BABY_MOLE_5781(1770, 5226, 0, <PERSON><PERSON>UT<PERSON>, 4)
        BABY_MOLE(1772, 5200, 0, SOUTH, 4)
        BABY_MOLE(1776, 5241, 0, SOUTH, 4)
        BABY_MOLE_5782(1778, 5232, 0, SOUTH, 12)
        BABY_MOLE_5782(1779, 5210, 0, SOUTH, 12)
        BABY_MOLE_5781(1783, 5191, 0, SOUTH, 4)
    }
}