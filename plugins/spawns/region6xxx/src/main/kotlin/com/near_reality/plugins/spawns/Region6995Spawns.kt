package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region6995Spawns : NPCSpawnsScript() {
    init {
        WATERFIEND(1738, 5343, 0, SOUTH, 16)
        WATERFIEND(1738, 5348, 0, SOUTH, 16)
        WATERFIEND(1738, 5352, 0, SOUTH, 16)
        WATERFIEND(1741, 5356, 0, SOUTH, 16)
        WATERFIEND(1742, 5342, 0, SOUTH, 16)
        WATERFIEND(1742, 5361, 0, SOUTH, 16)
        ANGRY_BARBARIAN_SPIRIT(1746, 5326, 0, SOUTH, 0)
        WATERFIEND(1746, 5340, 0, <PERSON>OUT<PERSON>, 16)
        WATERFIEND(1747, 5363, 0, <PERSON>O<PERSON><PERSON>, 16)
        SKELETON_BRUTE(1748, 5323, 0, <PERSON>OUTH, 2)
        ENRAGED_BARBARIAN_SPIRIT(1751, 5331, 0, SOUTH, 2)
        WATERFIEND(1751, 5342, 0, SOUTH, 16)
        WATERFIEND(1752, 5351, 0, SOUTH, 16)
        WATERFIEND(1755, 5355, 0, SOUTH, 16)
        WATERFIEND(1755, 5360, 0, SOUTH, 16)
        BRUTAL_GREEN_DRAGON(1756, 5334, 0, SOUTH, 2)
        WATERFIEND(1757, 5361, 0, SOUTH, 16)
        LOST_BARBARIAN(1761, 5322, 0, SOUTH, 0)
        BRUTAL_GREEN_DRAGON(1763, 5326, 0, SOUTH, 2)
        SKELETON_WARLORD(1763, 5355, 0, SOUTH, 2)
        WATERFIEND(1763, 5361, 0, SOUTH, 16)
        BRUTAL_GREEN_DRAGON(1765, 5332, 0, SOUTH, 2)
        WATERFIEND(1766, 5363, 0, SOUTH, 16)
        CONFUSED_BARBARIAN(1767, 5345, 0, SOUTH, 0)
        WATERFIEND(1767, 5359, 0, SOUTH, 16)
        BRUTAL_GREEN_DRAGON(1771, 5340, 0, SOUTH, 2)
        BRUTAL_GREEN_DRAGON(1773, 5358, 0, SOUTH, 2)
        BRUTAL_GREEN_DRAGON(1774, 5350, 0, SOUTH, 2)
        ENRAGED_BARBARIAN_SPIRIT(1775, 5324, 0, SOUTH, 2)
        BRUTAL_GREEN_DRAGON(1778, 5332, 0, SOUTH, 2)
        BERSERK_BARBARIAN_SPIRIT(1779, 5325, 0, SOUTH, 4)
        SKELETON_THUG(1787, 5340, 0, SOUTH, 5)
        MITHRIL_DRAGON(1755, 5326, 1, SOUTH, 2)
        MITHRIL_DRAGON(1762, 5338, 1, SOUTH, 2)
        MITHRIL_DRAGON(1766, 5331, 1, SOUTH, 2)
        MITHRIL_DRAGON(1767, 5343, 1, SOUTH, 2)
        MITHRIL_DRAGON(1780, 5339, 1, SOUTH, 2)
        MITHRIL_DRAGON(1781, 5356, 1, SOUTH, 2)
        MITHRIL_DRAGON(1783, 5328, 1, SOUTH, 2)
        SKELETON_WARLORD(1789, 5333, 1, SOUTH, 2)
    }
}