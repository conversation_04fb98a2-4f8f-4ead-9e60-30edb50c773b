package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8519Spawns : NPCSpawnsScript() {
    init {
        9217(2122, 4562, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        9215(2131, 4602, 1, SOUTH, 5)
        9219(2132, 4554, 1, SOUTH, 5)
        9218(2134, 4565, 1, SOUTH, 5)
        GIANT_BAT_4562(2150, 4591, 1, <PERSON>OUTH, 5)
        4560(2153, 4546, 1, <PERSON><PERSON>UTH, 5)
        9214(2155, 4566, 1, <PERSON>OUT<PERSON>, 5)
    }
}