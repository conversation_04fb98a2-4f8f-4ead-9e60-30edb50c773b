package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8520Spawns : NPCSpawnsScript() {
    init {
        9221(2133, 4647, 1, SOUTH, 5)
        9220(2149, 4646, 1, SOUTH, 5)
        9220(2150, 4648, 1, SOUTH, 5)
        9220(2153, 4646, 1, SOUTH, 5)
        9220(2153, 4649, 1, SOUTH, 5)
        9220(2156, 4646, 1, SOUTH, 5)
        9220(2157, 4649, 1, <PERSON>OUTH, 5)
        9220(2159, 4635, 1, SOUTH, 5)
        9220(2159, 4642, 1, SOUTH, 5)
        9220(2159, 4646, 1, <PERSON>OUTH, 5)
        9220(2159, 4650, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        9220(2160, 4662, 1, <PERSON>O<PERSON><PERSON>, 5)
        9220(2163, 4653, 1, <PERSON><PERSON><PERSON><PERSON>, 5)
        9220(2163, 4660, 1, SOUTH, 5)
    }
}