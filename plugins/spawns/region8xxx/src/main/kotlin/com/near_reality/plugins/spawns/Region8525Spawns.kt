package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8525Spawns : NPCSpawnsScript() {
    init {
        GNOME_SOLDIER(2146, 4965, 0, SOUTH, 5)
        GOBLIN_4906(2147, 4956, 0, SOUTH, 5)
        GNOME_SOLDIER_4910(2147, 4966, 0, SOUTH, 5)
        GOBLIN_4904(2148, 4956, 0, SOUTH, 5)
        HEALTHORG_AND_TORTOISE(2148, 4961, 0, SOUTH, 0)
        GOBLIN_4902(2149, 4957, 0, SOUTH, 5)
        GLOUPHRIE_THE_UNTRUSTED(2149, 4960, 0, <PERSON>O<PERSON><PERSON>, 5)
        GOBLIN_4903(2150, 4956, 0, <PERSON>O<PERSON><PERSON>, 5)
        GNOME_SOLDIER_4911(2150, 4964, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GOBLIN_4905(2151, 4956, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GNOME_<PERSON><PERSON>DIER_4908(2151, 4966, 0, SOUTH, 5)
        GNOME_SOLDIER_4909(2152, 4965, 0, SOUTH, 5)
    }
}