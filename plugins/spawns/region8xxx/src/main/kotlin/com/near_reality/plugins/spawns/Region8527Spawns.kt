package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8527Spawns : NPCSpawnsScript() {
    init {
        FISHING_SPOT(2113, 5074, 0, SOUTH, 0)
        _50_LUKE(2120, 5096, 0, NORTH, 0)
        FEVER_SPIDER(2140, 5093, 0, SOUTH, 8)
        ZOMBIE_PIRATE_613(2141, 5083, 0, SOUTH, 9)
        ZOMBIE_PIRATE_614(2142, 5081, 0, SOUTH, 9)
        FEVER_SPIDER(2142, 5097, 0, SOUTH, 8)
        FEVER_SPIDER(2142, 5103, 0, SOUTH, 8)
        ZOMBIE_PIRATE_618(2143, 5078, 0, <PERSON>O<PERSON><PERSON>, 8)
        FEVER_SPIDER(2143, 5094, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        ZOMBIE_SWAB_623(2144, 5071, 0, <PERSON>OUTH, 3)
        ZOMBIE_P<PERSON>ATE_615(2145, 5080, 0, SOUTH, 8)
        ZOMBIE_PROTESTER_612(2145, 5086, 0, SOUTH, 3)
        ZOMBIE_PROTESTER(2145, 5087, 0, SOUTH, 3)
        FEVER_SPIDER(2146, 5098, 0, SOUTH, 8)
        ZOMBIE_SWAB_620(2147, 5071, 0, SOUTH, 9)
        ZOMBIE_PROTESTER_608(2147, 5085, 0, SOUTH, 5)
        ZOMBIE_PIRATE_617(2148, 5078, 0, SOUTH, 7)
        ZOMBIE_PIRATE_616(2148, 5080, 0, SOUTH, 8)
        ZOMBIE_PROTESTER_611(2148, 5087, 0, SOUTH, 3)
        FEVER_SPIDER(2148, 5103, 0, SOUTH, 8)
        CAPTAIN_DONNIE(2150, 5078, 0, SOUTH, 4)
        ZOMBIE_PROTESTER_610(2150, 5085, 0, SOUTH, 5)
        ZOMBIE_PROTESTER_609(2150, 5086, 0, SOUTH, 6)
        FEVER_SPIDER(2151, 5094, 0, SOUTH, 8)
        ZOMBIE_SWAB(2152, 5073, 0, SOUTH, 7)
        ZOMBIE_SWAB_621(2153, 5071, 0, SOUTH, 2)
        ZOMBIE_PROTESTER_611(2153, 5084, 0, SOUTH, 3)
        ZOMBIE_PROTESTER_610(2153, 5086, 0, SOUTH, 5)
        ZOMBIE_PROTESTER_609(2154, 5085, 0, SOUTH, 6)
        FEVER_SPIDER(2154, 5101, 0, SOUTH, 8)
        ZOMBIE_PIRATE_613(2155, 5080, 0, SOUTH, 9)
        ZOMBIE_PIRATE_614(2156, 5078, 0, SOUTH, 9)
        ZOMBIE_PROTESTER_612(2157, 5084, 0, SOUTH, 3)
        FEVER_SPIDER(2157, 5098, 0, SOUTH, 8)
        ZOMBIE_SWAB_622(2158, 5071, 0, SOUTH, 2)
        ZOMBIE_PIRATE_618(2158, 5076, 0, SOUTH, 8)
        ZOMBIE_PIRATE_615(2158, 5079, 0, SOUTH, 8)
        ZOMBIE_PROTESTER_608(2158, 5083, 0, SOUTH, 5)
        ZOMBIE_PROTESTER(2159, 5086, 0, SOUTH, 3)
        FEVER_SPIDER(2159, 5104, 0, SOUTH, 8)
        ZOMBIE_PIRATE_616(2160, 5078, 0, SOUTH, 8)
        FEVER_SPIDER(2160, 5094, 0, SOUTH, 8)
        ZOMBIE_SWAB_624(2161, 5071, 0, SOUTH, 3)
        FISHING_SPOT(2162, 5061, 0, SOUTH, 0)
        ZOMBIE_PIRATE_617(2162, 5079, 0, SOUTH, 7)
        FISHING_SPOT(2173, 5074, 0, SOUTH, 0)
        DAVEY(2132, 5100, 1, SOUTH, 0)
        CAPTAIN_BRAINDEATH(2144, 5109, 1, SOUTH, 0)
        BREWER_634(2148, 5096, 1, SOUTH, 5)
        BREWER(2149, 5093, 1, SOUTH, 5)
        BREWER_628(2151, 5094, 1, SOUTH, 5)
        BREWER_633(2152, 5096, 1, SOUTH, 5)
        BREWER_629(2154, 5094, 1, SOUTH, 5)
        BREWER_630(2158, 5094, 1, SOUTH, 4)
        BREWER_631(2158, 5096, 1, SOUTH, 4)
        BREWER_632(2160, 5094, 1, SOUTH, 4)
        PIRATE_PETE_602(2162, 5115, 1, SOUTH, 0)
    }
}