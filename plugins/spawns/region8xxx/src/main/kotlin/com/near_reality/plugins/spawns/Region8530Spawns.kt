package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8530Spawns : NPCSpawnsScript() {
    init {
        SPIDER(2119, 5274, 0, SOUTH, 7)
        CATABLEPON_2476(2119, 5296, 0, SOUT<PERSON>, 2)
        GIANT_SPIDER(2120, 5275, 0, SOUTH, 7)
        SPIDER(2120, 5277, 0, SOUTH, 7)
        CATABLEPON_2476(2120, 5292, 0, SOUTH, 2)
        CATABLEPON_2476(2120, 5299, 0, SOUTH, 2)
        GIANT_SPIDER(2121, 5273, 0, SOUTH, 7)
        SPIDER(2121, 5276, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        SPIDER(2122, 5271, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        CATABLEPON_2476(2122, 5291, 0, <PERSON><PERSON><PERSON><PERSON>, 2)
        GIANT_SPIDER(2123, 5270, 0, <PERSON>OUT<PERSON>, 7)
        GIANT_SPIDER(2123, 5275, 0, SOUTH, 7)
        CATABLEPON_2476(2123, 5302, 0, SOUTH, 2)
        SPIDER(2124, 5269, 0, SOUTH, 7)
        GIANT_SPIDER(2124, 5272, 0, SOUTH, 7)
        SPIDER(2124, 5273, 0, SOUTH, 7)
        GIANT_SPIDER(2124, 5274, 0, SOUTH, 7)
        GIANT_SPIDER(2125, 5270, 0, SOUTH, 7)
        SPIDER(2126, 5270, 0, SOUTH, 7)
        SPIDER(2126, 5272, 0, SOUTH, 7)
        GIANT_SPIDER(2126, 5274, 0, SOUTH, 7)
        SPIDER(2127, 5269, 0, SOUTH, 7)
        GIANT_SPIDER(2127, 5272, 0, SOUTH, 7)
        SPIDER(2127, 5273, 0, SOUTH, 7)
        CATABLEPON_2476(2127, 5305, 0, SOUTH, 2)
        SPIDER(2128, 5271, 0, SOUTH, 7)
        SPIDER(2129, 5267, 0, SOUTH, 7)
        GIANT_SPIDER(2129, 5268, 0, SOUTH, 7)
        GIANT_SPIDER(2129, 5270, 0, SOUTH, 7)
        SPIDER(2129, 5273, 0, SOUTH, 7)
        CATABLEPON_2476(2129, 5302, 0, SOUTH, 2)
        GIANT_SPIDER(2130, 5272, 0, SOUTH, 7)
        CATABLEPON_2476(2130, 5298, 0, SOUTH, 2)
        GIANT_SPIDER(2131, 5267, 0, SOUTH, 7)
        CATABLEPON(2142, 5252, 0, SOUTH, 4)
        GIANT_SPIDER(2142, 5256, 0, SOUTH, 7)
        GIANT_SPIDER(2143, 5251, 0, SOUTH, 7)
        CATABLEPON(2145, 5252, 0, SOUTH, 4)
        GIANT_SPIDER(2145, 5253, 0, SOUTH, 7)
        CATABLEPON(2145, 5256, 0, SOUTH, 4)
        GIANT_SPIDER(2145, 5306, 0, SOUTH, 7)
        GIANT_SPIDER(2146, 5259, 0, SOUTH, 7)
        GIANT_SPIDER(2146, 5306, 0, SOUTH, 7)
        GIANT_SPIDER(2147, 5255, 0, SOUTH, 7)
        GIANT_SPIDER(2147, 5305, 0, SOUTH, 7)
        GIANT_SPIDER(2147, 5307, 0, SOUTH, 7)
        CATABLEPON(2148, 5253, 0, SOUTH, 4)
        CATABLEPON(2149, 5255, 0, SOUTH, 4)
        GIANT_SPIDER(2149, 5304, 0, SOUTH, 7)
        GIANT_SPIDER(2149, 5305, 0, SOUTH, 7)
        GIANT_SPIDER(2149, 5306, 0, SOUTH, 7)
        SPIDER(2150, 5267, 0, SOUTH, 7)
        GIANT_SPIDER(2150, 5307, 0, SOUTH, 7)
        GIANT_SPIDER(2150, 5308, 0, SOUTH, 7)
        GIANT_SPIDER(2151, 5268, 0, SOUTH, 7)
        GIANT_SPIDER(2151, 5305, 0, SOUTH, 7)
        GIANT_SPIDER(2151, 5308, 0, SOUTH, 7)
        CATABLEPON(2152, 5251, 0, SOUTH, 4)
        GIANT_SPIDER(2152, 5268, 0, SOUTH, 7)
        SPIDER(2152, 5270, 0, SOUTH, 7)
        GIANT_SPIDER(2152, 5306, 0, SOUTH, 7)
        CATABLEPON(2153, 5254, 0, SOUTH, 4)
        GIANT_SPIDER(2153, 5270, 0, SOUTH, 7)
        GIANT_SPIDER(2153, 5305, 0, SOUTH, 7)
        GIANT_SPIDER(2153, 5306, 0, SOUTH, 7)
        GIANT_SPIDER(2153, 5307, 0, SOUTH, 7)
        GIANT_SPIDER(2154, 5269, 0, SOUTH, 7)
        GIANT_SPIDER(2154, 5271, 0, SOUTH, 7)
        GIANT_SPIDER(2154, 5273, 0, SOUTH, 7)
        GIANT_SPIDER(2154, 5305, 0, SOUTH, 7)
        CATABLEPON(2155, 5252, 0, SOUTH, 4)
        GIANT_SPIDER(2155, 5254, 0, SOUTH, 7)
        SPIDER(2155, 5270, 0, SOUTH, 7)
        SPIDER(2155, 5271, 0, SOUTH, 7)
        GIANT_SPIDER(2155, 5305, 0, SOUTH, 7)
        SPIDER(2156, 5273, 0, SOUTH, 7)
        SPIDER(2156, 5274, 0, SOUTH, 7)
        GIANT_SPIDER(2157, 5271, 0, SOUTH, 7)
        GIANT_SPIDER(2157, 5273, 0, SOUTH, 7)
        GIANT_SPIDER(2157, 5274, 0, SOUTH, 7)
        CATABLEPON_2475(2158, 5282, 0, SOUTH, 3)
        CATABLEPON_2475(2161, 5281, 0, SOUTH, 3)
        CATABLEPON_2475(2162, 5284, 0, SOUTH, 3)
        CATABLEPON_2476(2163, 5301, 0, SOUTH, 2)
        CATABLEPON_2475(2164, 5280, 0, SOUTH, 3)
        SPIDER(2165, 5255, 0, SOUTH, 7)
        SPIDER(2166, 5256, 0, SOUTH, 7)
        CATABLEPON_2476(2166, 5303, 0, SOUTH, 2)
        CATABLEPON_2476(2166, 5306, 0, SOUTH, 2)
        SPIDER(2167, 5251, 0, SOUTH, 7)
        SPIDER(2167, 5253, 0, SOUTH, 7)
        SPIDER(2167, 5255, 0, SOUTH, 7)
        CATABLEPON_2476(2167, 5300, 0, SOUTH, 2)
        SPIDER(2168, 5253, 0, SOUTH, 7)
        SPIDER(2168, 5256, 0, SOUTH, 7)
        SPIDER(2168, 5257, 0, SOUTH, 7)
        SPIDER(2169, 5250, 0, SOUTH, 7)
        SPIDER(2169, 5254, 0, SOUTH, 7)
        SPIDER(2169, 5255, 0, SOUTH, 7)
        SCORPION(2169, 5290, 0, SOUTH, 4)
        CATABLEPON_2476(2169, 5303, 0, SOUTH, 2)
        SPIDER(2170, 5252, 0, SOUTH, 7)
        SPIDER(2170, 5254, 0, SOUTH, 7)
        SPIDER(2170, 5256, 0, SOUTH, 7)
        SCORPION_2480(2170, 5277, 0, SOUTH, 6)
        SCORPION(2170, 5285, 0, SOUTH, 4)
        SCORPION_2480(2170, 5288, 0, SOUTH, 6)
        CATABLEPON_2476(2170, 5306, 0, SOUTH, 2)
        SPIDER(2171, 5253, 0, SOUTH, 7)
        SCORPION(2171, 5280, 0, SOUTH, 4)
        SPIDER(2172, 5254, 0, SOUTH, 7)
        SCORPION(2172, 5274, 0, SOUTH, 4)
        SCORPION_2480(2172, 5283, 0, SOUTH, 6)
        SCORPION_2480(2173, 5272, 0, SOUTH, 6)
    }
}