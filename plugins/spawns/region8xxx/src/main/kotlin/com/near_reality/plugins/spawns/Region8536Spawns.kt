package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8536Spawns : NPCSpawnsScript() {
    init {
        DEMONIC_GORILLA(2123, 5676, 0, SOUTH, 11)
        DEMONIC_GORILLA_7146(2130, 5671, 0, SOUTH, 8)
        TORTURED_GORILLA_7150(2131, 5644, 0, SOUTH, 9)
        DEMONIC_GORILLA_7147(2131, 5680, 0, SOUTH, 5)
        TORTURED_GORILLA_7151(2136, 5646, 0, SOUTH, 8)
        DEMONIC_GORILLA(2142, 5678, 0, SO<PERSON><PERSON>, 11)
        TORTURED_GORILLA_7150(2145, 5643, 0, <PERSON><PERSON><PERSON><PERSON>, 9)
        TORTURED_GORILLA_7151(2146, 5650, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        TORTURED_GORILLA_7150(2151, 5649, 0, SOUTH, 9)
        DEMONIC_GORILLA_7149(2152, 5658, 0, SOUTH, 5)
        DEMONIC_GORILLA_7146(2152, 5668, 0, SOUTH, 8)
        DEMONIC_GORILLA_7147(2152, 5678, 0, SOUTH, 5)
        DEMONIC_GORILLA_7146(2157, 5658, 0, SOUTH, 8)
        DEMONIC_GORILLA(2112, 5651, 0, SOUTH, 11)
        DEMONIC_GORILLA_7145(2119, 5660, 0, SOUTH, 22)
        TORTURED_GORILLA_7151(2132, 5660, 0, SOUTH, 8)
        TORTURED_GORILLA_7150(2137, 5660, 0, SOUTH, 9)
        TORTURED_GORILLA_7151(2141, 5661, 0, SOUTH, 8)
    }
}