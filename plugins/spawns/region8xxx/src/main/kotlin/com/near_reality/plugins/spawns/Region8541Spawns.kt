package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8541Spawns : NPCSpawnsScript() {
    init {
        BIRD_10541(2117, 5975, 0, SOUTH, 5)
        SQUIRREL(2121, 5963, 0, SOUTH, 8)
        BIRD_10541(2126, 6013, 0, SOUTH, 5)
        10543(2128, 5997, 0, SOUTH, 5)
        SQUIRREL_1418(2130, 6007, 0, SOUTH, 10)
        BUTTERFLY_238(2134, 5966, 0, SOUTH, 0)
        SQUIRREL_1417(2139, 5983, 0, SOUTH, 9)
        BUTTERFLY_238(2141, 5992, 0, SOUTH, 0)
    }
}