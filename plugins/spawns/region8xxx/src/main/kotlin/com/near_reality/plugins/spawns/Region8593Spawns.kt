package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8593Spawns : NPCSpawnsScript() {
    init {
        FIRE_GIANT_2080(2116, 9320, 0, SOUTH, 4)
        FIRE_GIANT_2078(2116, 9323, 0, SOUTH, 6)
        FIRE_GIANT_2079(2116, 9330, 0, SOUTH, 2)
        FIRE_GIANT_2080(2119, 9327, 0, SOUTH, 4)
        BLUE_DRAGON_268(2120, 9305, 0, SOUTH, 4)
        FIRE_GIANT_2078(2120, 9323, 0, SOUTH, 6)
        FIRE_GIANT_2078(2120, 9331, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        BLUE_DRAGON(2122, 9300, 0, <PERSON>O<PERSON><PERSON>, 3)
        FIRE_GIANT_2080(2123, 9330, 0, <PERSON><PERSON>UTH, 4)
        FIRE_GIANT_2079(2124, 9326, 0, <PERSON>OUT<PERSON>, 2)
        BABY_BLUE_DRAGON(2125, 9306, 0, SOUTH, 4)
        BABY_BLUE_DRAGON_242(2126, 9303, 0, SOUTH, 4)
        BLUE_DRAGON_268(2127, 9299, 0, SOUTH, 4)
        BLUE_DRAGON_266(2129, 9304, 0, SOUTH, 4)
        BABY_BLUE_DRAGON_242(2131, 9298, 0, SOUTH, 4)
        SKELETON_80(2131, 9322, 0, SOUTH, 8)
        IRON_DRAGON(2148, 9293, 0, SOUTH, 7)
        LESSER_DEMON(2149, 9337, 0, SOUTH, 4)
        SKELETON_77(2150, 9298, 0, SOUTH, 7)
        SKELETON_79(2151, 9316, 0, SOUTH, 8)
        LESSER_DEMON_2007(2152, 9332, 0, SOUTH, 4)
        IRON_DRAGON(2153, 9290, 0, SOUTH, 7)
        SKELETON_78(2153, 9299, 0, SOUTH, 8)
        LESSER_DEMON_2018(2155, 9335, 0, SOUTH, 8)
        LESSER_DEMON_2006(2156, 9330, 0, SOUTH, 6)
        GREATER_DEMON_2026(2159, 9336, 0, SOUTH, 4)
        IRON_DRAGON(2160, 9294, 0, SOUTH, 7)
        GREATER_DEMON(2161, 9330, 0, SOUTH, 2)
        GREATER_DEMON_2027(2163, 9337, 0, SOUTH, 2)
        GREATER_DEMON_2027(2165, 9326, 0, SOUTH, 2)
        GREATER_DEMON(2165, 9333, 0, SOUTH, 2)
        GREATER_DEMON_2029(2167, 9323, 0, SOUTH, 7)
        GREATER_DEMON_2028(2167, 9330, 0, SOUTH, 4)
    }
}