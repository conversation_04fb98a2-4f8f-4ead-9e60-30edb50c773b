package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8747Spawns : NPCSpawnsScript() {
    init {
        BUTTERFLY_238(2180, 2798, 0, SOUTH, 0)
        SQUIRREL(2184, 2782, 0, SOUTH, 8)
        RAT_2854(2193, 2789, 0, SOUTH, 14)
        RAT_2854(2194, 2797, 0, SOUTH, 14)
        RAT_2854(2204, 2791, 0, SOUTH, 14)
        BUTTERFLY_238(2209, 2812, 0, SOUTH, 0)
        RAT_2854(2216, 2787, 0, SOUTH, 14)
        BUTTERFLY_238(2217, 2780, 0, SOUTH, 0)
        SQUIRREL_1418(2227, 2805, 0, <PERSON><PERSON><PERSON><PERSON>, 10)
        MOSS_GIANT_2091(2227, 2815, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        BIRD_10541(2234, 2802, 0, <PERSON>OUTH, 5)
    }
}