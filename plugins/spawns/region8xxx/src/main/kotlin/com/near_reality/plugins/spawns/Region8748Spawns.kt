package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8748Spawns : NPCSpawnsScript() {
    init {
        DUCK_10547(2188, 2840, 0, SOUTH, 5)
        DUCK_10546(2194, 2835, 0, SOUTH, 5)
        DUCK_10546(2197, 2848, 0, SOUTH, 5)
        ZIMBERFIZZ_10530(2208, 2858, 0, SOUTH, 5)
        10555(2212, 2857, 0, SOUTH, 5)
        10527(2215, 2860, 0, SOUTH, 0)
        MOSS_GIANT_2092(2222, 2817, 0, SOUTH, 3)
        MOSS_GIANT(2223, 2821, 0, <PERSON>O<PERSON><PERSON>, 4)
        MOSS_GIANT_2093(2226, 2819, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        MOSS_GIANT_2092(2230, 2821, 0, <PERSON>OUT<PERSON>, 3)
        MOSS_GIANT_2093(2232, 2816, 0, SOUTH, 4)
    }
}