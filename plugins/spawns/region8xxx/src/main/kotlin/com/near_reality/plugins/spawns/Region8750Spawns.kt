package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8750Spawns : NPCSpawnsScript() {
    init {
        SQUIRREL_1418(2179, 2983, 0, SOUTH, 10)
        BIRD_10541(2181, 2984, 0, SOUTH, 5)
        BUTTERFLY_238(2182, 2979, 0, SOUTH, 0)
        BIRD_10541(2188, 2995, 0, SOUTH, 5)
        BIRD_10541(2192, 2986, 0, SOUTH, 5)
        SQUIRREL_1417(2193, 2961, 0, SOUTH, 9)
        COPPER_LONGTAIL(2203, 2963, 0, SOUT<PERSON>, 7)
        SQUIRREL(2203, 2993, 0, <PERSON><PERSON><PERSON><PERSON>, 8)
        COPPER_LONGTAIL(2205, 2967, 0, <PERSON><PERSON><PERSON><PERSON>, 7)
        COPPER_LONGTAIL(2207, 2961, 0, SOUTH, 7)
        COPPER_LONGTAIL(2207, 2964, 0, SOUTH, 7)
        UNICORN(2208, 2992, 0, SOUTH, 15)
        COPPER_LONGTAIL(2209, 2965, 0, SOUTH, 7)
        COPPER_LONGTAIL(2210, 2960, 0, SOUTH, 7)
        BUTTERFLY_238(2211, 2963, 0, SOUTH, 0)
        COPPER_LONGTAIL(2212, 2964, 0, SOUTH, 7)
        BIRD_10541(2215, 2995, 0, SOUTH, 5)
        BIRD_10541(2228, 2950, 0, SOUTH, 5)
        SQUIRREL_1418(2233, 2949, 0, SOUTH, 10)
    }
}