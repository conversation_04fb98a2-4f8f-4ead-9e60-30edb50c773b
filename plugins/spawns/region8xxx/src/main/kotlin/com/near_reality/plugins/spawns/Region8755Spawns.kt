package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8755Spawns : NPCSpawnsScript() {
    init {
        RABBIT_3420(2182, 3281, 0, SOUTH, 4)
        RABBIT_3421(2183, 3277, 0, SOUTH, 3)
        RABBIT_3420(2183, 3279, 0, SOUTH, 4)
        RABBIT_3420(2184, 3276, 0, SOUTH, 4)
        RABBIT_3422(2184, 3280, 0, SOUTH, 3)
        RABBIT_3420(2185, 3279, 0, SOUTH, 4)
        RABBIT_3422(2186, 3280, 0, SOUTH, 3)
        RABBIT_3420(2186, 3281, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        RABBIT_3420(2188, 3277, 0, <PERSON><PERSON><PERSON><PERSON>, 4)
        9242(2238, 3268, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
    }
}