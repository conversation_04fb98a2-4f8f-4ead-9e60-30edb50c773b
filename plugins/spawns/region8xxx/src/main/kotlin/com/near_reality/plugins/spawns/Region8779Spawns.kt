package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8779Spawns : NPCSpawnsScript() {
    init {
        GHOST(2188, 4855, 0, SOUTH, 5)
        GHOST_92(2190, 4820, 0, SOUTH, 6)
        GHOST_86(2192, 4852, 0, SOUTH, 5)
        GHOST(2193, 4845, 0, SOUTH, 5)
        GHOST_91(2194, 4824, 0, SOUTH, 6)
        GHOST_87(2197, 4849, 0, SOUTH, 2)
        GHOST_90(2198, 4825, 0, SOUTH, 8)
        GHOST_89(2206, 4824, 0, SOUTH, 4)
        GHOST_88(2210, 4824, 0, SOUTH, 5)
        GHOST(2216, 4829, 0, <PERSON>OUTH, 5)
        GHOST_86(2218, 4818, 0, <PERSON><PERSON><PERSON><PERSON>, 5)
        GHOST_92(2218, 4837, 0, <PERSON><PERSON><PERSON><PERSON>, 6)
        GHOST_88(2218, 4843, 0, SOUTH, 5)
        GHOST_89(2223, 4848, 0, SOUTH, 4)
        GHOST_90(2225, 4850, 0, SOUTH, 8)
        GHOST_91(2226, 4855, 0, SOUTH, 6)
        GHOST_87(2227, 4819, 0, SOUTH, 2)
    }
}